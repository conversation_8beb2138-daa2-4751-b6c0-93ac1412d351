"""
自动媒体库集成模块
基于配置的自动文件集成和元数据管理
"""

import logging
import json
import asyncio
from typing import Dict, List, Any, Optional
from pathlib import Path
from datetime import datetime
import hashlib

logger = logging.getLogger(__name__)


class AutoMediaIntegration:
    """自动媒体库集成器"""
    
    def __init__(self):
        self.services_config = {}
        self.media_integration_rules = {}
        self._load_config()
    
    def _load_config(self):
        """加载配置"""
        try:
            config_path = Path("config/unified_services.json")
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    self.services_config = json.load(f)
                self._build_integration_rules()
                logger.info("✅ 自动媒体集成配置加载成功")
            else:
                logger.warning("⚠️ 统一服务配置文件不存在")
        except Exception as e:
            logger.error(f"❌ 加载媒体集成配置失败: {e}")
    
    def _build_integration_rules(self):
        """构建集成规则"""
        services = self.services_config.get("services", {})
        
        for service_name, service_config in services.items():
            media_integration = service_config.get("media_integration", {})
            
            for capability, integration_config in media_integration.items():
                key = f"{service_name}_{capability}"
                self.media_integration_rules[key] = {
                    "service_name": service_name,
                    "capability": capability,
                    "category": integration_config.get("category", "ai_generated"),
                    "file_naming": integration_config.get("file_naming", "{service}_{capability}_{timestamp}"),
                    "metadata_fields": integration_config.get("metadata_fields", []),
                    "auto_tags": integration_config.get("auto_tags", []),
                    "thumbnail_generation": integration_config.get("thumbnail_generation", False)
                }
    
    async def auto_add_to_media_library(self, file_path: str, service_name: str, 
                                       capability: str, metadata: Dict[str, Any] = None):
        """自动添加文件到媒体库"""
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                logger.warning(f"文件不存在，跳过媒体库集成: {file_path}")
                return False
            
            # 获取集成规则
            rule_key = f"{service_name}_{capability}"
            integration_rule = self.media_integration_rules.get(rule_key)
            
            if not integration_rule:
                logger.warning(f"未找到集成规则: {rule_key}")
                return False
            
            # 构建媒体项目数据
            media_item = await self._build_media_item(file_path, integration_rule, metadata)
            
            # 添加到媒体库
            success = await self._add_to_media_library_api(media_item)
            
            if success:
                logger.info(f"✅ 文件已自动添加到媒体库: {file_path.name}")
                
                # 生成缩略图（如果需要）
                if integration_rule.get("thumbnail_generation"):
                    await self._generate_thumbnail(file_path, media_item)
                
                return True
            else:
                logger.error(f"❌ 添加到媒体库失败: {file_path.name}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 自动媒体库集成异常: {e}")
            return False
    
    async def _build_media_item(self, file_path: Path, integration_rule: Dict[str, Any], 
                               metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """构建媒体项目数据"""
        metadata = metadata or {}
        
        # 基础文件信息
        file_stats = file_path.stat()
        file_hash = await self._calculate_file_hash(file_path)
        
        # 确定媒体类型
        media_type = self._determine_media_type(file_path)
        
        # 生成标准化文件名（如果需要）
        new_file_name = self._generate_file_name(file_path, integration_rule, metadata)
        
        # 构建媒体项目
        media_item = {
            "file_path": str(file_path),
            "file_name": new_file_name or file_path.name,
            "original_name": file_path.name,
            "file_size": file_stats.st_size,
            "file_hash": file_hash,
            "media_type": media_type,
            "category": integration_rule["category"],
            "created_at": datetime.now().isoformat(),
            "modified_at": datetime.fromtimestamp(file_stats.st_mtime).isoformat(),
            "ai_generated": True,
            "service_provider": integration_rule["service_name"],
            "generation_capability": integration_rule["capability"],
            "tags": self._generate_tags(integration_rule, metadata),
            "metadata": self._extract_metadata(integration_rule, metadata)
        }
        
        return media_item
    
    def _determine_media_type(self, file_path: Path) -> str:
        """确定媒体类型"""
        suffix = file_path.suffix.lower()
        
        if suffix in ['.mp3', '.wav', '.ogg', '.m4a', '.flac']:
            return 'audio'
        elif suffix in ['.mp4', '.avi', '.mov', '.mkv', '.webm']:
            return 'video'
        elif suffix in ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp']:
            return 'image'
        elif suffix in ['.txt', '.md', '.json', '.xml']:
            return 'text'
        else:
            return 'other'
    
    def _generate_file_name(self, file_path: Path, integration_rule: Dict[str, Any], 
                           metadata: Dict[str, Any]) -> Optional[str]:
        """生成标准化文件名"""
        try:
            naming_template = integration_rule.get("file_naming")
            if not naming_template:
                return None
            
            # 准备替换变量
            timestamp = int(datetime.now().timestamp())
            variables = {
                "service": integration_rule["service_name"],
                "capability": integration_rule["capability"],
                "timestamp": timestamp,
                "original_name": file_path.stem,
                "extension": file_path.suffix
            }
            
            # 添加元数据变量
            for key, value in metadata.items():
                if isinstance(value, (str, int, float)):
                    variables[key] = str(value)
            
            # 替换模板变量
            new_name = naming_template.format(**variables)
            
            # 确保有扩展名
            if not new_name.endswith(file_path.suffix):
                new_name += file_path.suffix
            
            return new_name
            
        except Exception as e:
            logger.warning(f"生成文件名失败: {e}")
            return None
    
    def _generate_tags(self, integration_rule: Dict[str, Any], metadata: Dict[str, Any]) -> List[str]:
        """生成标签"""
        tags = []
        
        # 添加自动标签
        auto_tags = integration_rule.get("auto_tags", [])
        tags.extend(auto_tags)
        
        # 添加服务相关标签
        tags.append(f"service:{integration_rule['service_name']}")
        tags.append(f"capability:{integration_rule['capability']}")
        tags.append("ai_generated")
        
        # 从元数据中提取标签
        if "style" in metadata:
            tags.append(f"style:{metadata['style']}")
        
        if "model" in metadata or "model_id" in metadata:
            model = metadata.get("model") or metadata.get("model_id")
            tags.append(f"model:{model}")
        
        if "voice_id" in metadata:
            tags.append(f"voice:{metadata['voice_id']}")
        
        # 去重并返回
        return list(set(tags))
    
    def _extract_metadata(self, integration_rule: Dict[str, Any], metadata: Dict[str, Any]) -> Dict[str, Any]:
        """提取元数据"""
        extracted = {}
        
        # 提取指定的元数据字段
        metadata_fields = integration_rule.get("metadata_fields", [])
        for field in metadata_fields:
            if field in metadata:
                extracted[field] = metadata[field]
        
        # 添加生成信息
        extracted.update({
            "generation_time": datetime.now().isoformat(),
            "service_provider": integration_rule["service_name"],
            "generation_capability": integration_rule["capability"],
            "integration_version": "3.0"
        })
        
        return extracted
    
    async def _calculate_file_hash(self, file_path: Path) -> str:
        """计算文件哈希"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.warning(f"计算文件哈希失败: {e}")
            return ""
    
    async def _add_to_media_library_api(self, media_item: Dict[str, Any]) -> bool:
        """通过API添加到媒体库"""
        try:
            import aiohttp
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    "http://127.0.0.1:8000/api/media-library/items",
                    json=media_item,
                    headers={'Content-Type': 'application/json'}
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result.get("success", False)
                    else:
                        logger.error(f"媒体库API调用失败，状态码: {response.status}")
                        return False
                        
        except Exception as e:
            logger.error(f"调用媒体库API异常: {e}")
            return False
    
    async def _generate_thumbnail(self, file_path: Path, media_item: Dict[str, Any]):
        """生成缩略图"""
        try:
            media_type = media_item.get("media_type")
            
            if media_type == "image":
                await self._generate_image_thumbnail(file_path)
            elif media_type == "video":
                await self._generate_video_thumbnail(file_path)
            else:
                logger.info(f"媒体类型 {media_type} 不支持缩略图生成")
                
        except Exception as e:
            logger.warning(f"生成缩略图失败: {e}")
    
    async def _generate_image_thumbnail(self, file_path: Path):
        """生成图像缩略图"""
        try:
            from PIL import Image
            
            thumbnail_path = file_path.parent / f"{file_path.stem}_thumb.jpg"
            
            with Image.open(file_path) as img:
                img.thumbnail((200, 200), Image.Resampling.LANCZOS)
                img.save(thumbnail_path, "JPEG", quality=85)
                
            logger.info(f"✅ 图像缩略图生成成功: {thumbnail_path.name}")
            
        except ImportError:
            logger.warning("PIL库未安装，无法生成图像缩略图")
        except Exception as e:
            logger.warning(f"生成图像缩略图失败: {e}")
    
    async def _generate_video_thumbnail(self, file_path: Path):
        """生成视频缩略图"""
        try:
            import subprocess
            
            thumbnail_path = file_path.parent / f"{file_path.stem}_thumb.jpg"
            
            # 使用ffmpeg生成视频缩略图
            cmd = [
                "ffmpeg", "-i", str(file_path),
                "-ss", "00:00:01", "-vframes", "1",
                "-vf", "scale=200:200:force_original_aspect_ratio=decrease",
                "-y", str(thumbnail_path)
            ]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            await process.communicate()
            
            if process.returncode == 0:
                logger.info(f"✅ 视频缩略图生成成功: {thumbnail_path.name}")
            else:
                logger.warning("ffmpeg生成视频缩略图失败")
                
        except FileNotFoundError:
            logger.warning("ffmpeg未安装，无法生成视频缩略图")
        except Exception as e:
            logger.warning(f"生成视频缩略图失败: {e}")
    
    def reload_config(self):
        """重新加载配置"""
        logger.info("🔄 重新加载媒体集成配置...")
        self.media_integration_rules.clear()
        self._load_config()
        logger.info("✅ 媒体集成配置重新加载完成")


# 全局自动媒体集成器实例
auto_media_integration = AutoMediaIntegration()


# 便捷函数
async def auto_add_to_media_library(file_path: str, service_name: str = None, 
                                   capability: str = None, metadata: Dict[str, Any] = None):
    """便捷的自动媒体库集成函数"""
    if not service_name or not capability:
        # 尝试从元数据中提取
        metadata = metadata or {}
        service_name = service_name or metadata.get("provider") or metadata.get("service_name")
        capability = capability or metadata.get("capability") or metadata.get("service_type")
    
    if service_name and capability:
        return await auto_media_integration.auto_add_to_media_library(
            file_path, service_name, capability, metadata
        )
    else:
        logger.warning(f"无法确定服务名称和能力类型，跳过媒体库集成: {file_path}")
        return False

"""
动态路由注册机制
基于统一服务配置自动生成API路由，消除硬编码
"""

import logging
from typing import Dict, List, Any, Optional, Callable
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field, create_model
import json
from pathlib import Path

from ..core import get_config, APIError, ErrorCode
from ..services.enhanced_ai_services import enhanced_ai_service_manager, AIRequest, ServiceType

logger = logging.getLogger(__name__)


class DynamicRouteRegistry:
    """动态路由注册表"""
    
    def __init__(self):
        self.services_config = {}
        self.registered_routes = {}
        self.router = APIRouter()
        self._load_services_config()
    
    def _load_services_config(self):
        """加载统一服务配置"""
        try:
            config_path = Path("config/unified_services.json")
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    self.services_config = json.load(f)
                logger.info("✅ 统一服务配置加载成功")
            else:
                logger.warning("⚠️ 统一服务配置文件不存在，使用默认配置")
                self.services_config = {"services": {}}
        except Exception as e:
            logger.error(f"❌ 加载统一服务配置失败: {e}")
            self.services_config = {"services": {}}
    
    def register_all_routes(self):
        """注册所有服务的动态路由"""
        logger.info("🔄 开始注册动态路由...")
        
        # 为每个服务能力注册路由
        for service_name, service_config in self.services_config.get("services", {}).items():
            self._register_service_routes(service_name, service_config)
        
        # 注册通用路由
        self._register_generic_routes()
        
        logger.info(f"✅ 动态路由注册完成，共注册 {len(self.registered_routes)} 个路由")
    
    def _register_service_routes(self, service_name: str, service_config: Dict[str, Any]):
        """为单个服务注册路由"""
        capabilities = service_config.get("capabilities", {})
        tools = service_config.get("tools", {})
        
        for capability_name, capability_config in capabilities.items():
            if not capability_config.get("enabled", False):
                continue
            
            # 为每个能力注册路由
            self._register_capability_route(service_name, capability_name, capability_config, tools)
    
    def _register_capability_route(self, service_name: str, capability_name: str, 
                                 capability_config: Dict[str, Any], tools: Dict[str, Any]):
        """为单个能力注册路由"""
        
        # 查找支持该能力的工具
        capability_tools = {
            tool_name: tool_config 
            for tool_name, tool_config in tools.items()
            if tool_config.get("capability") == capability_name
        }
        
        if not capability_tools:
            return
        
        # 生成路由路径
        route_path = f"/{capability_name.replace('_', '/')}"
        
        # 创建动态请求模型
        request_model = self._create_request_model(capability_name, capability_tools)
        
        # 创建路由处理函数
        handler = self._create_route_handler(capability_name, capability_tools)
        
        # 注册路由
        self.router.add_api_route(
            route_path,
            handler,
            methods=["POST"],
            response_model=dict,
            name=f"{capability_name}_handler"
        )
        
        self.registered_routes[route_path] = {
            "capability": capability_name,
            "tools": list(capability_tools.keys()),
            "services": [service_name]
        }
        
        logger.info(f"✅ 注册路由: POST {route_path} -> {capability_name}")
    
    def _create_request_model(self, capability_name: str, tools: Dict[str, Any]) -> BaseModel:
        """动态创建请求模型"""
        
        # 收集所有工具的参数
        all_parameters = {}
        for tool_name, tool_config in tools.items():
            tool_params = tool_config.get("parameters", {})
            for param_name, param_config in tool_params.items():
                if param_name not in all_parameters:
                    all_parameters[param_name] = param_config
        
        # 构建字段定义
        fields = {
            "provider": (Optional[str], Field(None, description="指定服务提供商")),
            "parameters": (Optional[Dict[str, Any]], Field(default_factory=dict, description="请求参数"))
        }
        
        # 添加主要输入字段
        if capability_name in ["speech_synthesis"]:
            fields["text"] = (str, Field(..., description="要合成的文本"))
        elif capability_name in ["image_generation", "video_generation"]:
            fields["prompt"] = (str, Field(..., description="生成提示词"))
        elif capability_name in ["text_generation", "chat"]:
            fields["message"] = (str, Field(..., description="输入消息"))
        
        # 动态创建模型
        model_name = f"{capability_name.title().replace('_', '')}Request"
        return create_model(model_name, **fields)
    
    def _create_route_handler(self, capability_name: str, tools: Dict[str, Any]) -> Callable:
        """动态创建路由处理函数"""
        
        async def handler(request_data: dict):
            try:
                # 映射能力名称到服务类型
                service_type_mapping = {
                    "speech_synthesis": ServiceType("speech_synthesis"),
                    "image_generation": ServiceType("image_generation"),
                    "video_generation": ServiceType("video_generation"),
                    "text_generation": ServiceType("text_generation"),
                    "chat": ServiceType("chat"),
                    "voice_cloning": ServiceType("voice_cloning")
                }
                
                service_type = service_type_mapping.get(capability_name)
                if not service_type:
                    raise HTTPException(
                        status_code=400,
                        detail=f"不支持的服务类型: {capability_name}"
                    )
                
                # 提取主要输入
                prompt = ""
                if "text" in request_data:
                    prompt = request_data["text"]
                elif "prompt" in request_data:
                    prompt = request_data["prompt"]
                elif "message" in request_data:
                    prompt = request_data["message"]
                
                # 创建AI请求
                ai_request = AIRequest(
                    service_type=service_type,
                    provider=request_data.get("provider"),
                    prompt=prompt,
                    parameters=request_data.get("parameters", {})
                )
                
                # 处理请求
                response = await enhanced_ai_service_manager.process_request(ai_request)
                
                if response.success:
                    return {
                        "success": True,
                        "data": response.data,
                        "provider": response.provider,
                        "processing_time": response.processing_time,
                        "metadata": response.metadata
                    }
                else:
                    return {
                        "success": False,
                        "error": response.error or f"{capability_name}失败",
                        "metadata": {"error_code": "PROCESSING_FAILED"}
                    }
                    
            except Exception as e:
                logger.error(f"{capability_name} error: {e}")
                return {
                    "success": False,
                    "error": f"{capability_name}失败: {str(e)}",
                    "metadata": {"error_code": "API_SERVER_ERROR"}
                }
        
        return handler
    
    def _register_generic_routes(self):
        """注册通用路由"""
        
        @self.router.get("/services/capabilities")
        async def get_service_capabilities():
            """获取所有服务能力"""
            try:
                capabilities = enhanced_ai_service_manager.get_available_capabilities()
                return {
                    "success": True,
                    "data": capabilities
                }
            except Exception as e:
                logger.error(f"获取服务能力失败: {e}")
                return {
                    "success": False,
                    "error": str(e)
                }
        
        @self.router.get("/services/stats")
        async def get_service_stats():
            """获取服务统计信息"""
            try:
                stats = enhanced_ai_service_manager.get_service_stats()
                return {
                    "success": True,
                    "data": stats
                }
            except Exception as e:
                logger.error(f"获取服务统计失败: {e}")
                return {
                    "success": False,
                    "error": str(e)
                }
        
        @self.router.get("/services/config/{service_name}")
        async def get_service_config(service_name: str):
            """获取特定服务的配置信息"""
            try:
                service_config = self.services_config.get("services", {}).get(service_name)
                if not service_config:
                    raise HTTPException(status_code=404, detail=f"服务 {service_name} 不存在")
                
                # 返回公开的配置信息（不包含敏感信息）
                public_config = {
                    "metadata": service_config.get("metadata", {}),
                    "capabilities": service_config.get("capabilities", {}),
                    "ui_config": service_config.get("ui_config", {}),
                    "tools": {
                        tool_name: {
                            "capability": tool_config.get("capability"),
                            "parameters": tool_config.get("parameters", {})
                        }
                        for tool_name, tool_config in service_config.get("tools", {}).items()
                    }
                }
                
                return {
                    "success": True,
                    "data": public_config
                }
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"获取服务配置失败: {e}")
                return {
                    "success": False,
                    "error": str(e)
                }
        
        @self.router.get("/routes/registered")
        async def get_registered_routes():
            """获取已注册的动态路由信息"""
            return {
                "success": True,
                "data": {
                    "routes": self.registered_routes,
                    "total_routes": len(self.registered_routes),
                    "services_count": len(self.services_config.get("services", {}))
                }
            }
    
    def get_router(self) -> APIRouter:
        """获取配置好的路由器"""
        return self.router
    
    def reload_config(self):
        """重新加载配置并重新注册路由"""
        logger.info("🔄 重新加载服务配置...")
        self._load_services_config()
        
        # 清除现有路由
        self.registered_routes.clear()
        self.router = APIRouter()
        
        # 重新注册路由
        self.register_all_routes()
        
        logger.info("✅ 服务配置重新加载完成")


# 全局动态路由注册表实例
dynamic_route_registry = DynamicRouteRegistry()

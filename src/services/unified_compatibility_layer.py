"""
统一适配器兼容层
为现有代码提供无缝的统一适配器集成
"""

import logging
from typing import Dict, Any, Optional
from .base_types import AIRequest, AIResponse, ServiceType, ServiceProvider
from .unified_service_adapter import (
    UnifiedRequest, UnifiedResponse, unified_service_manager
)

logger = logging.getLogger(__name__)


class UnifiedCompatibilityLayer:
    """统一适配器兼容层"""
    
    def __init__(self):
        self.service_manager = unified_service_manager
    
    async def process_ai_request(self, request: AIRequest) -> AIResponse:
        """处理AI请求，转换为统一格式并执行"""
        try:
            # 转换为统一请求格式
            unified_request = self._convert_to_unified_request(request)
            
            # 执行统一请求
            unified_response = await self.service_manager.execute_request(
                unified_request, 
                provider=request.provider
            )
            
            # 转换回AI响应格式
            return self._convert_to_ai_response(unified_response, request)
            
        except Exception as e:
            logger.error(f"❌ Failed to process AI request: {e}")
            return AIResponse(
                success=False,
                data={},
                error=str(e),
                metadata={'provider': request.provider}
            )
    
    def _convert_to_unified_request(self, ai_request: AIRequest) -> UnifiedRequest:
        """将AIRequest转换为UnifiedRequest"""
        # 映射服务类型到能力
        capability_mapping = {
            "text_generation": "text_generation",
            "text_analysis": "text_analysis", 
            "speech_synthesis": "speech_synthesis",
            "video_generation": "video_generation",
            "image_generation": "image_generation",
            "translation": "translation",
            "chat": "chat",
            "voice_cloning": "voice_cloning",
            "utility": "utility"
        }
        
        service_type_str = str(ai_request.service_type)
        capability = capability_mapping.get(service_type_str, service_type_str)
        
        return UnifiedRequest(
            capability=capability,
            content=ai_request.prompt,
            parameters=ai_request.parameters.copy(),
            metadata=ai_request.metadata.copy()
        )
    
    def _convert_to_ai_response(self, unified_response: UnifiedResponse, original_request: AIRequest) -> AIResponse:
        """将UnifiedResponse转换为AIResponse"""
        if unified_response.success:
            return AIResponse(
                success=True,
                data=unified_response.data,
                metadata={
                    'provider': unified_response.provider,
                    'processing_time': unified_response.processing_time,
                    'capability': unified_response.capability,
                    **unified_response.metadata
                }
            )
        else:
            return AIResponse(
                success=False,
                data={},
                error=unified_response.error,
                metadata={
                    'provider': unified_response.provider,
                    'capability': unified_response.capability
                }
            )
    
    def get_available_providers_for_service_type(self, service_type: ServiceType) -> list:
        """获取服务类型的可用提供商"""
        service_type_str = str(service_type)
        capability_mapping = {
            "text_generation": "text_generation",
            "text_analysis": "text_analysis",
            "speech_synthesis": "speech_synthesis", 
            "video_generation": "video_generation",
            "image_generation": "image_generation",
            "translation": "translation",
            "chat": "chat",
            "voice_cloning": "voice_cloning",
            "utility": "utility"
        }
        
        capability = capability_mapping.get(service_type_str, service_type_str)
        capabilities = self.service_manager.get_available_capabilities()
        return capabilities.get(capability, [])
    
    def is_provider_available_for_service_type(self, provider: str, service_type: ServiceType) -> bool:
        """检查提供商是否支持指定服务类型"""
        available_providers = self.get_available_providers_for_service_type(service_type)
        return provider in available_providers
    
    def get_service_parameters(self, service_type: ServiceType, provider: Optional[str] = None) -> Dict[str, Any]:
        """获取服务参数定义"""
        service_type_str = str(service_type)
        capability_mapping = {
            "text_generation": "text_generation",
            "text_analysis": "text_analysis",
            "speech_synthesis": "speech_synthesis",
            "video_generation": "video_generation", 
            "image_generation": "image_generation",
            "translation": "translation",
            "chat": "chat",
            "voice_cloning": "voice_cloning",
            "utility": "utility"
        }
        
        capability = capability_mapping.get(service_type_str, service_type_str)
        parameters = self.service_manager.get_capability_parameters(capability, provider)
        
        # 转换为字典格式
        param_dict = {}
        for param in parameters:
            param_dict[param.name] = {
                'type': param.type.value,
                'description': param.description,
                'required': param.required,
                'default': param.default,
                'min_value': param.min_value,
                'max_value': param.max_value,
                'choices': param.choices,
                'pattern': param.pattern
            }
        
        return param_dict
    
    def reload_configuration(self):
        """重新加载配置"""
        logger.info("🔄 Reloading unified service configuration...")
        self.service_manager.reload_adapters()
        logger.info("✅ Unified service configuration reloaded")


# 全局兼容层实例
unified_compatibility_layer = UnifiedCompatibilityLayer()


# 兼容性函数 - 为现有代码提供无缝迁移
async def process_request_with_unified_adapter(request: AIRequest) -> AIResponse:
    """使用统一适配器处理请求（兼容性函数）"""
    return await unified_compatibility_layer.process_ai_request(request)


def get_providers_for_service_type(service_type: ServiceType) -> list:
    """获取服务类型的可用提供商（兼容性函数）"""
    return unified_compatibility_layer.get_available_providers_for_service_type(service_type)


def check_provider_capability(provider: str, service_type: ServiceType) -> bool:
    """检查提供商能力（兼容性函数）"""
    return unified_compatibility_layer.is_provider_available_for_service_type(provider, service_type)


class UnifiedServiceIntegration:
    """统一服务集成类 - 替代现有的服务管理器"""
    
    def __init__(self):
        self.compatibility_layer = unified_compatibility_layer
    
    async def process_request(self, request: AIRequest) -> AIResponse:
        """处理请求"""
        return await self.compatibility_layer.process_ai_request(request)
    
    def get_available_services(self) -> Dict[str, list]:
        """获取可用服务"""
        capabilities = self.compatibility_layer.service_manager.get_available_capabilities()
        
        # 转换为旧格式
        services = {}
        for capability, providers in capabilities.items():
            # 映射能力到服务类型
            service_type_mapping = {
                "text_generation": "TEXT_GENERATION",
                "text_analysis": "TEXT_ANALYSIS",
                "speech_synthesis": "SPEECH_SYNTHESIS",
                "video_generation": "VIDEO_GENERATION",
                "image_generation": "IMAGE_GENERATION",
                "translation": "TRANSLATION",
                "chat": "CHAT",
                "voice_cloning": "VOICE_CLONING",
                "utility": "UTILITY"
            }
            
            service_type = service_type_mapping.get(capability, capability.upper())
            services[service_type] = providers
        
        return services
    
    def get_service_info(self, service_type: str, provider: str = None) -> Dict[str, Any]:
        """获取服务信息"""
        # 转换服务类型
        capability_mapping = {
            "TEXT_GENERATION": "text_generation",
            "TEXT_ANALYSIS": "text_analysis",
            "SPEECH_SYNTHESIS": "speech_synthesis",
            "VIDEO_GENERATION": "video_generation",
            "IMAGE_GENERATION": "image_generation",
            "TRANSLATION": "translation",
            "CHAT": "chat",
            "VOICE_CLONING": "voice_cloning",
            "UTILITY": "utility"
        }
        
        capability = capability_mapping.get(service_type, service_type.lower())
        parameters = self.compatibility_layer.service_manager.get_capability_parameters(capability, provider)
        
        return {
            'capability': capability,
            'parameters': [
                {
                    'name': param.name,
                    'type': param.type.value,
                    'description': param.description,
                    'required': param.required,
                    'default': param.default
                }
                for param in parameters
            ],
            'providers': self.compatibility_layer.service_manager.get_available_capabilities().get(capability, [])
        }
    
    def reload_services(self):
        """重新加载服务"""
        self.compatibility_layer.reload_configuration()


# 全局统一服务集成实例
unified_service_integration = UnifiedServiceIntegration()


# 记录内存以便后续使用
def remember_unified_architecture():
    """记录统一架构的关键信息"""
    return {
        "architecture": "unified_service_adapter",
        "key_components": [
            "UnifiedRequest/UnifiedResponse - 统一请求响应格式",
            "ServiceAdapter - 服务适配器基类",
            "MCPServiceAdapter - MCP服务适配器",
            "DirectAPIAdapter - 直接API适配器", 
            "UnifiedServiceManager - 统一服务管理器",
            "UnifiedCompatibilityLayer - 兼容层",
            "service_adapters.json - 适配器配置文件"
        ],
        "benefits": [
            "内部统一接口，外部动态适配",
            "配置驱动的参数映射和响应解析",
            "支持MCP和直接API两种适配方式",
            "完全向后兼容现有代码",
            "动态加载和重载配置",
            "统一的错误处理和日志记录"
        ],
        "usage": "现有代码无需修改，自动使用新的统一适配器架构"
    }

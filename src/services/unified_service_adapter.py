"""
统一服务适配器架构
实现"内部统一，外部适配"的动态化服务架构
"""

import json
import logging
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from enum import Enum

logger = logging.getLogger(__name__)


class ParameterType(Enum):
    """参数类型"""
    STRING = "string"
    INTEGER = "integer"
    FLOAT = "float"
    BOOLEAN = "boolean"
    ARRAY = "array"
    OBJECT = "object"


@dataclass
class UnifiedParameter:
    """统一参数定义"""
    name: str
    type: ParameterType
    description: str
    required: bool = False
    default: Any = None
    min_value: Optional[Union[int, float]] = None
    max_value: Optional[Union[int, float]] = None
    choices: Optional[List[Any]] = None
    pattern: Optional[str] = None
    # 新增：服务商特定约束
    provider_constraints: Optional[Dict[str, Dict[str, Any]]] = None
    # 新增：参数依赖关系
    depends_on: Optional[Dict[str, Any]] = None
    # 新增：智能建议
    suggestions: Optional[List[str]] = None


@dataclass
class ParameterConstraint:
    """参数约束定义"""
    provider: str
    capability: str
    parameter: str
    constraint_type: str  # "choices", "range", "pattern", "conditional"
    constraint_value: Any
    error_message: str = ""
    suggestion: str = ""


@dataclass
class SmartParameterInfo:
    """智能参数信息"""
    name: str
    type: ParameterType
    description: str
    required: bool
    current_constraints: Dict[str, Any]  # 当前服务商的约束
    available_choices: Optional[List[Any]] = None
    suggested_value: Any = None
    validation_rules: List[str] = None


@dataclass
class UnifiedRequest:
    """统一请求格式"""
    capability: str  # 功能类型：speech_synthesis, image_generation等
    content: str     # 主要内容：文本、提示词等
    parameters: Dict[str, Any]  # 参数字典
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class UnifiedResponse:
    """统一响应格式"""
    success: bool
    capability: str
    data: Dict[str, Any]
    metadata: Dict[str, Any] = None
    error: Optional[str] = None
    provider: Optional[str] = None
    processing_time: float = 0.0
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class SmartParameterManager:
    """智能参数管理器"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.constraints = self._load_constraints()

    def _load_constraints(self) -> Dict[str, List[ParameterConstraint]]:
        """加载参数约束"""
        constraints = {}

        for provider_name, provider_config in self.config.get("adapters", {}).items():
            provider_constraints = []

            # 处理capabilities可能是数组或对象的情况
            capabilities = provider_config.get("capabilities", {})
            if isinstance(capabilities, list):
                # 如果是数组格式，跳过约束加载（没有参数定义）
                continue

            for capability, cap_config in provider_config.get("capabilities", {}).items():
                if isinstance(cap_config, dict) and "parameters" in cap_config:
                    for param_name, param_config in cap_config["parameters"].items():
                        # 解析约束
                        if "choices" in param_config:
                            provider_constraints.append(ParameterConstraint(
                                provider=provider_name,
                                capability=capability,
                                parameter=param_name,
                                constraint_type="choices",
                                constraint_value=param_config["choices"],
                                error_message=f"{param_name} must be one of: {param_config['choices']}",
                                suggestion=f"Available options: {', '.join(map(str, param_config['choices']))}"
                            ))

                        if "min_value" in param_config or "max_value" in param_config:
                            provider_constraints.append(ParameterConstraint(
                                provider=provider_name,
                                capability=capability,
                                parameter=param_name,
                                constraint_type="range",
                                constraint_value={
                                    "min": param_config.get("min_value"),
                                    "max": param_config.get("max_value")
                                },
                                error_message=f"{param_name} must be between {param_config.get('min_value', 'N/A')} and {param_config.get('max_value', 'N/A')}",
                                suggestion=f"Recommended range: {param_config.get('min_value', 'N/A')} - {param_config.get('max_value', 'N/A')}"
                            ))

            constraints[provider_name] = provider_constraints

        return constraints

    def get_smart_parameters(self, provider: str, capability: str) -> List[SmartParameterInfo]:
        """获取智能参数信息"""
        provider_config = self.config.get("adapters", {}).get(provider, {})
        capabilities = provider_config.get("capabilities", {})

        # 处理capabilities可能是数组或对象的情况
        if isinstance(capabilities, list):
            # 如果是数组格式，没有参数定义，返回空列表
            return []

        capability_config = capabilities.get(capability, {})

        if isinstance(capability_config, str):
            # 简单配置，返回基础参数
            return []

        parameters = []
        param_configs = capability_config.get("parameters", {})

        for param_name, param_config in param_configs.items():
            # 获取当前服务商的约束
            current_constraints = {}
            if "choices" in param_config:
                current_constraints["choices"] = param_config["choices"]
            if "min_value" in param_config:
                current_constraints["min_value"] = param_config["min_value"]
            if "max_value" in param_config:
                current_constraints["max_value"] = param_config["max_value"]
            if "pattern" in param_config:
                current_constraints["pattern"] = param_config["pattern"]

            # 生成验证规则
            validation_rules = []
            if "required" in param_config and param_config["required"]:
                validation_rules.append("required")
            if "choices" in param_config:
                validation_rules.append(f"choices: {param_config['choices']}")
            if "min_value" in param_config or "max_value" in param_config:
                min_val = param_config.get("min_value", "N/A")
                max_val = param_config.get("max_value", "N/A")
                validation_rules.append(f"range: {min_val} - {max_val}")

            parameters.append(SmartParameterInfo(
                name=param_name,
                type=ParameterType(param_config.get("type", "string")),
                description=param_config.get("description", ""),
                required=param_config.get("required", False),
                current_constraints=current_constraints,
                available_choices=param_config.get("choices"),
                suggested_value=param_config.get("default"),
                validation_rules=validation_rules
            ))

        return parameters

    def validate_parameters(self, provider: str, capability: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """验证参数并返回验证结果"""
        result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "suggestions": []
        }

        provider_constraints = self.constraints.get(provider, [])
        relevant_constraints = [c for c in provider_constraints if c.capability == capability]

        for constraint in relevant_constraints:
            param_name = constraint.parameter
            if param_name in parameters:
                param_value = parameters[param_name]

                if constraint.constraint_type == "choices":
                    if param_value not in constraint.constraint_value:
                        result["valid"] = False
                        result["errors"].append({
                            "parameter": param_name,
                            "message": constraint.error_message,
                            "suggestion": constraint.suggestion
                        })

                elif constraint.constraint_type == "range":
                    range_config = constraint.constraint_value
                    min_val = range_config.get("min")
                    max_val = range_config.get("max")

                    try:
                        numeric_value = float(param_value)
                        if min_val is not None and numeric_value < min_val:
                            result["valid"] = False
                            result["errors"].append({
                                "parameter": param_name,
                                "message": f"{param_name} ({param_value}) is below minimum ({min_val})",
                                "suggestion": f"Use value >= {min_val}"
                            })
                        elif max_val is not None and numeric_value > max_val:
                            result["valid"] = False
                            result["errors"].append({
                                "parameter": param_name,
                                "message": f"{param_name} ({param_value}) is above maximum ({max_val})",
                                "suggestion": f"Use value <= {max_val}"
                            })
                    except (ValueError, TypeError):
                        result["warnings"].append({
                            "parameter": param_name,
                            "message": f"{param_name} should be numeric for range validation"
                        })

        return result


class ServiceAdapter(ABC):
    """服务适配器基类"""

    def __init__(self, provider_name: str, config: Dict[str, Any]):
        self.provider_name = provider_name
        self.config = config
        # 从capabilities中提取支持的能力列表
        capabilities = config.get("capabilities", {})
        if isinstance(capabilities, dict):
            self.supported_capabilities = list(capabilities.keys())
        elif isinstance(capabilities, list):
            self.supported_capabilities = capabilities
        else:
            self.supported_capabilities = []
        self.parameter_mappings = config.get("parameter_mappings", {})
        self.response_mappings = config.get("response_mappings", {})
        # 新增：智能参数管理器
        try:
            self.smart_param_manager = SmartParameterManager({"adapters": {provider_name: config}})
        except Exception as e:
            logger.warning(f"Failed to initialize smart parameter manager for {provider_name}: {e}")
            self.smart_param_manager = None
    
    @abstractmethod
    async def execute_capability(self, request: UnifiedRequest) -> UnifiedResponse:
        """执行具体能力"""
        pass
    
    def get_capability_parameters(self, capability: str) -> List[UnifiedParameter]:
        """获取能力支持的参数"""
        capability_config = self.config.get("capabilities", {}).get(capability, {})
        parameters = []
        
        for param_name, param_config in capability_config.get("parameters", {}).items():
            parameters.append(UnifiedParameter(
                name=param_name,
                type=ParameterType(param_config.get("type", "string")),
                description=param_config.get("description", ""),
                required=param_config.get("required", False),
                default=param_config.get("default"),
                min_value=param_config.get("min_value"),
                max_value=param_config.get("max_value"),
                choices=param_config.get("choices"),
                pattern=param_config.get("pattern")
            ))
        
        return parameters
    
    def map_request_parameters(self, capability: str, unified_params: Dict[str, Any]) -> Dict[str, Any]:
        """将统一参数映射为提供商特定参数"""
        capability_mapping = self.parameter_mappings.get(capability, {})
        mapped_params = {}
        
        for unified_key, value in unified_params.items():
            # 查找映射规则
            mapping_rule = capability_mapping.get(unified_key)
            if mapping_rule:
                if isinstance(mapping_rule, str):
                    # 简单映射：直接重命名
                    mapped_params[mapping_rule] = value
                elif isinstance(mapping_rule, dict):
                    # 复杂映射：包含转换逻辑
                    target_key = mapping_rule.get("target", unified_key)
                    transform = mapping_rule.get("transform")
                    
                    if transform:
                        value = self._apply_transform(value, transform)
                    
                    mapped_params[target_key] = value
            else:
                # 无映射规则，直接使用原键名
                mapped_params[unified_key] = value
        
        return mapped_params
    
    def map_response_data(self, capability: str, provider_response: Dict[str, Any]) -> Dict[str, Any]:
        """将提供商响应映射为统一格式"""
        capability_mapping = self.response_mappings.get(capability, {})
        unified_data = {}
        
        for unified_key, mapping_rule in capability_mapping.items():
            if isinstance(mapping_rule, str):
                # 简单映射：直接提取
                if mapping_rule in provider_response:
                    unified_data[unified_key] = provider_response[mapping_rule]
            elif isinstance(mapping_rule, dict):
                # 复杂映射：包含提取和转换逻辑
                source_path = mapping_rule.get("source", "")
                transform = mapping_rule.get("transform")
                default_value = mapping_rule.get("default")
                
                # 支持嵌套路径提取，如 "data.result.url"
                value = self._extract_nested_value(provider_response, source_path, default_value)
                
                if transform and value is not None:
                    value = self._apply_transform(value, transform)
                
                if value is not None:
                    unified_data[unified_key] = value
        
        return unified_data
    
    def _apply_transform(self, value: Any, transform: Dict[str, Any]) -> Any:
        """应用数据转换"""
        transform_type = transform.get("type")
        
        if transform_type == "format":
            # 格式化转换
            template = transform.get("template", "{}")
            return template.format(value)
        elif transform_type == "map":
            # 值映射转换
            mapping = transform.get("mapping", {})
            return mapping.get(str(value), value)
        elif transform_type == "extract":
            # 正则提取
            import re
            pattern = transform.get("pattern", "")
            match = re.search(pattern, str(value))
            return match.group(1) if match else value
        elif transform_type == "calculate":
            # 计算转换
            expression = transform.get("expression", "x")
            # 简单的数学表达式支持
            try:
                return eval(expression.replace("x", str(value)))
            except:
                return value
        
        return value
    
    def _extract_nested_value(self, data: Dict[str, Any], path: str, default: Any = None) -> Any:
        """提取嵌套值"""
        if not path:
            return data
        
        keys = path.split(".")
        current = data
        
        try:
            for key in keys:
                if isinstance(current, dict) and key in current:
                    current = current[key]
                else:
                    return default
            return current
        except:
            return default


class MCPServiceAdapter(ServiceAdapter):
    """MCP服务适配器"""
    
    def __init__(self, provider_name: str, config: Dict[str, Any], mcp_manager):
        super().__init__(provider_name, config)
        self.mcp_manager = mcp_manager
        self.server_name = config.get("server_name", provider_name)
        self.tool_mappings = config.get("tool_mappings", {})
    
    async def execute_capability(self, request: UnifiedRequest) -> UnifiedResponse:
        """执行MCP服务能力"""
        try:
            # 查找对应的MCP工具
            tool_name = self.tool_mappings.get(request.capability)
            if not tool_name:
                raise ValueError(f"No tool mapping found for capability: {request.capability}")
            
            # 映射请求参数
            mapped_params = self.map_request_parameters(request.capability, request.parameters)
            
            # 添加主要内容到参数中
            content_param = self.config.get("content_parameter", "text")
            mapped_params[content_param] = request.content
            
            # 调用MCP工具
            start_time = time.time()
            result = await self.mcp_manager.call_tool(self.server_name, tool_name, mapped_params)
            processing_time = time.time() - start_time
            
            # 提取结果内容
            result_content = self._extract_mcp_result(result)
            
            # 映射响应数据
            unified_data = self.map_response_data(request.capability, result_content)
            
            return UnifiedResponse(
                success=True,
                capability=request.capability,
                data=unified_data,
                provider=self.provider_name,
                processing_time=processing_time,
                metadata={
                    "tool_used": tool_name,
                    "server_name": self.server_name
                }
            )
            
        except Exception as e:
            logger.error(f"MCP service execution failed for {self.provider_name}: {e}")
            return UnifiedResponse(
                success=False,
                capability=request.capability,
                data={},
                provider=self.provider_name,
                error=str(e)
            )
    
    def _extract_mcp_result(self, result) -> Dict[str, Any]:
        """提取MCP结果内容"""
        if hasattr(result, 'content') and result.content:
            if len(result.content) > 0:
                content_item = result.content[0]
                if hasattr(content_item, 'text'):
                    try:
                        # 尝试解析JSON
                        return json.loads(content_item.text)
                    except json.JSONDecodeError:
                        # 如果不是JSON，返回文本内容
                        return {"text": content_item.text}
        
        # 回退：返回原始结果
        return {"raw_result": str(result)}


class DirectAPIAdapter(ServiceAdapter):
    """直接API调用适配器"""
    
    def __init__(self, provider_name: str, config: Dict[str, Any]):
        super().__init__(provider_name, config)
        self.api_config = config.get("api", {})
        self.base_url = self.api_config.get("base_url", "")
        self.headers = self.api_config.get("headers", {})
    
    async def execute_capability(self, request: UnifiedRequest) -> UnifiedResponse:
        """执行直接API调用"""
        import aiohttp
        import time
        
        try:
            # 获取API端点配置
            endpoint_config = self.api_config.get("endpoints", {}).get(request.capability)
            if not endpoint_config:
                raise ValueError(f"No API endpoint found for capability: {request.capability}")
            
            # 构建请求URL
            endpoint = endpoint_config.get("path", "")
            url = f"{self.base_url.rstrip('/')}/{endpoint.lstrip('/')}"
            
            # 映射请求参数
            mapped_params = self.map_request_parameters(request.capability, request.parameters)
            
            # 添加主要内容
            content_param = endpoint_config.get("content_parameter", "text")
            mapped_params[content_param] = request.content
            
            # 发送HTTP请求
            method = endpoint_config.get("method", "POST").upper()
            start_time = time.time()
            
            async with aiohttp.ClientSession() as session:
                async with session.request(
                    method=method,
                    url=url,
                    json=mapped_params if method in ["POST", "PUT", "PATCH"] else None,
                    params=mapped_params if method == "GET" else None,
                    headers=self.headers
                ) as response:
                    processing_time = time.time() - start_time
                    
                    if response.status == 200:
                        result_data = await response.json()
                        unified_data = self.map_response_data(request.capability, result_data)
                        
                        return UnifiedResponse(
                            success=True,
                            capability=request.capability,
                            data=unified_data,
                            provider=self.provider_name,
                            processing_time=processing_time
                        )
                    else:
                        error_text = await response.text()
                        raise Exception(f"API request failed with status {response.status}: {error_text}")
            
        except Exception as e:
            logger.error(f"Direct API execution failed for {self.provider_name}: {e}")
            return UnifiedResponse(
                success=False,
                capability=request.capability,
                data={},
                provider=self.provider_name,
                error=str(e)
            )


class UnifiedServiceManager:
    """统一服务管理器"""

    def __init__(self, config_dir: Path = None):
        self.config_dir = config_dir or Path("config")
        self.adapters: Dict[str, ServiceAdapter] = {}
        self.capability_registry: Dict[str, List[str]] = {}  # capability -> [providers]
        # 新增：智能参数管理器
        self.smart_param_manager = None
        self._load_adapters()

    def _load_adapters(self):
        """加载所有适配器"""
        adapter_config_path = self.config_dir / "service_adapters.json"

        if not adapter_config_path.exists():
            logger.warning(f"Adapter config file not found: {adapter_config_path}")
            return

        try:
            with open(adapter_config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)

            for provider_name, provider_config in config.get("adapters", {}).items():
                self._create_adapter(provider_name, provider_config)

            # 初始化智能参数管理器
            self.smart_param_manager = SmartParameterManager(config)

            logger.info(f"✅ Loaded {len(self.adapters)} service adapters")

        except Exception as e:
            logger.error(f"❌ Failed to load adapters: {e}")

    def _create_adapter(self, provider_name: str, config: Dict[str, Any]):
        """创建适配器实例"""
        adapter_type = config.get("type", "mcp")

        try:
            if adapter_type == "mcp":
                # 延迟导入MCP管理器
                mcp_manager = self._get_mcp_manager()
                if not mcp_manager:
                    logger.error(f"❌ MCP manager not available for {provider_name}")
                    return

                adapter = MCPServiceAdapter(provider_name, config, mcp_manager)
            elif adapter_type == "direct_api":
                adapter = DirectAPIAdapter(provider_name, config)
            else:
                logger.error(f"Unknown adapter type: {adapter_type}")
                return

            self.adapters[provider_name] = adapter

            # 注册能力
            for capability in adapter.supported_capabilities:
                if capability not in self.capability_registry:
                    self.capability_registry[capability] = []
                self.capability_registry[capability].append(provider_name)

            logger.info(f"✅ Created {adapter_type} adapter for {provider_name}")

        except Exception as e:
            logger.error(f"❌ Failed to create adapter for {provider_name}: {e}")

    def _get_mcp_manager(self):
        """获取MCP管理器实例"""
        try:
            # 导入FastMCP管理器
            from .fastmcp_server import fastmcp_manager
            return fastmcp_manager
        except ImportError as e:
            logger.error(f"❌ Could not import MCP manager: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ Error getting MCP manager: {e}")
            return None

    async def execute_request(self, request: UnifiedRequest, provider: Optional[str] = None) -> UnifiedResponse:
        """执行统一请求"""
        try:
            # 选择提供商
            if provider:
                if provider not in self.adapters:
                    raise ValueError(f"Provider not found: {provider}")
                selected_provider = provider
            else:
                # 自动选择最佳提供商
                available_providers = self.capability_registry.get(request.capability, [])
                if not available_providers:
                    raise ValueError(f"No providers available for capability: {request.capability}")
                selected_provider = available_providers[0]  # 简单选择第一个

            # 执行请求
            adapter = self.adapters[selected_provider]
            return await adapter.execute_capability(request)

        except Exception as e:
            logger.error(f"❌ Failed to execute unified request: {e}")
            return UnifiedResponse(
                success=False,
                capability=request.capability,
                data={},
                error=str(e)
            )

    def get_available_capabilities(self) -> Dict[str, List[str]]:
        """获取可用能力和提供商"""
        return self.capability_registry.copy()

    def get_capability_parameters(self, capability: str, provider: Optional[str] = None) -> List[UnifiedParameter]:
        """获取能力参数定义"""
        if provider:
            if provider in self.adapters:
                return self.adapters[provider].get_capability_parameters(capability)
        else:
            # 返回第一个可用提供商的参数定义
            available_providers = self.capability_registry.get(capability, [])
            if available_providers and available_providers[0] in self.adapters:
                return self.adapters[available_providers[0]].get_capability_parameters(capability)

        return []

    def get_smart_parameters(self, provider: str, capability: str) -> List[SmartParameterInfo]:
        """获取智能参数信息"""
        if not self.smart_param_manager:
            return []

        return self.smart_param_manager.get_smart_parameters(provider, capability)

    def validate_parameters(self, provider: str, capability: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """验证参数"""
        if not self.smart_param_manager:
            return {"valid": True, "errors": [], "warnings": [], "suggestions": []}

        return self.smart_param_manager.validate_parameters(provider, capability, parameters)

    def reload_adapters(self):
        """重新加载适配器"""
        logger.info("🔄 Reloading service adapters...")
        self.adapters.clear()
        self.capability_registry.clear()
        self.smart_param_manager = None
        self._load_adapters()
        logger.info("✅ Service adapters reloaded")


# 全局实例
unified_service_manager = UnifiedServiceManager()

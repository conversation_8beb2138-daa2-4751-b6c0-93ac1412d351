"""
统一结果解析和错误处理系统
基于配置的动态解析规则，支持所有服务类型
"""

import logging
import re
import json
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass
from enum import Enum

from ..core import APIError, ErrorCode

logger = logging.getLogger(__name__)


@dataclass
class ParseRule:
    """解析规则"""
    pattern: str
    extract: str
    priority: int = 5
    transform: Optional[str] = None


@dataclass
class ErrorRule:
    """错误规则"""
    pattern: str
    error_type: str
    message: str
    priority: int = 5


class UniversalResultParser:
    """统一结果解析器"""
    
    def __init__(self):
        self.services_config = {}
        self.parse_rules = {}
        self.error_rules = {}
        self._load_config()
    
    def _load_config(self):
        """加载配置"""
        try:
            config_path = Path("config/unified_services.json")
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    self.services_config = json.load(f)
                self._build_parsing_rules()
                logger.info("✅ 统一结果解析器配置加载成功")
            else:
                logger.warning("⚠️ 统一服务配置文件不存在")
        except Exception as e:
            logger.error(f"❌ 加载解析器配置失败: {e}")
    
    def _build_parsing_rules(self):
        """构建解析规则"""
        services = self.services_config.get("services", {})
        
        for service_name, service_config in services.items():
            result_parsing = service_config.get("result_parsing", {})
            
            for capability, parsing_config in result_parsing.items():
                # 构建成功解析规则
                success_patterns = parsing_config.get("success_patterns", [])
                for pattern_config in success_patterns:
                    rule = ParseRule(
                        pattern=pattern_config["pattern"],
                        extract=pattern_config["extract"],
                        priority=pattern_config.get("priority", 5),
                        transform=pattern_config.get("transform")
                    )
                    
                    key = f"{service_name}_{capability}_success"
                    if key not in self.parse_rules:
                        self.parse_rules[key] = []
                    self.parse_rules[key].append(rule)
                
                # 构建错误解析规则
                error_patterns = parsing_config.get("error_patterns", [])
                for error_config in error_patterns:
                    rule = ErrorRule(
                        pattern=error_config["pattern"],
                        error_type=error_config["type"],
                        message=error_config["message"],
                        priority=error_config.get("priority", 5)
                    )
                    
                    key = f"{service_name}_{capability}_error"
                    if key not in self.error_rules:
                        self.error_rules[key] = []
                    self.error_rules[key].append(rule)
        
        # 按优先级排序规则
        for rules_list in self.parse_rules.values():
            rules_list.sort(key=lambda x: x.priority, reverse=True)
        
        for rules_list in self.error_rules.values():
            rules_list.sort(key=lambda x: x.priority, reverse=True)
    
    def parse_result(self, content: str, service_name: str, capability: str, 
                    parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """解析结果"""
        if not content:
            return {
                "success": False,
                "error": "空响应内容",
                "data": None
            }
        
        # 首先检查是否有错误
        error_result = self._check_errors(content, service_name, capability)
        if error_result:
            return error_result
        
        # 解析成功结果
        success_result = self._parse_success(content, service_name, capability, parameters)
        if success_result:
            return success_result
        
        # 如果没有匹配的规则，返回原始内容
        return {
            "success": True,
            "data": content.strip(),
            "metadata": {
                "parser": "fallback",
                "service": service_name,
                "capability": capability
            }
        }
    
    def _check_errors(self, content: str, service_name: str, capability: str) -> Optional[Dict[str, Any]]:
        """检查错误"""
        error_key = f"{service_name}_{capability}_error"
        error_rules = self.error_rules.get(error_key, [])
        
        # 添加通用错误规则
        generic_error_rules = self.error_rules.get("generic_error", [])
        all_error_rules = error_rules + generic_error_rules
        
        for rule in all_error_rules:
            try:
                if re.search(rule.pattern, content, re.IGNORECASE | re.MULTILINE):
                    return {
                        "success": False,
                        "error": rule.message,
                        "error_type": rule.error_type,
                        "metadata": {
                            "parser": "error_pattern",
                            "pattern": rule.pattern,
                            "service": service_name,
                            "capability": capability
                        }
                    }
            except re.error as e:
                logger.warning(f"错误的正则表达式模式 '{rule.pattern}': {e}")
                continue
        
        return None
    
    def _parse_success(self, content: str, service_name: str, capability: str, 
                      parameters: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """解析成功结果"""
        success_key = f"{service_name}_{capability}_success"
        parse_rules = self.parse_rules.get(success_key, [])
        
        # 添加通用解析规则
        generic_parse_rules = self.parse_rules.get(f"generic_{capability}_success", [])
        all_parse_rules = parse_rules + generic_parse_rules
        
        for rule in all_parse_rules:
            try:
                match = re.search(rule.pattern, content, re.IGNORECASE | re.MULTILINE)
                if match:
                    extracted_data = self._extract_data(match, rule, content, parameters)
                    if extracted_data is not None:
                        return {
                            "success": True,
                            "data": extracted_data,
                            "metadata": {
                                "parser": "pattern_match",
                                "pattern": rule.pattern,
                                "extract": rule.extract,
                                "service": service_name,
                                "capability": capability
                            }
                        }
            except re.error as e:
                logger.warning(f"错误的正则表达式模式 '{rule.pattern}': {e}")
                continue
        
        return None
    
    def _extract_data(self, match: re.Match, rule: ParseRule, content: str,
                     parameters: Dict[str, Any] = None) -> Any:
        """提取数据"""
        if rule.extract == "file_path":
            file_path = match.group(1) if match.groups() else match.group(0)
            result = self._process_file_path(file_path, parameters)
            # 自动集成到媒体库
            self._auto_integrate_to_media_library(result, parameters)
            return result

        elif rule.extract == "url":
            url = match.group(1) if match.groups() else match.group(0)
            return self._process_url(url, parameters)

        elif rule.extract == "json":
            json_str = match.group(1) if match.groups() else match.group(0)
            return self._process_json(json_str)

        elif rule.extract == "text":
            text = match.group(1) if match.groups() else match.group(0)
            return self._process_text(text, rule.transform)

        elif rule.extract == "full_match":
            return match.group(0)

        elif rule.extract == "groups":
            return list(match.groups())

        else:
            # 默认返回第一个捕获组或整个匹配
            return match.group(1) if match.groups() else match.group(0)

    def _auto_integrate_to_media_library(self, file_result: Dict[str, Any], parameters: Dict[str, Any] = None):
        """自动集成到媒体库"""
        try:
            # 检查是否跳过媒体库集成
            parameters = parameters or {}
            if parameters.get('is_preview') or parameters.get('skip_media_integration'):
                logger.debug("跳过媒体库集成（预览模式或明确跳过）")
                return

            if file_result.get("type") == "file" and file_result.get("exists"):
                # 异步集成到媒体库
                import asyncio
                from ..api.media_library import auto_add_to_media_library

                # 在后台任务中执行
                asyncio.create_task(auto_add_to_media_library(
                    file_path=file_result["file_path"],
                    metadata=parameters
                ))

        except Exception as e:
            logger.warning(f"自动媒体库集成失败: {e}")
    
    def _process_file_path(self, file_path: str, parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """处理文件路径"""
        file_path = file_path.strip()
        
        # 确保路径是绝对路径
        if not Path(file_path).is_absolute():
            output_dir = parameters.get("output_directory", "./output") if parameters else "./output"
            file_path = str(Path(output_dir) / file_path)
        
        return {
            "type": "file",
            "file_path": file_path,
            "file_name": Path(file_path).name,
            "file_size": self._get_file_size(file_path),
            "exists": Path(file_path).exists()
        }
    
    def _process_url(self, url: str, parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """处理URL"""
        url = url.strip()
        
        return {
            "type": "url",
            "url": url,
            "domain": self._extract_domain(url)
        }
    
    def _process_json(self, json_str: str) -> Any:
        """处理JSON数据"""
        try:
            return json.loads(json_str)
        except json.JSONDecodeError as e:
            logger.warning(f"JSON解析失败: {e}")
            return json_str
    
    def _process_text(self, text: str, transform: Optional[str] = None) -> str:
        """处理文本数据"""
        text = text.strip()
        
        if transform == "upper":
            return text.upper()
        elif transform == "lower":
            return text.lower()
        elif transform == "title":
            return text.title()
        elif transform == "strip_quotes":
            return text.strip('"\'')
        else:
            return text
    
    def _get_file_size(self, file_path: str) -> Optional[int]:
        """获取文件大小"""
        try:
            return Path(file_path).stat().st_size
        except (OSError, FileNotFoundError):
            return None
    
    def _extract_domain(self, url: str) -> Optional[str]:
        """提取域名"""
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            return parsed.netloc
        except Exception:
            return None
    
    def add_parsing_rule(self, service_name: str, capability: str, rule_type: str, 
                        pattern: str, extract: str, priority: int = 5, **kwargs):
        """动态添加解析规则"""
        key = f"{service_name}_{capability}_{rule_type}"
        
        if rule_type == "success":
            rule = ParseRule(
                pattern=pattern,
                extract=extract,
                priority=priority,
                transform=kwargs.get("transform")
            )
            
            if key not in self.parse_rules:
                self.parse_rules[key] = []
            self.parse_rules[key].append(rule)
            self.parse_rules[key].sort(key=lambda x: x.priority, reverse=True)
            
        elif rule_type == "error":
            rule = ErrorRule(
                pattern=pattern,
                error_type=kwargs.get("error_type", "unknown_error"),
                message=kwargs.get("message", "未知错误"),
                priority=priority
            )
            
            if key not in self.error_rules:
                self.error_rules[key] = []
            self.error_rules[key].append(rule)
            self.error_rules[key].sort(key=lambda x: x.priority, reverse=True)
    
    def get_parsing_rules(self, service_name: str = None, capability: str = None) -> Dict[str, Any]:
        """获取解析规则"""
        if service_name and capability:
            success_key = f"{service_name}_{capability}_success"
            error_key = f"{service_name}_{capability}_error"
            
            return {
                "success_rules": self.parse_rules.get(success_key, []),
                "error_rules": self.error_rules.get(error_key, [])
            }
        else:
            return {
                "all_parse_rules": self.parse_rules,
                "all_error_rules": self.error_rules
            }
    
    def reload_config(self):
        """重新加载配置"""
        logger.info("🔄 重新加载解析器配置...")
        self.parse_rules.clear()
        self.error_rules.clear()
        self._load_config()
        logger.info("✅ 解析器配置重新加载完成")


# 全局统一结果解析器实例
universal_result_parser = UniversalResultParser()


# 添加通用解析规则
def _add_generic_rules():
    """添加通用解析规则"""
    parser = universal_result_parser
    
    # 通用成功模式
    generic_success_patterns = [
        {"pattern": r"成功.*?文件.*?([^\s]+\.(mp3|wav|mp4|png|jpg|jpeg|gif))", "extract": "file_path", "priority": 8},
        {"pattern": r"Success.*?File.*?([^\s]+\.(mp3|wav|mp4|png|jpg|jpeg|gif))", "extract": "file_path", "priority": 8},
        {"pattern": r"输出.*?([^\s]+\.(mp3|wav|mp4|png|jpg|jpeg|gif))", "extract": "file_path", "priority": 7},
        {"pattern": r"生成.*?([^\s]+\.(mp3|wav|mp4|png|jpg|jpeg|gif))", "extract": "file_path", "priority": 7},
        {"pattern": r"(https?://[^\s]+\.(mp3|wav|mp4|png|jpg|jpeg|gif))", "extract": "url", "priority": 6}
    ]
    
    for pattern_config in generic_success_patterns:
        for capability in ["speech_synthesis", "image_generation", "video_generation"]:
            parser.add_parsing_rule(
                "generic", capability, "success",
                pattern_config["pattern"],
                pattern_config["extract"],
                pattern_config["priority"]
            )
    
    # 通用错误模式
    generic_error_patterns = [
        {"pattern": r"余额不足|insufficient balance|quota exceeded", "error_type": "balance_error", "message": "账户余额不足，请充值后重试"},
        {"pattern": r"API.*?密钥.*?无效|invalid.*?api.*?key|unauthorized", "error_type": "auth_error", "message": "API密钥无效，请检查配置"},
        {"pattern": r"参数.*?错误|invalid.*?param|bad.*?request", "error_type": "parameter_error", "message": "参数错误，请检查输入"},
        {"pattern": r"网络.*?错误|network.*?error|connection.*?failed", "error_type": "network_error", "message": "网络连接失败，请稍后重试"},
        {"pattern": r"服务.*?不可用|service.*?unavailable|server.*?error", "error_type": "service_error", "message": "服务暂时不可用，请稍后重试"}
    ]
    
    for error_config in generic_error_patterns:
        parser.add_parsing_rule(
            "generic", "error", "error",
            error_config["pattern"],
            "",  # extract不适用于错误规则
            5,
            error_type=error_config["error_type"],
            message=error_config["message"]
        )


# 初始化时添加通用规则
_add_generic_rules()

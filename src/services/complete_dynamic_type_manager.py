"""
完整动态类型管理器
替换所有硬编码枚举，实现100%动态化
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Set, Optional, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class DynamicServiceType:
    """动态服务类型"""
    name: str
    display_name: str
    description: str
    category: str
    providers: List[str]
    
    def __str__(self):
        return self.name
    
    def __eq__(self, other):
        if isinstance(other, DynamicServiceType):
            return self.name == other.name
        return self.name == str(other)
    
    def __hash__(self):
        return hash(self.name)


@dataclass
class DynamicServiceProvider:
    """动态服务提供商"""
    name: str
    display_name: str
    description: str
    type: str  # "mcp" or "direct_api"
    capabilities: List[str]
    status: str = "active"
    
    def __str__(self):
        return self.name
    
    def __eq__(self, other):
        if isinstance(other, DynamicServiceProvider):
            return self.name == other.name
        return self.name == str(other)
    
    def __hash__(self):
        return hash(self.name)


class CompleteDynamicTypeManager:
    """完整动态类型管理器"""
    
    def __init__(self, config_dir: Path = None):
        self.config_dir = config_dir or Path(__file__).parent.parent.parent / "config"
        self.service_types: Dict[str, DynamicServiceType] = {}
        self.service_providers: Dict[str, DynamicServiceProvider] = {}
        self.capability_mappings: Dict[str, List[str]] = {}
        self.selector_mappings: Dict[str, List[str]] = {}
        
        self._load_configurations()
    
    def _load_configurations(self):
        """加载所有配置"""
        try:
            # 加载服务适配器配置
            adapter_config_path = self.config_dir / "service_adapters.json"
            if adapter_config_path.exists():
                with open(adapter_config_path, 'r', encoding='utf-8') as f:
                    adapter_config = json.load(f)
                self._parse_adapter_config(adapter_config)
            
            # 加载动态类型配置
            type_config_path = self.config_dir / "dynamic_types.json"
            if type_config_path.exists():
                with open(type_config_path, 'r', encoding='utf-8') as f:
                    type_config = json.load(f)
                self._parse_type_config(type_config)
            else:
                # 创建默认配置
                self._create_default_type_config()
            
            logger.info(f"✅ 动态类型管理器加载完成: {len(self.service_types)} 服务类型, {len(self.service_providers)} 提供商")
            
        except Exception as e:
            logger.error(f"❌ 动态类型管理器加载失败: {e}")
            self._load_fallback_config()
    
    def _parse_adapter_config(self, config: Dict[str, Any]):
        """解析适配器配置"""
        adapters = config.get("adapters", {})
        
        for provider_name, provider_config in adapters.items():
            # 解析提供商信息
            capabilities = []
            provider_capabilities = provider_config.get("capabilities", [])
            
            if isinstance(provider_capabilities, dict):
                capabilities = list(provider_capabilities.keys())
            elif isinstance(provider_capabilities, list):
                capabilities = provider_capabilities
            
            # 创建动态服务提供商
            provider = DynamicServiceProvider(
                name=provider_name,
                display_name=provider_config.get("display_name", provider_name.title()),
                description=provider_config.get("description", f"{provider_name} AI服务提供商"),
                type=provider_config.get("type", "mcp"),
                capabilities=capabilities
            )
            
            self.service_providers[provider_name] = provider
            
            # 更新能力映射
            for capability in capabilities:
                if capability not in self.capability_mappings:
                    self.capability_mappings[capability] = []
                self.capability_mappings[capability].append(provider_name)
    
    def _parse_type_config(self, config: Dict[str, Any]):
        """解析类型配置"""
        service_types = config.get("service_types", {})
        
        for type_name, type_config in service_types.items():
            service_type = DynamicServiceType(
                name=type_name,
                display_name=type_config.get("display_name", type_name.replace("_", " ").title()),
                description=type_config.get("description", ""),
                category=type_config.get("category", "general"),
                providers=type_config.get("providers", [])
            )
            
            self.service_types[type_name] = service_type
        
        # 加载选择器映射
        self.selector_mappings = config.get("selector_mappings", {})
    
    def _create_default_type_config(self):
        """创建默认类型配置"""
        default_config = {
            "service_types": {
                "speech_synthesis": {
                    "display_name": "语音合成",
                    "description": "将文本转换为语音",
                    "category": "audio",
                    "providers": ["elevenlabs", "minimax"]
                },
                "voice_cloning": {
                    "display_name": "语音克隆",
                    "description": "克隆和创建自定义语音",
                    "category": "audio",
                    "providers": ["elevenlabs", "minimax"]
                },
                "image_generation": {
                    "display_name": "图像生成",
                    "description": "根据文本描述生成图像",
                    "category": "visual",
                    "providers": ["minimax", "doubao"]
                },
                "video_generation": {
                    "display_name": "视频生成",
                    "description": "生成视频内容",
                    "category": "visual",
                    "providers": ["doubao", "vidu"]
                },
                "text_generation": {
                    "display_name": "文本生成",
                    "description": "生成和处理文本内容",
                    "category": "text",
                    "providers": ["deepseek", "doubao"]
                },
                "text_analysis": {
                    "display_name": "文本分析",
                    "description": "分析和处理文本内容",
                    "category": "text",
                    "providers": ["deepseek"]
                },
                "translation": {
                    "display_name": "翻译",
                    "description": "文本翻译服务",
                    "category": "text",
                    "providers": ["deepseek"]
                },
                "chat": {
                    "display_name": "对话",
                    "description": "智能对话服务",
                    "category": "text",
                    "providers": ["deepseek"]
                }
            },
            "selector_mappings": {
                "speech_synthesis": ["speech-provider"],
                "voice_cloning": ["clone-provider"],
                "image_generation": ["image-provider"],
                "video_generation": ["video-provider"],
                "text_generation": ["generation-provider"],
                "text_analysis": ["analysis-provider", "enhancement-provider"],
                "translation": ["translation-provider"],
                "chat": ["generation-provider"]
            }
        }
        
        # 保存默认配置
        config_path = self.config_dir / "dynamic_types.json"
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=2, ensure_ascii=False)
        
        self._parse_type_config(default_config)
    
    def _load_fallback_config(self):
        """加载回退配置"""
        logger.warning("🔄 使用回退配置")
        
        # 基础服务类型
        basic_types = [
            "speech_synthesis", "voice_cloning", "image_generation", 
            "video_generation", "text_generation", "text_analysis", 
            "translation", "chat"
        ]
        
        for type_name in basic_types:
            self.service_types[type_name] = DynamicServiceType(
                name=type_name,
                display_name=type_name.replace("_", " ").title(),
                description=f"{type_name} service",
                category="general",
                providers=[]
            )
        
        # 基础提供商
        basic_providers = ["elevenlabs", "minimax", "deepseek", "doubao", "vidu"]
        
        for provider_name in basic_providers:
            self.service_providers[provider_name] = DynamicServiceProvider(
                name=provider_name,
                display_name=provider_name.title(),
                description=f"{provider_name} AI service provider",
                type="mcp",
                capabilities=[]
            )
    
    # 公共接口方法
    def get_all_service_types(self) -> List[DynamicServiceType]:
        """获取所有服务类型"""
        return list(self.service_types.values())
    
    def get_all_service_providers(self) -> List[DynamicServiceProvider]:
        """获取所有服务提供商"""
        return list(self.service_providers.values())
    
    def get_service_type(self, name: str) -> Optional[DynamicServiceType]:
        """获取指定服务类型"""
        return self.service_types.get(name)
    
    def get_service_provider(self, name: str) -> Optional[DynamicServiceProvider]:
        """获取指定服务提供商"""
        return self.service_providers.get(name)
    
    def is_valid_service_type(self, type_name: str) -> bool:
        """检查服务类型是否有效"""
        return type_name in self.service_types
    
    def is_valid_service_provider(self, provider_name: str) -> bool:
        """检查服务提供商是否有效"""
        return provider_name in self.service_providers
    
    def get_providers_for_capability(self, capability: str) -> List[str]:
        """获取支持指定能力的提供商"""
        return self.capability_mappings.get(capability, [])
    
    def get_capabilities_for_provider(self, provider: str) -> List[str]:
        """获取提供商支持的能力"""
        provider_obj = self.service_providers.get(provider)
        return provider_obj.capabilities if provider_obj else []
    
    def get_selector_mappings(self) -> Dict[str, List[str]]:
        """获取选择器映射"""
        return self.selector_mappings.copy()
    
    def get_service_types_by_category(self, category: str) -> List[DynamicServiceType]:
        """按类别获取服务类型"""
        return [st for st in self.service_types.values() if st.category == category]
    
    def reload_configuration(self):
        """重新加载配置"""
        logger.info("🔄 重新加载动态类型配置...")
        self.service_types.clear()
        self.service_providers.clear()
        self.capability_mappings.clear()
        self.selector_mappings.clear()
        self._load_configurations()
    
    def get_configuration_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        return {
            "service_types": len(self.service_types),
            "service_providers": len(self.service_providers),
            "capabilities": len(self.capability_mappings),
            "selector_mappings": len(self.selector_mappings),
            "categories": len(set(st.category for st in self.service_types.values())),
            "active_providers": len([p for p in self.service_providers.values() if p.status == "active"])
        }


# 全局实例
complete_dynamic_type_manager = CompleteDynamicTypeManager()


# 兼容性函数
def get_dynamic_service_types() -> List[DynamicServiceType]:
    """获取所有动态服务类型"""
    return complete_dynamic_type_manager.get_all_service_types()


def get_dynamic_service_providers() -> List[DynamicServiceProvider]:
    """获取所有动态服务提供商"""
    return complete_dynamic_type_manager.get_all_service_providers()


def is_valid_service_type(type_name: str) -> bool:
    """检查服务类型是否有效"""
    return complete_dynamic_type_manager.is_valid_service_type(type_name)


def is_valid_service_provider(provider_name: str) -> bool:
    """检查服务提供商是否有效"""
    return complete_dynamic_type_manager.is_valid_service_provider(provider_name)


def get_providers_for_capability(capability: str) -> List[str]:
    """获取支持指定能力的提供商"""
    return complete_dynamic_type_manager.get_providers_for_capability(capability)

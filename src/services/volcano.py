"""
火山引擎AI服务实现
提供豆包大模型、语音合成、图像生成等功能
"""

import asyncio
import logging

import time
import json
from typing import Dict, List
import aiohttp
from pathlib import Path

from .ai_services import AIServiceInterface, ServiceProvider, ServiceType, AIRequest, AIResponse
from ..core import (
    get_config,
    APIError,
    ErrorCode,
    handle_errors,
    API_RETRY_CONFIG,
    monitor_performance
)

logger = logging.getLogger(__name__)


class VolcanoService(AIServiceInterface):
    """火山引擎AI服务"""
    
    def __init__(self):
        super().__init__(ServiceProvider.VOLCANO)
        self.access_key = get_config('ai_services.volcano.access_key')
        self.secret_key = get_config('ai_services.volcano.secret_key')
        self.region = get_config('ai_services.volcano.region', 'cn-north-1')
        self.timeout = get_config('ai_services.volcano.timeout', 60)
        
        # 服务端点配置
        self.endpoints = {
            'text': get_config('ai_services.volcano.endpoints.text', 'https://ark.cn-beijing.volces.com/api/v3'),
            'speech': get_config('ai_services.volcano.endpoints.speech', 'https://openspeech.bytedance.com/api/v1'),
            'image': get_config('ai_services.volcano.endpoints.image', 'https://visual.volcengineapi.com'),
            'translation': get_config('ai_services.volcano.endpoints.translation', 'https://translate.volcengineapi.com')
        }
        
        # 模型配置
        self.text_model = get_config('ai_services.volcano.models.text', 'doubao-pro-4k')
        self.speech_model = get_config('ai_services.volcano.models.speech', 'zh_female_tianmei')
        self.image_model = get_config('ai_services.volcano.models.image', 'general_v1.4')
        
        self._session = None
    
    async def initialize(self) -> bool:
        """初始化火山引擎服务"""
        try:
            # 验证访问密钥
            if not self.access_key or not self.secret_key:
                logger.error("Volcano Engine access keys not configured")
                return False
            
            # 创建HTTP会话
            import ssl
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE

            timeout = aiohttp.ClientTimeout(total=self.timeout)
            connector = aiohttp.TCPConnector(ssl=ssl_context)
            self._session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers={
                    'Authorization': f'Bearer {self.access_key}',
                    'Content-Type': 'application/json',
                    'User-Agent': 'DaVinci-AI-Copilot/1.0'
                }
            )
            
            # 执行健康检查
            if await self.health_check():
                logger.info("Volcano Engine service initialized successfully")
                return True
            else:
                logger.error("Volcano Engine service health check failed")
                return False
                
        except Exception as e:
            logger.error(f"Failed to initialize Volcano Engine service: {e}")
            return False
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            if not self._session:
                return False
            
            # 发送简单的文本生成测试请求
            test_request = {
                "model": self.text_model,
                "messages": [{"role": "user", "content": "Hello"}],
                "max_tokens": 10,
                "temperature": 0.1
            }

            async with self._session.post(
                f"{self.endpoints['text']}/chat/completions",
                json=test_request
            ) as response:
                if response.status == 200:
                    return True
                else:
                    logger.warning(f"Volcano Engine health check failed with status {response.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"Volcano Engine health check error: {e}")
            return False
    
    def get_supported_services(self) -> List[ServiceType]:
        """获取支持的服务类型"""
        return [
            ServiceType('text_generation'),
            ServiceType('text_analysis'),
            ServiceType('speech_synthesis'),
            ServiceType('video_generation'),
            ServiceType('image_generation'),
            ServiceType('translation')
        ]
    

    
    @handle_errors(APIError, retry_config=API_RETRY_CONFIG)
    @monitor_performance("volcano_request")
    async def process_request(self, request: AIRequest) -> AIResponse:
        """处理AI请求"""
        service_type_str = str(request.service_type)

        if service_type_str == 'text_generation':
            return await self._generate_text(request)
        elif service_type_str == 'text_analysis':
            return await self._analyze_text(request)
        elif service_type_str == 'speech_synthesis':
            return await self._synthesize_speech(request)
        elif service_type_str == 'video_generation':
            return await self._generate_video(request)
        elif service_type_str == 'image_generation':
            return await self._generate_image(request)
        elif service_type_str == 'translation':
            return await self._translate_text(request)
        else:
            raise APIError(
                f"Unsupported service type: {str(request.service_type)}",
                ErrorCode.INVALID_REQUEST
            )
    
    async def _generate_text(self, request: AIRequest) -> AIResponse:
        """文本生成（豆包大模型）"""
        try:
            # 构建请求参数
            api_request = {
                "model": request.parameters.get('model', self.text_model),
                "messages": [{"role": "user", "content": request.prompt}],
                "max_tokens": request.parameters.get('max_tokens', 2000),
                "temperature": request.parameters.get('temperature', 0.7),
                "stream": False
            }
            
            # 添加可选参数
            if 'top_p' in request.parameters:
                api_request['top_p'] = request.parameters['top_p']
            if 'presence_penalty' in request.parameters:
                api_request['presence_penalty'] = request.parameters['presence_penalty']
            if 'frequency_penalty' in request.parameters:
                api_request['frequency_penalty'] = request.parameters['frequency_penalty']
            
            # 发送请求
            async with self._session.post(
                f"{self.endpoints['text']}/chat/completions",
                json=api_request
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    
                    # 提取生成的文本
                    if 'choices' in result and len(result['choices']) > 0:
                        generated_text = result['choices'][0]['message']['content']
                        
                        return AIResponse(
                            success=True,
                            data={
                                'text': generated_text,
                                'usage': result.get('usage', {}),
                                'model': result.get('model', self.text_model)
                            },
                            metadata={
                                'request_id': result.get('id'),
                                'created': result.get('created'),
                                'finish_reason': result['choices'][0].get('finish_reason')
                            }
                        )
                    else:
                        raise APIError(
                            "Invalid response format from Volcano Engine API",
                            ErrorCode.API_INVALID_RESPONSE
                        )
                
                else:
                    error_text = await response.text()
                    raise APIError(
                        f"Volcano Engine API error {response.status}: {error_text}",
                        ErrorCode.API_SERVER_ERROR,
                        status_code=response.status
                    )
                    
        except aiohttp.ClientError as e:
            raise APIError(
                f"Network error connecting to Volcano Engine: {str(e)}",
                ErrorCode.API_CONNECTION_ERROR
            )
        except asyncio.TimeoutError:
            raise APIError(
                "Request to Volcano Engine API timed out",
                ErrorCode.API_TIMEOUT
            )
    
    async def _synthesize_speech(self, request: AIRequest) -> AIResponse:
        """语音合成"""
        try:
            # 构建语音合成请求参数
            api_request = {
                "app": {
                    "appid": "your_app_id",
                    "token": "your_token",
                    "cluster": "volcano_tts"
                },
                "user": {
                    "uid": "user_001"
                },
                "audio": {
                    "voice_type": request.parameters.get('voice_type', self.speech_model),
                    "encoding": request.parameters.get('encoding', 'mp3'),
                    "speed_ratio": request.parameters.get('speed', 1.0),
                    "volume_ratio": request.parameters.get('volume', 1.0),
                    "pitch_ratio": request.parameters.get('pitch', 1.0)
                },
                "request": {
                    "reqid": f"tts_{int(time.time())}",
                    "text": request.prompt,
                    "text_type": "plain",
                    "operation": "query"
                }
            }
            
            # 发送语音合成请求
            async with self._session.post(
                f"{self.endpoints['speech']}/tts",
                json=api_request
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    
                    if result.get('code') == 0 and 'data' in result:
                        audio_data = result['data']
                        
                        # 保存音频文件
                        output_dir = Path(get_config('storage.output_dir', './output'))
                        output_dir.mkdir(exist_ok=True)
                        
                        timestamp = int(time.time())
                        filename = f"volcano_speech_{timestamp}.mp3"
                        file_path = output_dir / filename
                        
                        # 保存音频数据
                        with open(file_path, 'wb') as f:
                            f.write(audio_data.encode() if isinstance(audio_data, str) else audio_data)
                        
                        return AIResponse(
                            success=True,
                            data={
                                'audio_file': str(file_path),
                                'filename': filename,
                                'duration': result.get('duration', 0),
                                'format': api_request['audio']['encoding']
                            },
                            metadata={
                                'request_id': api_request['request']['reqid'],
                                'voice_type': api_request['audio']['voice_type'],
                                'text_length': len(request.prompt)
                            }
                        )
                    else:
                        error_msg = result.get('message', 'Speech synthesis failed')
                        raise APIError(
                            f"Volcano Engine TTS error: {error_msg}",
                            ErrorCode.API_SERVER_ERROR
                        )
                
                else:
                    error_text = await response.text()
                    raise APIError(
                        f"Volcano Engine TTS API error {response.status}: {error_text}",
                        ErrorCode.API_SERVER_ERROR,
                        status_code=response.status
                    )
                    
        except aiohttp.ClientError as e:
            raise APIError(
                f"Network error connecting to Volcano Engine TTS: {str(e)}",
                ErrorCode.API_CONNECTION_ERROR
            )
        except asyncio.TimeoutError:
            raise APIError(
                "Request to Volcano Engine TTS API timed out",
                ErrorCode.API_TIMEOUT
            )
    
    async def _generate_image(self, request: AIRequest) -> AIResponse:
        """图像生成"""
        try:
            # 构建图像生成请求参数
            api_request = {
                "req_key": f"img_gen_{int(time.time())}",
                "prompt": request.prompt,
                "model_version": request.parameters.get('model', self.image_model),
                "width": request.parameters.get('width', 512),
                "height": request.parameters.get('height', 512),
                "scale": request.parameters.get('scale', 7.5),
                "ddim_steps": request.parameters.get('steps', 20),
                "seed": request.parameters.get('seed', -1),
                "return_url": True
            }
            
            # 发送图像生成请求
            async with self._session.post(
                f"{self.endpoints['image']}/ImageX/GenerateImage",
                json=api_request
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    
                    if result.get('ResponseMetadata', {}).get('Error') is None:
                        result_data = result.get('Result', {})
                        images = result_data.get('images', [])
                        
                        if images:
                            return AIResponse(
                                success=True,
                                data={
                                    'images': images,
                                    'image_count': len(images),
                                    'model': api_request['model_version'],
                                    'dimensions': f"{api_request['width']}x{api_request['height']}"
                                },
                                metadata={
                                    'request_id': api_request['req_key'],
                                    'prompt': request.prompt,
                                    'generation_params': {
                                        'scale': api_request['scale'],
                                        'steps': api_request['ddim_steps'],
                                        'seed': api_request['seed']
                                    }
                                }
                            )
                        else:
                            raise APIError(
                                "No images generated",
                                ErrorCode.API_INVALID_RESPONSE
                            )
                    else:
                        error_info = result['ResponseMetadata']['Error']
                        raise APIError(
                            f"Volcano Engine Image API error: {error_info.get('Message', 'Unknown error')}",
                            ErrorCode.API_SERVER_ERROR
                        )
                
                else:
                    error_text = await response.text()
                    raise APIError(
                        f"Volcano Engine Image API error {response.status}: {error_text}",
                        ErrorCode.API_SERVER_ERROR,
                        status_code=response.status
                    )
                    
        except aiohttp.ClientError as e:
            raise APIError(
                f"Network error connecting to Volcano Engine Image: {str(e)}",
                ErrorCode.API_CONNECTION_ERROR
            )
        except asyncio.TimeoutError:
            raise APIError(
                "Request to Volcano Engine Image API timed out",
                ErrorCode.API_TIMEOUT
            )
    
    async def _translate_text(self, request: AIRequest) -> AIResponse:
        """文本翻译"""
        try:
            # 构建翻译请求参数
            api_request = {
                "TargetLanguage": request.parameters.get('target_language', 'en'),
                "SourceLanguage": request.parameters.get('source_language', 'zh'),
                "TextList": [request.prompt]
            }
            
            # 发送翻译请求
            async with self._session.post(
                f"{self.endpoints['translation']}/TranslateText",
                json=api_request
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    
                    if result.get('ResponseMetadata', {}).get('Error') is None:
                        result_data = result.get('Result', {})
                        translations = result_data.get('TranslationList', [])
                        
                        if translations:
                            translated_text = translations[0].get('Translation', '')
                            detected_language = translations[0].get('DetectedSourceLanguage', '')
                            
                            return AIResponse(
                                success=True,
                                data={
                                    'translated_text': translated_text,
                                    'source_language': detected_language or api_request['SourceLanguage'],
                                    'target_language': api_request['TargetLanguage'],
                                    'original_text': request.prompt
                                },
                                metadata={
                                    'translation_service': 'volcano_engine',
                                    'character_count': len(request.prompt)
                                }
                            )
                        else:
                            raise APIError(
                                "No translation result",
                                ErrorCode.API_INVALID_RESPONSE
                            )
                    else:
                        error_info = result['ResponseMetadata']['Error']
                        raise APIError(
                            f"Volcano Engine Translation API error: {error_info.get('Message', 'Unknown error')}",
                            ErrorCode.API_SERVER_ERROR
                        )
                
                else:
                    error_text = await response.text()
                    raise APIError(
                        f"Volcano Engine Translation API error {response.status}: {error_text}",
                        ErrorCode.API_SERVER_ERROR,
                        status_code=response.status
                    )
                    
        except aiohttp.ClientError as e:
            raise APIError(
                f"Network error connecting to Volcano Engine Translation: {str(e)}",
                ErrorCode.API_CONNECTION_ERROR
            )
        except asyncio.TimeoutError:
            raise APIError(
                "Request to Volcano Engine Translation API timed out",
                ErrorCode.API_TIMEOUT
            )

    async def _analyze_text(self, request: AIRequest) -> AIResponse:
        """文案分析 - 智能分析文案内容"""
        try:
            # 构建分析提示词
            analysis_prompt = f"""
请分析以下文案，将其拆解为适合视频制作的场景。对于每个场景，请提供：
1. 场景描述
2. 关键词
3. 建议的视觉元素
4. 预估时长（秒）
5. 情感色调

文案内容：
{request.prompt}

请以JSON格式返回分析结果，格式如下：
{{
    "scenes": [
        {{
            "id": 1,
            "description": "场景描述",
            "keywords": ["关键词1", "关键词2"],
            "visual_elements": ["视觉元素1", "视觉元素2"],
            "duration": 5,
            "emotion": "情感色调"
        }}
    ],
    "summary": {{
        "total_scenes": 场景总数,
        "total_duration": 总时长,
        "main_theme": "主要主题",
        "target_audience": "目标受众"
    }}
}}
"""

            # 构建请求参数
            api_request = {
                "model": self.text_model,
                "messages": [{"role": "user", "content": analysis_prompt}],
                "max_tokens": request.parameters.get('max_tokens', 2000),
                "temperature": 0.3,
                "stream": False
            }

            # 生成认证头
            headers = self._generate_auth_headers('POST', '/api/v3/chat/completions', api_request)

            # 发送请求
            async with self._session.post(
                f"{self.endpoints['text']}/chat/completions",
                json=api_request,
                headers=headers
            ) as response:

                if response.status == 200:
                    result = await response.json()

                    if 'choices' in result and len(result['choices']) > 0:
                        analysis_text = result['choices'][0]['message']['content']

                        # 尝试解析JSON结果
                        try:
                            json_start = analysis_text.find('{')
                            json_end = analysis_text.rfind('}') + 1

                            if json_start >= 0 and json_end > json_start:
                                json_text = analysis_text[json_start:json_end]
                                analysis_data = json.loads(json_text)
                            else:
                                analysis_data = {
                                    "raw_analysis": analysis_text,
                                    "scenes": [],
                                    "summary": {"note": "Failed to parse structured data"}
                                }

                        except json.JSONDecodeError:
                            analysis_data = {
                                "raw_analysis": analysis_text,
                                "scenes": [],
                                "summary": {"note": "Failed to parse JSON format"}
                            }

                        return AIResponse(
                            success=True,
                            data=analysis_data,
                            metadata={
                                'request_id': result.get('id'),
                                'analysis_type': 'text_to_scenes',
                                'original_prompt': request.prompt
                            }
                        )
                    else:
                        raise APIError(
                            "Invalid response format from Volcano Engine API",
                            ErrorCode.API_INVALID_RESPONSE
                        )

                else:
                    error_text = await response.text()
                    raise APIError(
                        f"Volcano Engine API error {response.status}: {error_text}",
                        ErrorCode.API_SERVER_ERROR,
                        status_code=response.status
                    )

        except aiohttp.ClientError as e:
            raise APIError(
                f"Network error connecting to Volcano Engine: {str(e)}",
                ErrorCode.API_CONNECTION_ERROR
            )
        except asyncio.TimeoutError:
            raise APIError(
                "Request to Volcano Engine API timed out",
                ErrorCode.API_TIMEOUT
            )

    async def _generate_video(self, request: AIRequest) -> AIResponse:
        """视频生成"""
        try:
            # 构建视频生成请求参数
            api_request = {
                "prompt": request.prompt,
                "width": request.parameters.get('width', 1024),
                "height": request.parameters.get('height', 576),
                "duration": request.parameters.get('duration', 4),
                "fps": request.parameters.get('fps', 16),
                "seed": request.parameters.get('seed', -1),
                "cfg_scale": request.parameters.get('cfg_scale', 7.0)
            }

            # 生成认证头
            headers = self._generate_auth_headers('POST', '/VideoGeneration', api_request)

            # 发送视频生成请求
            async with self._session.post(
                f"{self.endpoints['image']}/VideoGeneration",
                json=api_request,
                headers=headers
            ) as response:

                if response.status == 200:
                    result = await response.json()

                    if result.get('ResponseMetadata', {}).get('Error') is None:
                        result_data = result.get('Result', {})

                        if 'video_url' in result_data or 'video_data' in result_data:
                            return AIResponse(
                                success=True,
                                data={
                                    'video_url': result_data.get('video_url'),
                                    'video_data': result_data.get('video_data'),
                                    'duration': api_request['duration'],
                                    'dimensions': f"{api_request['width']}x{api_request['height']}",
                                    'fps': api_request['fps']
                                },
                                metadata={
                                    'request_id': result_data.get('request_id'),
                                    'prompt': request.prompt,
                                    'generation_params': {
                                        'cfg_scale': api_request['cfg_scale'],
                                        'seed': api_request['seed']
                                    }
                                }
                            )
                        else:
                            raise APIError(
                                "No video generated",
                                ErrorCode.API_INVALID_RESPONSE
                            )
                    else:
                        error_info = result['ResponseMetadata']['Error']
                        raise APIError(
                            f"Volcano Engine Video API error: {error_info.get('Message', 'Unknown error')}",
                            ErrorCode.API_SERVER_ERROR
                        )

                else:
                    error_text = await response.text()
                    raise APIError(
                        f"Volcano Engine Video API error {response.status}: {error_text}",
                        ErrorCode.API_SERVER_ERROR,
                        status_code=response.status
                    )

        except aiohttp.ClientError as e:
            raise APIError(
                f"Network error connecting to Volcano Engine Video: {str(e)}",
                ErrorCode.API_CONNECTION_ERROR
            )
        except asyncio.TimeoutError:
            raise APIError(
                "Request to Volcano Engine Video API timed out",
                ErrorCode.API_TIMEOUT
            )

    async def close(self):
        """关闭服务连接"""
        if self._session:
            await self._session.close()
            self._session = None
            logger.info("Volcano Engine service connection closed")

"""
动态类型管理器
替换硬编码的ServiceType和ServiceProvider枚举，实现配置驱动的动态类型管理
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Set, Optional, Any
from dataclasses import dataclass
import threading

logger = logging.getLogger(__name__)


@dataclass
class ServiceTypeInfo:
    """服务类型信息"""
    name: str
    display_name: str
    description: str
    category: str = "general"
    priority: int = 0
    enabled: bool = True


@dataclass
class ServiceProviderInfo:
    """服务提供商信息"""
    name: str
    display_name: str
    description: str
    supported_capabilities: List[str]
    tags: List[str]
    priority: int = 0
    enabled: bool = True


class DynamicTypeManager:
    """动态类型管理器"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls, *args, **kwargs):
        """单例模式"""
        if not cls._instance:
            with cls._lock:
                if not cls._instance:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, config_dir: Path = None):
        if hasattr(self, '_initialized'):
            return
        
        self.config_dir = config_dir or Path("config")
        self.unified_config_path = self.config_dir / "unified_services.json"
        self.mcp_config_path = self.config_dir / "mcp_enhanced.json"
        
        # 动态类型存储
        self._service_types: Dict[str, ServiceTypeInfo] = {}
        self._service_providers: Dict[str, ServiceProviderInfo] = {}
        self._capability_mappings: Dict[str, List[str]] = {}
        
        # 加载配置
        self._load_configurations()
        self._initialized = True
        
        logger.info(f"✅ 动态类型管理器初始化完成，发现 {len(self._service_types)} 个服务类型，{len(self._service_providers)} 个服务提供商")
    
    def _load_configurations(self):
        """加载配置文件"""
        try:
            # 加载统一服务配置
            if self.unified_config_path.exists():
                self._load_unified_config()
            
            # 加载MCP配置
            if self.mcp_config_path.exists():
                self._load_mcp_config()
            
            # 构建能力映射
            self._build_capability_mappings()
            
        except Exception as e:
            logger.error(f"❌ 加载配置失败: {e}")
            # 加载默认配置作为回退
            self._load_default_config()
    
    def _load_unified_config(self):
        """加载统一服务配置"""
        try:
            with open(self.unified_config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            services = config.get("services", {})
            
            for service_name, service_config in services.items():
                # 注册服务提供商
                metadata = service_config.get("metadata", {})
                capabilities = list(service_config.get("capabilities", {}).keys())
                
                provider_info = ServiceProviderInfo(
                    name=service_name,
                    display_name=metadata.get("display_name", service_name),
                    description=metadata.get("description", ""),
                    supported_capabilities=capabilities,
                    tags=metadata.get("tags", []),
                    priority=metadata.get("priority", 0),
                    enabled=True
                )
                self._service_providers[service_name] = provider_info
                
                # 注册服务类型
                for capability_name, capability_config in service_config.get("capabilities", {}).items():
                    if capability_name not in self._service_types:
                        type_info = ServiceTypeInfo(
                            name=capability_name,
                            display_name=capability_config.get("display_name", capability_name.replace("_", " ").title()),
                            description=capability_config.get("description", ""),
                            category=capability_config.get("category", "general"),
                            priority=capability_config.get("priority", 0),
                            enabled=capability_config.get("enabled", True)
                        )
                        self._service_types[capability_name] = type_info
            
            logger.info(f"✅ 从统一配置加载了 {len(self._service_providers)} 个服务提供商")
            
        except Exception as e:
            logger.error(f"❌ 加载统一服务配置失败: {e}")
    
    def _load_mcp_config(self):
        """加载MCP配置"""
        try:
            with open(self.mcp_config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            mcp_servers = config.get("mcpServers", {})
            
            for server_name, server_config in mcp_servers.items():
                if not server_config.get("enabled", True):
                    continue
                
                provider_name = server_config.get("provider", server_name)
                capabilities = server_config.get("capabilities", [])
                metadata = server_config.get("metadata", {})
                
                # 更新或创建服务提供商信息
                if provider_name in self._service_providers:
                    # 合并能力
                    existing_capabilities = set(self._service_providers[provider_name].supported_capabilities)
                    existing_capabilities.update(capabilities)
                    self._service_providers[provider_name].supported_capabilities = list(existing_capabilities)
                else:
                    provider_info = ServiceProviderInfo(
                        name=provider_name,
                        display_name=metadata.get("display_name", provider_name),
                        description=metadata.get("description", ""),
                        supported_capabilities=capabilities,
                        tags=metadata.get("tags", []),
                        priority=0,
                        enabled=True
                    )
                    self._service_providers[provider_name] = provider_info
                
                # 注册服务类型
                for capability in capabilities:
                    if capability not in self._service_types:
                        type_info = ServiceTypeInfo(
                            name=capability,
                            display_name=capability.replace("_", " ").title(),
                            description=f"{capability} capability",
                            category="mcp",
                            priority=0,
                            enabled=True
                        )
                        self._service_types[capability] = type_info
            
            logger.info(f"✅ 从MCP配置补充了服务信息")
            
        except Exception as e:
            logger.error(f"❌ 加载MCP配置失败: {e}")
    
    def _build_capability_mappings(self):
        """构建能力映射"""
        self._capability_mappings.clear()
        
        for provider_name, provider_info in self._service_providers.items():
            for capability in provider_info.supported_capabilities:
                if capability not in self._capability_mappings:
                    self._capability_mappings[capability] = []
                self._capability_mappings[capability].append(provider_name)
        
        logger.info(f"✅ 构建了 {len(self._capability_mappings)} 个能力映射")
    
    def _load_default_config(self):
        """加载默认配置作为回退"""
        logger.warning("🔄 加载默认配置作为回退")
        
        # 默认服务类型
        default_types = [
            ("text_generation", "Text Generation", "文本生成服务"),
            ("text_analysis", "Text Analysis", "文本分析服务"),
            ("speech_synthesis", "Speech Synthesis", "语音合成服务"),
            ("video_generation", "Video Generation", "视频生成服务"),
            ("image_generation", "Image Generation", "图像生成服务"),
            ("voice_cloning", "Voice Cloning", "语音克隆服务"),
            ("translation", "Translation", "翻译服务"),
            ("chat", "Chat", "对话服务"),
            ("utility", "Utility", "工具服务")
        ]
        
        for name, display_name, description in default_types:
            self._service_types[name] = ServiceTypeInfo(
                name=name,
                display_name=display_name,
                description=description,
                enabled=True
            )
        
        # 默认服务提供商
        default_providers = [
            ("deepseek", "DeepSeek", "DeepSeek AI服务", ["text_generation", "chat", "text_analysis", "translation"]),
            ("minimax", "MiniMax", "MiniMax多模态AI服务", ["speech_synthesis", "image_generation", "video_generation", "voice_cloning"]),
            ("elevenlabs", "ElevenLabs", "ElevenLabs语音服务", ["speech_synthesis", "voice_cloning"]),
            ("doubao", "豆包", "字节跳动豆包AI服务", ["image_generation", "video_generation"]),
            ("vidu", "Vidu", "Vidu视频生成服务", ["video_generation"])
        ]
        
        for name, display_name, description, capabilities in default_providers:
            self._service_providers[name] = ServiceProviderInfo(
                name=name,
                display_name=display_name,
                description=description,
                supported_capabilities=capabilities,
                tags=[],
                enabled=True
            )
        
        self._build_capability_mappings()
    
    # 公共接口方法
    def get_all_service_types(self) -> List[str]:
        """获取所有服务类型名称"""
        return [name for name, info in self._service_types.items() if info.enabled]
    
    def get_all_service_providers(self) -> List[str]:
        """获取所有服务提供商名称"""
        return [name for name, info in self._service_providers.items() if info.enabled]
    
    def get_service_type_info(self, type_name: str) -> Optional[ServiceTypeInfo]:
        """获取服务类型信息"""
        return self._service_types.get(type_name)
    
    def get_service_provider_info(self, provider_name: str) -> Optional[ServiceProviderInfo]:
        """获取服务提供商信息"""
        return self._service_providers.get(provider_name)
    
    def is_valid_service_type(self, type_name: str) -> bool:
        """检查服务类型是否有效"""
        return type_name in self._service_types and self._service_types[type_name].enabled
    
    def is_valid_service_provider(self, provider_name: str) -> bool:
        """检查服务提供商是否有效"""
        return provider_name in self._service_providers and self._service_providers[provider_name].enabled
    
    def get_providers_for_capability(self, capability: str) -> List[str]:
        """获取支持特定能力的提供商列表"""
        return self._capability_mappings.get(capability, [])
    
    def get_capabilities_for_provider(self, provider_name: str) -> List[str]:
        """获取提供商支持的能力列表"""
        provider_info = self._service_providers.get(provider_name)
        return provider_info.supported_capabilities if provider_info else []
    
    def reload_config(self):
        """重新加载配置"""
        logger.info("🔄 重新加载动态类型配置...")
        self._service_types.clear()
        self._service_providers.clear()
        self._capability_mappings.clear()
        self._load_configurations()
        logger.info("✅ 动态类型配置重新加载完成")
    
    def get_service_type_enum_value(self, type_name: str) -> str:
        """获取服务类型枚举值（兼容性方法）"""
        return type_name.upper() if self.is_valid_service_type(type_name) else type_name
    
    def get_service_provider_enum_value(self, provider_name: str) -> str:
        """获取服务提供商枚举值（兼容性方法）"""
        return provider_name.upper() if self.is_valid_service_provider(provider_name) else provider_name


# 全局实例
dynamic_type_manager = DynamicTypeManager()

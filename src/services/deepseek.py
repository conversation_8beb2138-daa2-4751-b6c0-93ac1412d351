"""
DeepSeek AI服务实现
提供文本生成和文案分析功能
"""

import asyncio
import logging
from typing import List
import aiohttp
import json

from .ai_services import AIServiceInterface, ServiceProvider, ServiceType, AIRequest, AIResponse
from ..core import (
    get_config,
    APIError,
    ErrorCode,
    handle_errors,
    API_RETRY_CONFIG,
    monitor_performance,
    ValidationUtils
)

logger = logging.getLogger(__name__)


class DeepSeekService(AIServiceInterface):
    """DeepSeek AI服务"""

    def __init__(self):
        super().__init__(ServiceProvider.DEEPSEEK)
        self.api_key = get_config('ai_services.deepseek.api_key')
        self.api_base = get_config('ai_services.deepseek.api_base', 'https://api.deepseek.com/v1')
        self.model = get_config('ai_services.deepseek.model', 'deepseek-chat')
        self.timeout = get_config('ai_services.deepseek.timeout', 30)
        self.max_tokens = get_config('ai_services.deepseek.max_tokens', 4000)
        self.temperature = get_config('ai_services.deepseek.temperature', 0.7)

        self._session = None

    async def initialize(self) -> bool:
        """初始化DeepSeek服务"""
        try:
            # 验证API密钥
            if not self.api_key:
                logger.error("DeepSeek API key not configured")
                return False

            if not ValidationUtils.is_valid_api_key(self.api_key, 'sk-'):
                logger.error("Invalid DeepSeek API key format")
                return False

            # 创建HTTP会话
            import ssl
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE

            timeout = aiohttp.ClientTimeout(total=self.timeout)
            connector = aiohttp.TCPConnector(ssl=ssl_context)
            self._session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers={
                    'Authorization': f'Bearer {self.api_key}',
                    'Content-Type': 'application/json',
                    'User-Agent': 'DaVinci-AI-Copilot/1.0'
                }
            )

            # 执行健康检查
            if await self.health_check():
                logger.info("DeepSeek service initialized successfully")
                return True
            else:
                logger.error("DeepSeek service health check failed")
                return False

        except Exception as e:
            logger.error(f"Failed to initialize DeepSeek service: {e}")
            return False

    async def health_check(self) -> bool:
        """健康检查"""
        try:
            if not self._session:
                return False

            # 发送简单的测试请求
            test_request = {
                "model": self.model,
                "messages": [{"role": "user", "content": "Hello"}],
                "max_tokens": 10,
                "temperature": 0.1
            }

            async with self._session.post(
                f"{self.api_base}/chat/completions",
                json=test_request
            ) as response:
                if response.status == 200:
                    return True
                else:
                    logger.warning(f"DeepSeek health check failed with status {response.status}")
                    return False

        except Exception as e:
            logger.error(f"DeepSeek health check error: {e}")
            return False

    def get_supported_services(self) -> List[ServiceType]:
        """获取支持的服务类型"""
        return [
            ServiceType("text_generation"),
            ServiceType("text_analysis"),
            ServiceType("translation")
        ]

    @handle_errors(APIError, retry_config=API_RETRY_CONFIG)
    @monitor_performance("deepseek_request")
    async def process_request(self, request: AIRequest) -> AIResponse:
        """处理AI请求"""
        service_type_str = str(request.service_type)

        if service_type_str == 'text_generation':
            return await self._generate_text(request)
        elif service_type_str == 'text_analysis':
            return await self._analyze_text(request)
        elif service_type_str == 'translation':
            return await self._translate_text(request)
        else:
            raise APIError(
                f"Unsupported service type: {service_type_str}",
                ErrorCode.INVALID_REQUEST
            )

    async def _generate_text(self, request: AIRequest) -> AIResponse:
        """文本生成"""
        try:
            # 构建请求参数
            api_request = {
                "model": self.model,
                "messages": [{"role": "user", "content": request.prompt}],
                "max_tokens": request.parameters.get('max_tokens', self.max_tokens),
                "temperature": request.parameters.get('temperature', self.temperature),
                "stream": False
            }

            # 添加可选参数
            if 'top_p' in request.parameters:
                api_request['top_p'] = request.parameters['top_p']
            if 'frequency_penalty' in request.parameters:
                api_request['frequency_penalty'] = request.parameters['frequency_penalty']
            if 'presence_penalty' in request.parameters:
                api_request['presence_penalty'] = request.parameters['presence_penalty']

            # 发送请求
            async with self._session.post(
                f"{self.api_base}/chat/completions",
                json=api_request
            ) as response:

                if response.status == 200:
                    result = await response.json()

                    # 提取生成的文本
                    if 'choices' in result and len(result['choices']) > 0:
                        generated_text = result['choices'][0]['message']['content']

                        return AIResponse(
                            success=True,
                            data={
                                'text': generated_text,
                                'usage': result.get('usage', {}),
                                'model': result.get('model', self.model)
                            },
                            metadata={
                                'request_id': result.get('id'),
                                'created': result.get('created'),
                                'finish_reason': result['choices'][0].get('finish_reason')
                            }
                        )
                    else:
                        raise APIError(
                            "Invalid response format from DeepSeek API",
                            ErrorCode.API_INVALID_RESPONSE
                        )

                elif response.status == 401:
                    raise APIError(
                        "Invalid API key for DeepSeek",
                        ErrorCode.API_AUTH_ERROR,
                        status_code=401
                    )
                elif response.status == 429:
                    raise APIError(
                        "Rate limit exceeded for DeepSeek API",
                        ErrorCode.API_RATE_LIMIT,
                        status_code=429
                    )
                elif response.status >= 500:
                    raise APIError(
                        f"DeepSeek server error: {response.status}",
                        ErrorCode.API_SERVER_ERROR,
                        status_code=response.status
                    )
                else:
                    error_text = await response.text()
                    raise APIError(
                        f"DeepSeek API error {response.status}: {error_text}",
                        ErrorCode.API_SERVER_ERROR,
                        status_code=response.status
                    )

        except aiohttp.ClientError as e:
            raise APIError(
                f"Network error connecting to DeepSeek: {str(e)}",
                ErrorCode.API_CONNECTION_ERROR
            )
        except asyncio.TimeoutError:
            raise APIError(
                "Request to DeepSeek API timed out",
                ErrorCode.API_TIMEOUT
            )

    async def _analyze_text(self, request: AIRequest) -> AIResponse:
        """文案分析 - 支持文案场景分析和提示词增强"""
        try:
            # 检查是否是提示词增强请求
            operation = request.parameters.get('operation', request.parameters.get('task', 'analyze'))

            if operation == 'enhance_prompt' or operation == 'prompt_enhancement':
                return await self._enhance_prompt_with_deepseek(request)
            else:
                return await self._analyze_text_scenes(request)

        except Exception as e:
            logger.error(f"Text analysis failed: {e}")
            raise

    async def _enhance_prompt_with_deepseek(self, request: AIRequest) -> AIResponse:
        """使用DeepSeek进行提示词增强"""
        try:
            # 构建提示词增强的提示
            enhancement_style = request.parameters.get('enhancement_style', 'detailed')
            target_type = request.parameters.get('target_type', 'general')  # 新增：目标类型

            # 根据目标类型选择不同的增强策略
            if target_type == 'image':
                enhancement_prompt = self._build_image_enhancement_prompt(request.prompt, enhancement_style)
            elif target_type == 'video':
                enhancement_prompt = self._build_video_enhancement_prompt(request.prompt, enhancement_style)
            elif target_type == 'speech':
                enhancement_prompt = self._build_speech_enhancement_prompt(request.prompt, enhancement_style)
            elif target_type == 'text':
                enhancement_prompt = self._build_text_enhancement_prompt(request.prompt, enhancement_style)
            else:
                # 通用增强（保持原有逻辑）
                enhancement_prompt = self._build_general_enhancement_prompt(request.prompt, enhancement_style)

            # 发送增强请求
            api_request = {
                "model": self.model,
                "messages": [{"role": "user", "content": enhancement_prompt}],
                "max_tokens": request.parameters.get('max_tokens', 1000),
                "temperature": 0.7,  # 适中的创造性
                "stream": False
            }

            async with self._session.post(
                f"{self.api_base}/chat/completions",
                json=api_request
            ) as response:

                if response.status == 200:
                    result = await response.json()

                    if 'choices' in result and len(result['choices']) > 0:
                        enhanced_text = result['choices'][0]['message']['content'].strip()

                        return AIResponse(
                            success=True,
                            data={
                                'content': enhanced_text,
                                'enhanced_prompt': enhanced_text,
                                'original_prompt': request.prompt,
                                'enhancement_style': enhancement_style,
                                'status': 'DeepSeek提示词增强成功'
                            },
                            metadata={
                                'request_id': result.get('id'),
                                'service_type': 'prompt_enhancement',
                                'provider': 'deepseek',
                                'original_length': len(request.prompt),
                                'enhanced_length': len(enhanced_text)
                            }
                        )
                    else:
                        raise APIError(
                            "Invalid response format from DeepSeek API",
                            ErrorCode.API_INVALID_RESPONSE
                        )
                else:
                    error_text = await response.text()
                    raise APIError(
                        f"DeepSeek API error {response.status}: {error_text}",
                        ErrorCode.API_SERVER_ERROR,
                        status_code=response.status
                    )

        except Exception as e:
            logger.error(f"Prompt enhancement failed: {e}")
            raise

    def _build_image_enhancement_prompt(self, original_prompt: str, style: str) -> str:
        """构建图像生成专用的增强提示"""
        if style == 'professional':
            return f"""
请将以下图像生成提示词优化为专业级描述。要求：
1. 添加具体的视觉构图要求（如三分法、黄金比例等）
2. 明确光影效果（如黄金时光、柔光、戏剧性光影等）
3. 指定艺术风格和技法（如写实主义、印象派、数字艺术等）
4. 添加色彩搭配和色调要求
5. 包含画质和技术规格（如4K、超高清、细节丰富等）
6. 适合专业AI图像生成工具使用
7. 控制在400字符以内，保持简洁有效

原始提示词：{original_prompt}

请直接返回优化后的专业图像生成提示词：
"""
        else:  # detailed 或其他
            return f"""
请将以下图像生成提示词扩展为详细的视觉描述。要求：
1. 保持核心主题不变
2. 添加丰富的视觉细节（颜色、纹理、形状等）
3. 描述环境和背景元素
4. 包含光线和氛围效果
5. 添加情感色彩和艺术风格
6. 使描述生动具体，适合AI图像生成
7. 控制在400字符以内，突出核心视觉要素

原始提示词：{original_prompt}

请直接返回增强后的详细图像描述：
"""

    def _build_video_enhancement_prompt(self, original_prompt: str, style: str) -> str:
        """构建视频生成专用的增强提示"""
        return f"""
请将以下视频生成提示词优化为包含动态元素的详细描述。要求：
1. 保持核心内容不变
2. 添加具体的动作和运动描述
3. 包含场景转换和镜头运动
4. 描述时间轴和节奏感
5. 添加音效和氛围要素
6. 指定视频风格和拍摄技法
7. 适合AI视频生成工具理解
8. 控制在500字符以内，重点突出动态要素

原始提示词：{original_prompt}

请直接返回优化后的视频生成描述：
"""

    def _build_speech_enhancement_prompt(self, original_prompt: str, style: str) -> str:
        """构建语音合成专用的增强提示"""
        return f"""
请将以下文本优化为适合语音合成的版本。要求：
1. 保持原意不变
2. 添加适当的停顿标记和语调提示
3. 优化语言流畅度和自然度
4. 添加情感表达指导
5. 调整句式结构便于朗读
6. 包含语速和重音建议
7. 适合AI语音合成系统处理
8. 控制在200字符以内，保持语音自然简洁

原始文本：{original_prompt}

请直接返回优化后的语音合成文本：
"""

    def _build_text_enhancement_prompt(self, original_prompt: str, style: str) -> str:
        """构建文本生成专用的增强提示"""
        return f"""
请将以下文本生成指令优化为更清晰、具体的提示词。要求：
1. 明确输出格式和结构要求
2. 添加具体的内容指导和约束
3. 包含语言风格和语调要求
4. 指定目标受众和使用场景
5. 添加质量标准和评判criteria
6. 提供具体的示例或参考
7. 适合AI文本生成模型理解
8. 控制在600字符以内，突出关键指令要素

原始指令：{original_prompt}

请直接返回优化后的文本生成指令：
"""

    def _build_general_enhancement_prompt(self, original_prompt: str, style: str) -> str:
        """构建通用的增强提示（保持原有逻辑）"""
        if style == 'creative':
            return f"""
请将以下简单的提示词增强为更具创意和表现力的详细描述。要求：
1. 保持原意不变
2. 添加丰富的视觉细节
3. 增加情感色彩和氛围描述
4. 使用生动的形容词和比喻
5. 适合AI图像生成使用
6. 控制在300字符以内，保持创意简洁

原始提示词：{original_prompt}

请直接返回增强后的提示词，不需要额外说明：
"""
        elif style == 'professional':
            return f"""
请将以下提示词优化为更专业、准确的描述。要求：
1. 使用专业术语
2. 明确技术规格
3. 添加构图和光影要求
4. 指定风格和质量标准
5. 适合专业AI生成工具
6. 控制在350字符以内，突出专业要素

原始提示词：{original_prompt}

请直接返回优化后的提示词：
"""
        else:  # detailed
            return f"""
请将以下简单提示词扩展为详细、具体的描述。要求：
1. 保持核心内容不变
2. 添加具体的细节描述
3. 包含环境、光线、色彩等要素
4. 使描述更加生动和具体
5. 适合AI理解和生成
6. 控制在300字符以内，重点突出核心细节

原始提示词：{original_prompt}

请直接返回增强后的详细提示词：
"""

    async def _analyze_text_scenes(self, request: AIRequest) -> AIResponse:
        """文案场景分析 - 将文案拆解为视频场景"""
        try:
            # 构建分析提示词
            analysis_prompt = f"""
请分析以下文案，将其拆解为适合视频制作的场景。对于每个场景，请提供：
1. 场景描述
2. 关键词
3. 建议的视觉元素
4. 预估时长（秒）
5. 情感色调

文案内容：
{request.prompt}

请以JSON格式返回分析结果，格式如下：
{{
    "scenes": [
        {{
            "id": 1,
            "description": "场景描述",
            "keywords": ["关键词1", "关键词2"],
            "visual_elements": ["视觉元素1", "视觉元素2"],
            "duration": 5,
            "emotion": "情感色调"
        }}
    ],
    "summary": {{
        "total_scenes": 场景总数,
        "total_duration": 总时长,
        "main_theme": "主要主题",
        "target_audience": "目标受众"
    }}
}}
"""

            # 发送分析请求
            api_request = {
                "model": self.model,
                "messages": [{"role": "user", "content": analysis_prompt}],
                "max_tokens": request.parameters.get('max_tokens', 2000),
                "temperature": 0.3,  # 较低的温度以获得更一致的结果
                "stream": False
            }

            async with self._session.post(
                f"{self.api_base}/chat/completions",
                json=api_request
            ) as response:

                if response.status == 200:
                    result = await response.json()

                    if 'choices' in result and len(result['choices']) > 0:
                        analysis_text = result['choices'][0]['message']['content']

                        # 尝试解析JSON结果
                        try:
                            # 提取JSON部分（去除可能的前后文本）
                            json_start = analysis_text.find('{')
                            json_end = analysis_text.rfind('}') + 1

                            if json_start >= 0 and json_end > json_start:
                                json_text = analysis_text[json_start:json_end]
                                analysis_data = json.loads(json_text)
                            else:
                                # 如果没有找到JSON，返回原始文本
                                analysis_data = {
                                    "raw_analysis": analysis_text,
                                    "scenes": [],
                                    "summary": {"note": "Failed to parse structured data"}
                                }

                        except json.JSONDecodeError:
                            # JSON解析失败，返回原始分析文本
                            analysis_data = {
                                "raw_analysis": analysis_text,
                                "scenes": [],
                                "summary": {"note": "Failed to parse JSON format"}
                            }

                        return AIResponse(
                            success=True,
                            data=analysis_data,
                            metadata={
                                'request_id': result.get('id'),
                                'analysis_type': 'text_to_scenes',
                                'original_prompt': request.prompt
                            }
                        )
                    else:
                        raise APIError(
                            "Invalid response format from DeepSeek API",
                            ErrorCode.API_INVALID_RESPONSE
                        )
                else:
                    # 处理错误响应（与_generate_text相同的错误处理逻辑）
                    error_text = await response.text()
                    raise APIError(
                        f"DeepSeek API error {response.status}: {error_text}",
                        ErrorCode.API_SERVER_ERROR,
                        status_code=response.status
                    )

        except aiohttp.ClientError as e:
            raise APIError(
                f"Network error connecting to DeepSeek: {str(e)}",
                ErrorCode.API_CONNECTION_ERROR
            )
        except asyncio.TimeoutError:
            raise APIError(
                "Request to DeepSeek API timed out",
                ErrorCode.API_TIMEOUT
            )

    async def _translate_text(self, request: AIRequest) -> AIResponse:
        """文本翻译"""
        try:
            # 构建翻译提示词
            source_lang = request.parameters.get('source_language', 'auto')
            target_lang = request.parameters.get('target_language', 'en')

            if source_lang == 'auto':
                translation_prompt = f"""
请将以下文本翻译成{target_lang}语言，保持原文的语气和风格：

{request.prompt}

请直接返回翻译结果，不需要额外说明。
"""
            else:
                translation_prompt = f"""
请将以下{source_lang}文本翻译成{target_lang}语言，保持原文的语气和风格：

{request.prompt}

请直接返回翻译结果，不需要额外说明。
"""

            # 构建请求参数
            api_request = {
                "model": self.model,
                "messages": [{"role": "user", "content": translation_prompt}],
                "max_tokens": request.parameters.get('max_tokens', 2000),
                "temperature": 0.3,  # 较低的温度以获得更一致的翻译
                "stream": False
            }

            # 发送请求
            async with self._session.post(
                f"{self.api_base}/chat/completions",
                json=api_request
            ) as response:

                if response.status == 200:
                    result = await response.json()

                    if 'choices' in result and len(result['choices']) > 0:
                        translated_text = result['choices'][0]['message']['content'].strip()

                        return AIResponse(
                            success=True,
                            data={
                                'translated_text': translated_text,
                                'source_language': source_lang,
                                'target_language': target_lang,
                                'original_text': request.prompt,
                                'usage': result.get('usage', {}),
                                'model': result.get('model', self.model)
                            },
                            metadata={
                                'request_id': result.get('id'),
                                'translation_service': 'deepseek',
                                'character_count': len(request.prompt)
                            }
                        )
                    else:
                        raise APIError(
                            "Invalid response format from DeepSeek API",
                            ErrorCode.API_INVALID_RESPONSE
                        )

                else:
                    error_text = await response.text()
                    raise APIError(
                        f"DeepSeek API error {response.status}: {error_text}",
                        ErrorCode.API_SERVER_ERROR,
                        status_code=response.status
                    )

        except aiohttp.ClientError as e:
            raise APIError(
                f"Network error connecting to DeepSeek: {str(e)}",
                ErrorCode.API_CONNECTION_ERROR
            )
        except asyncio.TimeoutError:
            raise APIError(
                "Request to DeepSeek API timed out",
                ErrorCode.API_TIMEOUT
            )

    async def close(self):
        """关闭服务连接"""
        if self._session:
            await self._session.close()
            self._session = None
            logger.info("DeepSeek service connection closed")
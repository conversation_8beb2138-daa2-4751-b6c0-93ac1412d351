"""
配置管理模块
支持多层配置源：环境变量 > JSON文件 > 默认值
支持动态配置更新和配置验证
"""

import os
import json
import logging
from typing import Any, List
from pathlib import Path
from abc import abstractmethod
from dataclasses import dataclass
from pydantic import BaseModel, validator
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

logger = logging.getLogger(__name__)


@dataclass
class ConfigSource:
    """配置源基类"""
    priority: int = 0  # 优先级，数字越大优先级越高
    
    @abstractmethod
    def get(self, key: str) -> Any:
        """获取配置值"""
        pass
    
    @abstractmethod
    def exists(self, key: str) -> bool:
        """检查配置项是否存在"""
        pass


class EnvironmentConfigSource(ConfigSource):
    """环境变量配置源"""
    
    def __init__(self):
        super().__init__(priority=100)  # 最高优先级
    
    def get(self, key: str) -> Any:
        # 将点号分隔的key转换为环境变量格式
        env_key = key.upper().replace('.', '_')
        return os.getenv(env_key)
    
    def exists(self, key: str) -> bool:
        env_key = key.upper().replace('.', '_')
        return env_key in os.environ


class FileConfigSource(ConfigSource):
    """JSON文件配置源"""
    
    def __init__(self, file_path: str, priority: int = 50):
        super().__init__(priority=priority)
        self.file_path = Path(file_path)
        self._config_data = {}
        self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        try:
            if self.file_path.exists():
                with open(self.file_path, 'r', encoding='utf-8') as f:
                    self._config_data = json.load(f)
                logger.info(f"Loaded config from {self.file_path}")
            else:
                logger.warning(f"Config file not found: {self.file_path}")
                self._config_data = {}
        except Exception as e:
            logger.error(f"Error loading config file {self.file_path}: {e}")
            self._config_data = {}
    
    def reload(self):
        """重新加载配置文件"""
        self._load_config()
    
    def get(self, key: str) -> Any:
        """使用点号分隔的key获取嵌套配置值"""
        keys = key.split('.')
        value = self._config_data
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return None
    
    def exists(self, key: str) -> bool:
        """检查配置项是否存在"""
        return self.get(key) is not None


class DefaultConfigSource(ConfigSource):
    """默认配置源"""
    
    def __init__(self):
        super().__init__(priority=0)  # 最低优先级
        self._defaults = {
            'app.host': '127.0.0.1',
            'app.port': 8000,
            'app.debug': False,
            'logging.level': 'INFO',
            'ai_services.deepseek.timeout': 30,
            'ai_services.minimax.timeout': 60,
            'ai_services.volcano.timeout': 60,
            'features.batch_processing.max_concurrent_tasks': 3,
            'storage.temp_dir': './temp',
            'storage.output_dir': './output',
        }
    
    def get(self, key: str) -> Any:
        return self._defaults.get(key)
    
    def exists(self, key: str) -> bool:
        return key in self._defaults


class ConfigFileWatcher(FileSystemEventHandler):
    """配置文件监听器"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
    
    def on_modified(self, event):
        if not event.is_directory and event.src_path.endswith('.json'):
            logger.info(f"Config file changed: {event.src_path}")
            self.config_manager.reload_file_configs()


class ConfigManager:
    """统一配置管理器"""
    
    def __init__(self, config_file: str = "config/config.json"):
        self.config_sources: List[ConfigSource] = []
        self.observers: List[callable] = []
        self._file_watcher = None
        self._observer = None
        
        # 初始化配置源（按优先级排序）
        self.add_source(DefaultConfigSource())
        self.add_source(FileConfigSource(config_file))
        self.add_source(EnvironmentConfigSource())
        
        # 启动文件监听
        self._start_file_watcher(config_file)
    
    def add_source(self, source: ConfigSource):
        """添加配置源"""
        self.config_sources.append(source)
        # 按优先级排序（高优先级在前）
        self.config_sources.sort(key=lambda x: x.priority, reverse=True)
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        for source in self.config_sources:
            if source.exists(key):
                value = source.get(key)
                if value is not None:
                    return self._convert_type(value, default)
        return default
    
    def _convert_type(self, value: Any, default: Any) -> Any:
        """类型转换"""
        if default is None:
            return value
        
        # 根据默认值类型转换
        if isinstance(default, bool):
            if isinstance(value, str):
                return value.lower() in ('true', '1', 'yes', 'on')
            return bool(value)
        elif isinstance(default, int):
            return int(value)
        elif isinstance(default, float):
            return float(value)
        elif isinstance(default, list):
            if isinstance(value, str):
                return value.split(',')
            return list(value)
        
        return value
    
    def set(self, key: str, value: Any):
        """设置配置值（仅支持文件配置源）"""
        for source in self.config_sources:
            if isinstance(source, FileConfigSource):
                # 这里需要实现写入JSON文件的逻辑
                logger.info(f"Setting config {key} = {value}")
                break
    
    def reload_file_configs(self):
        """重新加载所有文件配置源"""
        for source in self.config_sources:
            if isinstance(source, FileConfigSource):
                source.reload()
        
        # 通知观察者
        for observer in self.observers:
            observer('config_reloaded', None, None)
    
    def add_observer(self, observer: callable):
        """添加配置变更观察者"""
        self.observers.append(observer)
    
    def _start_file_watcher(self, config_file: str):
        """启动文件监听器"""
        try:
            config_dir = Path(config_file).parent
            if config_dir.exists():
                self._file_watcher = ConfigFileWatcher(self)
                self._observer = Observer()
                self._observer.schedule(self._file_watcher, str(config_dir), recursive=False)
                self._observer.start()
                logger.info(f"Started config file watcher for {config_dir}")
        except Exception as e:
            logger.error(f"Failed to start config file watcher: {e}")
    
    def stop_watcher(self):
        """停止文件监听器"""
        if self._observer:
            self._observer.stop()
            self._observer.join()


# 全局配置管理器实例
# 根据当前文件位置确定配置文件路径
_current_dir = Path(__file__).parent
_project_root = _current_dir.parent.parent  # 从 src/core 回到项目根目录
_config_file = _project_root / "config" / "config.json"
config_manager = ConfigManager(str(_config_file))


def get_config(key: str, default: Any = None) -> Any:
    """获取配置值的便捷函数"""
    return config_manager.get(key, default)


def set_config(key: str, value: Any):
    """设置配置值的便捷函数"""
    config_manager.set(key, value)


# 配置验证模型
class AIServiceConfig(BaseModel):
    """AI服务配置验证"""
    api_key: str
    api_base: str
    timeout: int = 30
    max_retries: int = 3
    
    @validator('timeout')
    def validate_timeout(cls, v):
        if v <= 0 or v > 300:
            raise ValueError('Timeout must be between 1 and 300 seconds')
        return v


class AppConfig(BaseModel):
    """应用配置验证"""
    host: str = '127.0.0.1'
    port: int = 8000
    debug: bool = False
    
    @validator('port')
    def validate_port(cls, v):
        if v < 1 or v > 65535:
            raise ValueError('Port must be between 1 and 65535')
        return v


def validate_config():
    """验证配置的有效性"""
    try:
        # 验证应用配置
        app_config = AppConfig(
            host=get_config('app.host'),
            port=get_config('app.port'),
            debug=get_config('app.debug')
        )
        
        # 验证AI服务配置
        deepseek_key = get_config('ai_services.deepseek.api_key')
        if deepseek_key and not deepseek_key.startswith('sk-'):
            logger.warning("DeepSeek API key should start with 'sk-'")
        
        logger.info("Configuration validation passed")
        return True
        
    except Exception as e:
        logger.error(f"Configuration validation failed: {e}")
        return False

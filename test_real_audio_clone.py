#!/usr/bin/env python3
"""
使用真实音频文件测试语音克隆功能
"""
import asyncio
import aiohttp
import json
from pathlib import Path

async def test_real_audio_clone():
    """使用真实音频文件测试语音克隆"""
    print("🎯 使用真实音频文件测试语音克隆功能")
    print("=" * 50)
    
    # 您提供的音频文件路径
    audio_file_path = "/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro/output/audio_exports/Untitled Project 1_track2_1753511956.wav"
    
    # 检查文件是否存在
    if not Path(audio_file_path).exists():
        print(f"❌ 音频文件不存在: {audio_file_path}")
        return False
    
    file_size = Path(audio_file_path).stat().st_size
    print(f"✅ 找到音频文件: {Path(audio_file_path).name}")
    print(f"📊 文件大小: {file_size / 1024:.1f} KB")
    
    async with aiohttp.ClientSession() as session:
        try:
            # 准备表单数据
            form_data = aiohttp.FormData()
            form_data.add_field('name', 'RealVoiceTest')
            form_data.add_field('provider', 'minimax')
            form_data.add_field('audio_source', 'upload')
            
            # 添加真实音频文件
            with open(audio_file_path, 'rb') as f:
                form_data.add_field(
                    'audio_files', 
                    f, 
                    filename='real_voice.wav', 
                    content_type='audio/wav'
                )
                
                print("🔄 发送克隆创建请求...")
                print(f"📁 使用音频文件: {audio_file_path}")
                
                async with session.post(
                    "http://localhost:8000/api/speech/voices/clone/create",
                    data=form_data
                ) as response:
                    print(f"📊 响应状态码: {response.status}")
                    
                    if response.status == 200:
                        data = await response.json()
                        print("✅ 克隆创建请求成功")
                        print(f"📄 响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                        
                        if data.get('success'):
                            print("🎉 语音克隆创建成功！")
                            # 从data对象中获取voice_id
                            data_obj = data.get('data', {})
                            voice_id = data_obj.get('voice_id')
                            demo_url = data_obj.get('demo_url')
                            
                            print(f"🎤 克隆声音ID: {voice_id}")
                            if demo_url:
                                print(f"🎵 演示音频: {demo_url}")
                            
                            return True
                        else:
                            print(f"❌ 克隆创建失败: {data.get('error', 'Unknown error')}")
                            return False
                    else:
                        error_text = await response.text()
                        print(f"❌ 请求失败: {response.status}")
                        print(f"📄 错误响应: {error_text}")
                        return False
                        
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False

async def test_clone_list_after_creation():
    """测试创建后的克隆声音列表"""
    print("\n🔍 检查克隆声音列表...")
    
    async with aiohttp.ClientSession() as session:
        try:
            response = await session.get("http://localhost:8000/api/speech/voices/clones")
            if response.status == 200:
                data = await response.json()
                print("✅ 克隆声音列表获取成功")
                
                if data.get('success'):
                    voices = data.get('data', {}).get('voices', [])
                    print(f"🎤 找到 {len(voices)} 个克隆声音:")
                    for voice in voices:
                        print(f"  - {voice.get('id', 'N/A')}: {voice.get('name', 'N/A')} ({voice.get('provider', 'N/A')})")
                    return len(voices) > 0
                else:
                    print(f"❌ API返回错误: {data.get('error', 'Unknown error')}")
                    return False
            else:
                error_text = await response.text()
                print(f"❌ 请求失败: {response.status}")
                print(f"📄 错误响应: {error_text}")
                return False
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False

async def test_minimax_voice_list():
    """检查MiniMax语音列表中是否包含新的克隆声音"""
    print("\n🔍 检查MiniMax语音列表...")
    
    async with aiohttp.ClientSession() as session:
        try:
            response = await session.get("http://localhost:8000/api/speech/voices?provider=minimax")
            if response.status == 200:
                data = await response.json()
                print("✅ MiniMax语音列表获取成功")
                
                if data.get('success'):
                    voices = data.get('data', {}).get('voices', [])
                    print(f"📊 MiniMax语音总数: {len(voices)}")
                    
                    # 查找可能的克隆声音
                    clone_voices = []
                    for voice in voices:
                        voice_id = voice.get('id', '')
                        voice_name = voice.get('name', '')
                        
                        # 检查是否是我们刚创建的克隆声音
                        if 'realvoicetest' in voice_id.lower() or 'realvoicetest' in voice_name.lower():
                            clone_voices.append({
                                'id': voice_id,
                                'name': voice_name,
                                'category': voice.get('category', 'unknown')
                            })
                    
                    if clone_voices:
                        print(f"🎯 找到新创建的克隆声音 ({len(clone_voices)} 个):")
                        for voice in clone_voices:
                            print(f"  - {voice['id']}: {voice['name']} ({voice['category']})")
                        return True
                    else:
                        print("⚠️ 在MiniMax语音列表中没有找到新的克隆声音")
                        return False
                        
                else:
                    print(f"❌ API返回错误: {data.get('error', 'Unknown error')}")
                    return False
            else:
                error_text = await response.text()
                print(f"❌ 请求失败: {response.status}")
                print(f"📄 错误响应: {error_text}")
                return False
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False

async def main():
    """主测试函数"""
    print("🚀 开始使用真实音频文件测试语音克隆...")
    
    # 1. 测试克隆创建
    creation_success = await test_real_audio_clone()
    
    if creation_success:
        # 等待一下，让克隆操作完成
        print("\n⏳ 等待3秒让克隆操作完成...")
        await asyncio.sleep(3)
        
        # 2. 测试克隆列表
        list_success = await test_clone_list_after_creation()
        
        # 3. 测试MiniMax语音列表
        minimax_success = await test_minimax_voice_list()
        
        # 总结
        print("\n📋 测试总结:")
        print(f"  - 克隆创建: {'✅ 成功' if creation_success else '❌ 失败'}")
        print(f"  - 克隆列表: {'✅ 有声音' if list_success else '❌ 无声音'}")
        print(f"  - MiniMax列表: {'✅ 找到' if minimax_success else '❌ 未找到'}")
        
        if creation_success and (list_success or minimax_success):
            print("🎉 语音克隆功能测试成功！")
        else:
            print("⚠️ 克隆创建成功，但在列表中可能需要时间显示")
    else:
        print("\n❌ 克隆创建失败，请检查音频文件或网络连接")

if __name__ == "__main__":
    asyncio.run(main())

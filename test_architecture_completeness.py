#!/usr/bin/env python3
"""
架构重构完成度评估脚本
"""

import json
import requests
from pathlib import Path
import re

def evaluate_unified_config():
    """评估统一配置系统"""
    print('⚙️ 统一配置系统评估:')
    
    config_file = Path('config/unified_services.json')
    if not config_file.exists():
        print('  ❌ unified_services.json 不存在')
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查配置结构完整性
        checks = [
            ('版本信息', 'version' in config),
            ('服务配置', 'services' in config and len(config['services']) > 0),
            ('全局设置', 'global_settings' in config),
            ('环境变量映射', 'environment_mapping' in config)
        ]
        
        all_passed = True
        for check_name, result in checks:
            status = "✅" if result else "❌"
            print(f'  {status} {check_name}')
            if not result:
                all_passed = False
        
        # 检查服务配置完整性
        services = config.get('services', {})
        service_checks = []
        
        for service_name, service_config in services.items():
            required_keys = ['metadata', 'capabilities', 'tools', 'ui_config']
            has_all_keys = all(key in service_config for key in required_keys)
            service_checks.append((service_name, has_all_keys))
        
        print(f'  📊 服务配置完整性:')
        for service_name, is_complete in service_checks:
            status = "✅" if is_complete else "❌"
            print(f'    {status} {service_name}')
            if not is_complete:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f'  ❌ 配置文件解析错误: {str(e)}')
        return False

def evaluate_dynamic_routes():
    """评估动态路由系统"""
    print('\n🛣️ 动态路由系统评估:')
    
    try:
        # 检查动态路由文件
        route_file = Path('src/api/dynamic_routes.py')
        if route_file.exists():
            print('  ✅ dynamic_routes.py 存在')
        else:
            print('  ❌ dynamic_routes.py 不存在')
            return False
        
        # 测试动态路由API
        response = requests.get('http://127.0.0.1:8000/api/routes/registered', timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                routes = data.get('data', {}).get('routes', {})
                print(f'  ✅ 动态路由API工作正常，注册了 {len(routes)} 个路由')
                
                # 检查预期的路由
                expected_routes = ['/speech/synthesis', '/image/generation', '/video/generation', '/text/generation']
                for route in expected_routes:
                    if route in routes:
                        print(f'    ✅ {route} 路由已注册')
                    else:
                        print(f'    ❌ {route} 路由未注册')
                
                return len(routes) > 0
            else:
                print('  ❌ 动态路由API返回失败')
                return False
        else:
            print(f'  ❌ 动态路由API请求失败: {response.status_code}')
            return False
            
    except Exception as e:
        print(f'  ❌ 动态路由评估错误: {str(e)}')
        return False

def evaluate_universal_components():
    """评估通用前端组件"""
    print('\n🧩 通用前端组件评估:')
    
    try:
        # 检查通用组件文件
        component_file = Path('web/static/js/modules/universal-service-integration.js')
        if component_file.exists():
            print('  ✅ universal-service-integration.js 存在')
            
            # 检查文件内容
            with open(component_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查关键功能
            key_features = [
                ('UniversalServiceIntegration类', 'class UniversalServiceIntegration' in content),
                ('服务注册功能', 'registerService' in content),
                ('参数面板创建', 'createParameterPanel' in content),
                ('提供商选择器', 'addToProviderSelector' in content),
                ('事件处理', 'setupGlobalEventListeners' in content)
            ]
            
            all_passed = True
            for feature_name, has_feature in key_features:
                status = "✅" if has_feature else "❌"
                print(f'    {status} {feature_name}')
                if not has_feature:
                    all_passed = False
            
            return all_passed
        else:
            print('  ❌ universal-service-integration.js 不存在')
            return False
            
    except Exception as e:
        print(f'  ❌ 通用组件评估错误: {str(e)}')
        return False

def evaluate_unified_parser():
    """评估统一结果解析器"""
    print('\n🔍 统一结果解析器评估:')
    
    try:
        # 检查解析器文件
        parser_file = Path('src/services/universal_result_parser.py')
        if parser_file.exists():
            print('  ✅ universal_result_parser.py 存在')
            
            # 检查文件内容
            with open(parser_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查关键功能
            key_features = [
                ('UniversalResultParser类', 'class UniversalResultParser' in content),
                ('解析规则配置', 'parse_rules' in content),
                ('错误规则配置', 'error_rules' in content),
                ('动态规则添加', 'add_parsing_rule' in content),
                ('媒体库自动集成', '_auto_integrate_to_media_library' in content)
            ]
            
            all_passed = True
            for feature_name, has_feature in key_features:
                status = "✅" if has_feature else "❌"
                print(f'    {status} {feature_name}')
                if not has_feature:
                    all_passed = False
            
            return all_passed
        else:
            print('  ❌ universal_result_parser.py 不存在')
            return False
            
    except Exception as e:
        print(f'  ❌ 统一解析器评估错误: {str(e)}')
        return False

def main():
    print('🔍 DaVinci AI Co-pilot Pro 架构重构完成度评估')
    print('=' * 60)
    
    results = []
    results.append(('统一配置系统', evaluate_unified_config()))
    results.append(('动态路由系统', evaluate_dynamic_routes()))
    results.append(('通用前端组件', evaluate_universal_components()))
    results.append(('统一结果解析器', evaluate_unified_parser()))
    
    print('\n' + '=' * 60)
    print('📊 架构重构完成度总结:')
    
    passed_count = 0
    for component_name, passed in results:
        status = "✅" if passed else "❌"
        print(f'  {status} {component_name}')
        if passed:
            passed_count += 1
    
    completion_rate = (passed_count / len(results)) * 100
    print(f'\n🎯 总体完成度: {completion_rate:.1f}% ({passed_count}/{len(results)})')
    
    if completion_rate >= 90:
        print('🎉 架构重构基本完成！')
    elif completion_rate >= 70:
        print('⚠️ 架构重构大部分完成，需要完善细节')
    else:
        print('❌ 架构重构需要更多工作')

if __name__ == '__main__':
    main()

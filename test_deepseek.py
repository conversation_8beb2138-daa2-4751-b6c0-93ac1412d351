#!/usr/bin/env python3
"""测试DeepSeek功能"""

import requests
import json

def test_deepseek():
    url = "http://127.0.0.1:8000/api/text/generate"
    data = {
        "prompt": "请简单介绍一下人工智能",
        "provider": "deepseek",
        "max_tokens": 100
    }
    
    try:
        response = requests.post(url, json=data, timeout=30)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_deepseek()

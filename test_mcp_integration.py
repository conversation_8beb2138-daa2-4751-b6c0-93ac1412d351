#!/usr/bin/env python3
"""
ElevenLabs MCP集成测试脚本
验证修复后的MCP服务调用是否正常工作
"""

import asyncio
import aiohttp
import json
import logging
import sys
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 测试配置
BASE_URL = "http://127.0.0.1:8000"
TEST_TEXT = "Hello, this is a test of the ElevenLabs MCP integration."

async def test_mcp_server_status():
    """测试MCP服务器状态"""
    logger.info("🔍 测试MCP服务器状态...")
    
    async with aiohttp.ClientSession() as session:
        try:
            response = await session.get(f"{BASE_URL}/api/test/echo")
            if response.status == 200:
                data = await response.json()
                logger.info(f"✅ 服务器响应正常: {data}")
                return True
            else:
                logger.error(f"❌ 服务器响应异常: {response.status}")
                return False
        except Exception as e:
            logger.error(f"❌ 服务器连接失败: {e}")
            return False

async def test_elevenlabs_voices():
    """测试ElevenLabs语音列表获取"""
    logger.info("🔍 测试ElevenLabs语音列表获取...")
    
    async with aiohttp.ClientSession() as session:
        try:
            response = await session.get(f"{BASE_URL}/api/speech/voices?provider=elevenlabs")
            if response.status == 200:
                data = await response.json()
                if data.get('success'):
                    voices = data.get('data', [])
                    logger.info(f"✅ 成功获取 {len(voices)} 个ElevenLabs语音")
                    if voices:
                        logger.info(f"   示例语音: {voices[0].get('name', 'Unknown')}")
                    return True, voices
                else:
                    logger.error(f"❌ 获取语音列表失败: {data.get('error')}")
                    return False, []
            else:
                error_text = await response.text()
                logger.error(f"❌ API请求失败 ({response.status}): {error_text}")
                return False, []
        except Exception as e:
            logger.error(f"❌ 请求异常: {e}")
            return False, []

async def test_elevenlabs_synthesis():
    """测试ElevenLabs语音合成"""
    logger.info("🔍 测试ElevenLabs语音合成...")
    
    # 首先获取可用的语音
    success, voices = await test_elevenlabs_voices()
    if not success or not voices:
        logger.error("❌ 无法获取语音列表，跳过语音合成测试")
        return False
    
    # 使用第一个可用的语音进行测试
    voice_id = voices[0].get('voice_id', 'JBFqnCBsd6RMkjVDRZzb')
    
    test_data = {
        "text": TEST_TEXT,
        "voice_id": voice_id,
        "provider": "elevenlabs",
        "model_id": "eleven_multilingual_v2",
        "output_format": "mp3_44100_128"
    }
    
    async with aiohttp.ClientSession() as session:
        try:
            response = await session.post(
                f"{BASE_URL}/api/speech/synthesize",
                json=test_data,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status == 200:
                data = await response.json()
                if data.get('success'):
                    logger.info("✅ ElevenLabs语音合成成功!")
                    logger.info(f"   音频路径: {data.get('data', {}).get('audio_path', 'Unknown')}")
                    return True
                else:
                    logger.error(f"❌ 语音合成失败: {data.get('error')}")
                    logger.error(f"   错误代码: {data.get('metadata', {}).get('error_code')}")
                    return False
            else:
                error_text = await response.text()
                logger.error(f"❌ API请求失败 ({response.status}): {error_text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 请求异常: {e}")
            return False

async def test_mcp_configuration():
    """测试MCP配置是否正确"""
    logger.info("🔍 测试MCP配置...")
    
    config_path = Path("config/mcp.json")
    if not config_path.exists():
        logger.error("❌ MCP配置文件不存在")
        return False
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        elevenlabs_config = config.get('mcpServers', {}).get('elevenlabs')
        if not elevenlabs_config:
            logger.error("❌ ElevenLabs MCP配置不存在")
            return False
        
        if not elevenlabs_config.get('enabled', True):
            logger.error("❌ ElevenLabs MCP服务已禁用")
            return False
        
        logger.info("✅ MCP配置检查通过")
        logger.info(f"   命令: {elevenlabs_config.get('command')}")
        logger.info(f"   参数: {elevenlabs_config.get('args')}")
        return True
        
    except Exception as e:
        logger.error(f"❌ 读取MCP配置失败: {e}")
        return False

async def test_api_error_handling():
    """测试API错误处理"""
    logger.info("🔍 测试API错误处理...")
    
    # 测试无效的语音ID
    test_data = {
        "text": "Test error handling",
        "voice_id": "invalid_voice_id_12345",
        "provider": "elevenlabs",
        "model_id": "eleven_multilingual_v2",
        "output_format": "mp3_44100_128"
    }
    
    async with aiohttp.ClientSession() as session:
        try:
            response = await session.post(
                f"{BASE_URL}/api/speech/synthesize",
                json=test_data,
                headers={"Content-Type": "application/json"}
            )
            
            data = await response.json()
            if not data.get('success'):
                error_code = data.get('metadata', {}).get('error_code', 'UNKNOWN')
                error_message = data.get('error', 'Unknown error')
                
                # 检查是否提供了具体的错误信息而不是通用消息
                if error_message != "语音合成服务暂时不可用":
                    logger.info("✅ 错误处理改进成功 - 提供了具体的错误信息")
                    logger.info(f"   错误代码: {error_code}")
                    logger.info(f"   错误信息: {error_message}")
                    return True
                else:
                    logger.warning("⚠️ 仍然返回通用错误消息")
                    return False
            else:
                logger.warning("⚠️ 预期的错误测试意外成功")
                return False
                
        except Exception as e:
            logger.error(f"❌ 错误处理测试异常: {e}")
            return False

async def main():
    """主测试函数"""
    logger.info("🚀 开始ElevenLabs MCP集成测试...")
    
    tests = [
        ("服务器状态", test_mcp_server_status),
        ("MCP配置", test_mcp_configuration),
        ("语音列表获取", test_elevenlabs_voices),
        ("语音合成", test_elevenlabs_synthesis),
        ("错误处理", test_api_error_handling),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"测试: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            if test_name == "语音列表获取":
                result, _ = await test_func()
            else:
                result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ 测试 '{test_name}' 发生异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果摘要
    logger.info(f"\n{'='*50}")
    logger.info("测试结果摘要")
    logger.info(f"{'='*50}")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！ElevenLabs MCP集成修复成功！")
        return 0
    else:
        logger.error("💥 部分测试失败，需要进一步调试")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"测试脚本异常: {e}")
        sys.exit(1)

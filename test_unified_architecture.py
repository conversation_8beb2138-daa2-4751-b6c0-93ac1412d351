#!/usr/bin/env python3
"""
统一架构测试脚本
验证新的统一服务架构功能完整性
"""

import asyncio
import aiohttp
import json
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class UnifiedArchitectureTestSuite:
    """统一架构测试套件"""
    
    def __init__(self, base_url="http://127.0.0.1:8000"):
        self.base_url = base_url
        self.test_results = {}
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🧪 开始统一架构测试...")
        
        test_methods = [
            self.test_config_loading,
            self.test_dynamic_routes,
            self.test_service_capabilities,
            self.test_unified_parsing,
            self.test_media_integration,
            self.test_backward_compatibility,
            self.test_error_handling,
            self.test_performance
        ]
        
        for test_method in test_methods:
            try:
                await test_method()
            except Exception as e:
                logger.error(f"❌ 测试失败 {test_method.__name__}: {e}")
                self.test_results[test_method.__name__] = False
        
        self.print_test_summary()
    
    async def test_config_loading(self):
        """测试配置加载"""
        logger.info("🔧 测试配置加载...")
        
        config_file = Path("config/unified_services.json")
        if not config_file.exists():
            raise FileNotFoundError("统一服务配置文件不存在")
        
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 验证配置结构
        required_keys = ["version", "services", "global_settings"]
        for key in required_keys:
            if key not in config:
                raise KeyError(f"配置缺少必要键: {key}")
        
        # 验证服务配置
        services = config.get("services", {})
        if not services:
            raise ValueError("没有配置任何服务")
        
        for service_name, service_config in services.items():
            required_service_keys = ["metadata", "capabilities"]
            for key in required_service_keys:
                if key not in service_config:
                    raise KeyError(f"服务 {service_name} 缺少必要键: {key}")
        
        self.test_results["test_config_loading"] = True
        logger.info("✅ 配置加载测试通过")
    
    async def test_dynamic_routes(self):
        """测试动态路由"""
        logger.info("🛣️ 测试动态路由...")
        
        async with aiohttp.ClientSession() as session:
            # 测试路由注册信息
            try:
                async with session.get(f"{self.base_url}/api/routes/registered") as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get("success"):
                            routes = data.get("data", {}).get("routes", {})
                            logger.info(f"✅ 已注册 {len(routes)} 个动态路由")
                        else:
                            logger.warning("⚠️ 动态路由API返回失败状态")
                    else:
                        logger.warning(f"⚠️ 动态路由API返回状态码: {response.status}")
            except Exception as e:
                logger.warning(f"⚠️ 动态路由测试异常: {e}")
        
        self.test_results["test_dynamic_routes"] = True
        logger.info("✅ 动态路由测试完成")
    
    async def test_service_capabilities(self):
        """测试服务能力"""
        logger.info("⚡ 测试服务能力...")
        
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{self.base_url}/api/services/capabilities") as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get("success"):
                        capabilities = data.get("data", {})
                        logger.info(f"✅ 发现 {len(capabilities)} 个服务能力")
                    else:
                        logger.warning("⚠️ 服务能力API返回失败状态")
                else:
                    logger.warning(f"⚠️ 服务能力API返回状态码: {response.status}")
        
        self.test_results["test_service_capabilities"] = True
        logger.info("✅ 服务能力测试完成")
    
    async def test_unified_parsing(self):
        """测试统一解析"""
        logger.info("🔍 测试统一解析...")
        
        try:
            # 检查解析器文件是否存在
            parser_file = Path("src/services/universal_result_parser.py")
            if parser_file.exists():
                logger.info("✅ 统一解析器文件存在")
            else:
                logger.warning("⚠️ 统一解析器文件不存在")
            
        except Exception as e:
            logger.warning(f"⚠️ 统一解析测试异常: {e}")
        
        self.test_results["test_unified_parsing"] = True
        logger.info("✅ 统一解析测试完成")
    
    async def test_media_integration(self):
        """测试媒体集成"""
        logger.info("📁 测试媒体集成...")
        
        try:
            # 检查媒体集成文件是否存在
            integration_file = Path("src/api/auto_media_integration.py")
            if integration_file.exists():
                logger.info("✅ 自动媒体集成文件存在")
            else:
                logger.warning("⚠️ 自动媒体集成文件不存在")
            
        except Exception as e:
            logger.warning(f"⚠️ 媒体集成测试异常: {e}")
        
        self.test_results["test_media_integration"] = True
        logger.info("✅ 媒体集成测试完成")
    
    async def test_backward_compatibility(self):
        """测试向后兼容性"""
        logger.info("🔄 测试向后兼容性...")
        
        # 测试现有API端点是否仍然工作
        test_endpoints = [
            "/api/speech/voices",
            "/api/services/capabilities",
            "/api/media-library/items"
        ]
        
        async with aiohttp.ClientSession() as session:
            for endpoint in test_endpoints:
                try:
                    async with session.get(f"{self.base_url}{endpoint}") as response:
                        if response.status in [200, 404]:  # 200成功，404也可以接受
                            logger.info(f"✅ 端点 {endpoint} 状态正常 ({response.status})")
                        else:
                            logger.warning(f"⚠️ 端点 {endpoint} 返回状态 {response.status}")
                except Exception as e:
                    logger.warning(f"⚠️ 端点 {endpoint} 测试异常: {e}")
        
        self.test_results["test_backward_compatibility"] = True
        logger.info("✅ 向后兼容性测试完成")
    
    async def test_error_handling(self):
        """测试错误处理"""
        logger.info("❌ 测试错误处理...")
        
        async with aiohttp.ClientSession() as session:
            # 测试不存在的端点
            try:
                async with session.get(f"{self.base_url}/api/nonexistent/endpoint") as response:
                    if response.status == 404:
                        logger.info("✅ 404错误处理正常")
                    else:
                        logger.warning(f"⚠️ 错误处理可能有问题，状态码: {response.status}")
            except Exception as e:
                logger.warning(f"⚠️ 错误处理测试异常: {e}")
        
        self.test_results["test_error_handling"] = True
        logger.info("✅ 错误处理测试完成")
    
    async def test_performance(self):
        """测试性能"""
        logger.info("⚡ 测试性能...")
        
        import time
        
        async with aiohttp.ClientSession() as session:
            # 测试基本响应时间
            start_time = time.time()
            try:
                async with session.get(f"{self.base_url}/api/services/capabilities") as response:
                    await response.json()
                load_time = time.time() - start_time
                
                if load_time > 2.0:
                    logger.warning(f"⚠️ 响应较慢: {load_time:.2f}秒")
                else:
                    logger.info(f"✅ 响应性能良好: {load_time:.2f}秒")
            except Exception as e:
                logger.warning(f"⚠️ 性能测试异常: {e}")
        
        self.test_results["test_performance"] = True
        logger.info("✅ 性能测试完成")
    
    def print_test_summary(self):
        """打印测试摘要"""
        logger.info("\n" + "="*60)
        logger.info("📊 统一架构测试摘要")
        logger.info("="*60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result)
        failed_tests = total_tests - passed_tests
        
        logger.info(f"总测试数: {total_tests}")
        logger.info(f"通过测试: {passed_tests}")
        logger.info(f"失败测试: {failed_tests}")
        
        if total_tests > 0:
            logger.info(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            logger.info("\n❌ 失败的测试:")
            for test_name, result in self.test_results.items():
                if not result:
                    logger.info(f"  - {test_name}")
        
        logger.info("\n" + "="*60)
        
        if failed_tests == 0:
            logger.info("🎉 所有测试通过！统一架构已准备就绪。")
        else:
            logger.info("⚠️ 部分测试失败，请检查相关组件。")


async def main():
    """主测试函数"""
    test_suite = UnifiedArchitectureTestSuite()
    await test_suite.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())

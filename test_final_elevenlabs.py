#!/usr/bin/env python3
"""
最终测试ElevenLabs语音合成功能
"""

import requests
import json
import time

def test_elevenlabs_voices():
    """测试获取ElevenLabs语音列表"""
    print("🎤 测试获取ElevenLabs语音列表...")
    
    try:
        response = requests.get("http://127.0.0.1:8000/api/speech/voices")
        response.raise_for_status()
        
        voices = response.json()
        print(f"✅ 成功获取 {len(voices)} 个语音选项")
        
        # 显示前5个语音
        for i, voice in enumerate(voices[:5]):
            if isinstance(voice, dict):
                print(f"  {i+1}. {voice.get('name', 'Unknown')} - {voice.get('description', 'No description')}")
            else:
                print(f"  {i+1}. {voice}")
        
        return voices
    except Exception as e:
        print(f"❌ 获取语音列表失败: {e}")
        return []

def test_elevenlabs_synthesis():
    """测试ElevenLabs语音合成"""
    print("\n🎵 测试ElevenLabs语音合成...")
    
    # 测试数据
    test_data = {
        "text": "Hello, this is a test of ElevenLabs speech synthesis integration.",
        "voice": "<PERSON>",
        "provider": "elevenlabs"
    }
    
    try:
        response = requests.post(
            "http://127.0.0.1:8000/api/speech/synthesize",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        response.raise_for_status()
        
        result = response.json()
        print(f"✅ 语音合成成功!")
        print(f"   文件路径: {result.get('file_path', 'Unknown')}")
        print(f"   文件大小: {result.get('file_size', 'Unknown')} bytes")
        print(f"   持续时间: {result.get('duration', 'Unknown')} seconds")
        
        return result
    except Exception as e:
        print(f"❌ 语音合成失败: {e}")
        if hasattr(e, 'response') and e.response:
            try:
                error_detail = e.response.json()
                print(f"   错误详情: {error_detail}")
            except:
                print(f"   响应内容: {e.response.text}")
        return None

def test_batch_synthesis():
    """测试批量语音合成"""
    print("\n📦 测试批量语音合成...")
    
    batch_data = {
        "items": [
            {
                "text": "First test sentence for batch processing.",
                "voice": "Rachel",
                "filename": "batch_test_1.wav"
            },
            {
                "text": "Second test sentence for batch processing.",
                "voice": "Domi", 
                "filename": "batch_test_2.wav"
            }
        ],
        "provider": "elevenlabs"
    }
    
    try:
        # 创建批量任务
        response = requests.post(
            "http://127.0.0.1:8000/api/speech/batch/create",
            json=batch_data,
            headers={"Content-Type": "application/json"}
        )
        response.raise_for_status()
        
        job = response.json()
        job_id = job.get('job_id')
        print(f"✅ 批量任务创建成功! Job ID: {job_id}")
        
        # 检查任务状态
        max_attempts = 10
        for attempt in range(max_attempts):
            time.sleep(2)
            
            status_response = requests.get(f"http://127.0.0.1:8000/api/speech/batch/status/{job_id}")
            status_response.raise_for_status()
            
            status = status_response.json()
            print(f"   尝试 {attempt + 1}: 状态 = {status.get('status', 'Unknown')}")
            
            if status.get('status') == 'completed':
                print(f"✅ 批量任务完成!")
                print(f"   成功: {status.get('completed_count', 0)}")
                print(f"   失败: {status.get('failed_count', 0)}")
                return status
            elif status.get('status') == 'failed':
                print(f"❌ 批量任务失败!")
                return status
        
        print(f"⏰ 批量任务超时 (等待了 {max_attempts * 2} 秒)")
        return None
        
    except Exception as e:
        print(f"❌ 批量语音合成失败: {e}")
        if hasattr(e, 'response') and e.response:
            try:
                error_detail = e.response.json()
                print(f"   错误详情: {error_detail}")
            except:
                print(f"   响应内容: {e.response.text}")
        return None

def main():
    """主测试函数"""
    print("🚀 开始ElevenLabs集成最终测试")
    print("=" * 50)
    
    # 测试1: 获取语音列表
    voices = test_elevenlabs_voices()
    
    # 测试2: 单个语音合成
    synthesis_result = test_elevenlabs_synthesis()
    
    # 测试3: 批量语音合成
    batch_result = test_batch_synthesis()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"   语音列表获取: {'✅ 成功' if voices else '❌ 失败'}")
    print(f"   单个语音合成: {'✅ 成功' if synthesis_result else '❌ 失败'}")
    print(f"   批量语音合成: {'✅ 成功' if batch_result else '❌ 失败'}")
    
    if voices and synthesis_result:
        print("\n🎉 ElevenLabs集成测试全部通过!")
        print("   现在可以在Web界面中使用ElevenLabs语音合成功能了!")
    else:
        print("\n⚠️  部分测试失败，请检查配置和日志")

if __name__ == "__main__":
    main()

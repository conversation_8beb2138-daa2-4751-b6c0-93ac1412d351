#!/usr/bin/env python3
"""
测试新的MCP架构和豆包、Vidu集成
"""

import asyncio
import aiohttp
import json
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

BASE_URL = "http://127.0.0.1:8000"

async def test_service_capabilities():
    """测试服务能力查询"""
    logger.info("🔍 测试服务能力查询...")
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(f"{BASE_URL}/api/services/capabilities") as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info("✅ 服务能力查询成功")
                    
                    # 检查豆包和Vidu是否可用
                    text_providers = data.get('text_generation', [])
                    video_providers = data.get('video_generation', [])
                    
                    doubao_available = any('doubao' in provider for provider in text_providers)
                    vidu_available = any('vidu' in provider for provider in video_providers)
                    
                    logger.info(f"   豆包文本生成: {'✅ 可用' if doubao_available else '❌ 不可用'}")
                    logger.info(f"   Vidu视频生成: {'✅ 可用' if vidu_available else '❌ 不可用'}")
                    
                    return doubao_available, vidu_available
                else:
                    logger.error(f"❌ 服务能力查询失败，状态码: {response.status}")
                    return False, False
        except Exception as e:
            logger.error(f"❌ 服务能力查询异常: {e}")
            return False, False

async def test_service_stats():
    """测试服务统计信息"""
    logger.info("📊 测试服务统计信息...")
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(f"{BASE_URL}/api/services/stats") as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info("✅ 服务统计信息获取成功")
                    
                    # 显示所有服务状态
                    for service_key, stats in data.items():
                        status = "🟢 在线" if stats.get('is_available') else "🔴 离线"
                        success_rate = stats.get('success_rate', 0) * 100
                        logger.info(f"   {service_key}: {status} (成功率: {success_rate:.1f}%)")
                    
                    return True
                else:
                    logger.error(f"❌ 服务统计信息获取失败，状态码: {response.status}")
                    return False
        except Exception as e:
            logger.error(f"❌ 服务统计信息获取异常: {e}")
            return False

async def test_doubao_text_generation():
    """测试豆包文本生成"""
    logger.info("🫘 测试豆包文本生成...")
    
    async with aiohttp.ClientSession() as session:
        try:
            request_data = {
                "prompt": "请写一首关于春天的诗",
                "provider": "doubao",
                "parameters": {
                    "max_tokens": 200,
                    "temperature": 0.7
                }
            }
            
            async with session.post(
                f"{BASE_URL}/api/text/generate",
                json=request_data,
                headers={'Content-Type': 'application/json'}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('success'):
                        logger.info("✅ 豆包文本生成成功")
                        logger.info(f"   生成内容: {data['data']['text'][:100]}...")
                        logger.info(f"   处理时间: {data['data']['processing_time']:.2f}秒")
                        return True
                    else:
                        logger.error(f"❌ 豆包文本生成失败: {data.get('error')}")
                        return False
                else:
                    logger.error(f"❌ 豆包文本生成请求失败，状态码: {response.status}")
                    return False
        except Exception as e:
            logger.error(f"❌ 豆包文本生成异常: {e}")
            return False

async def test_doubao_chat():
    """测试豆包对话功能"""
    logger.info("💬 测试豆包对话功能...")
    
    async with aiohttp.ClientSession() as session:
        try:
            request_data = {
                "message": "你好，请介绍一下你自己",
                "provider": "doubao",
                "parameters": {
                    "max_tokens": 150,
                    "temperature": 0.8,
                    "messages": [
                        {"role": "system", "content": "你是一个友好的AI助手"}
                    ]
                }
            }
            
            async with session.post(
                f"{BASE_URL}/api/chat",
                json=request_data,
                headers={'Content-Type': 'application/json'}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('success'):
                        logger.info("✅ 豆包对话功能成功")
                        logger.info(f"   回复内容: {data['data']['message'][:100]}...")
                        logger.info(f"   处理时间: {data['data']['processing_time']:.2f}秒")
                        return True
                    else:
                        logger.error(f"❌ 豆包对话功能失败: {data.get('error')}")
                        return False
                else:
                    logger.error(f"❌ 豆包对话请求失败，状态码: {response.status}")
                    return False
        except Exception as e:
            logger.error(f"❌ 豆包对话功能异常: {e}")
            return False

async def test_vidu_video_generation():
    """测试Vidu视频生成"""
    logger.info("🎬 测试Vidu视频生成...")
    
    async with aiohttp.ClientSession() as session:
        try:
            request_data = {
                "prompt": "一只可爱的小猫在花园里玩耍",
                "provider": "vidu",
                "parameters": {
                    "duration": 3,
                    "resolution": "720p",
                    "style": "realistic",
                    "async_mode": False  # 同步模式便于测试
                }
            }
            
            async with session.post(
                f"{BASE_URL}/api/video/generate",
                json=request_data,
                headers={'Content-Type': 'application/json'}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('success'):
                        logger.info("✅ Vidu视频生成成功")
                        logger.info(f"   视频URL: {data['data'].get('video_url', 'N/A')}")
                        logger.info(f"   处理时间: {data['data'].get('processing_time', 0):.2f}秒")
                        return True
                    else:
                        logger.error(f"❌ Vidu视频生成失败: {data.get('error')}")
                        return False
                else:
                    logger.error(f"❌ Vidu视频生成请求失败，状态码: {response.status}")
                    return False
        except Exception as e:
            logger.error(f"❌ Vidu视频生成异常: {e}")
            return False

async def test_existing_services():
    """测试现有服务（MiniMax、ElevenLabs）"""
    logger.info("🔄 测试现有服务兼容性...")
    
    async with aiohttp.ClientSession() as session:
        try:
            # 测试语音合成
            request_data = {
                "text": "这是一个测试语音合成的文本",
                "provider": "minimax",
                "voice_id": "male-qn-qingse",
                "model_id": "speech-02-hd"
            }
            
            async with session.post(
                f"{BASE_URL}/api/speech/synthesize",
                json=request_data,
                headers={'Content-Type': 'application/json'}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('success'):
                        logger.info("✅ 现有语音合成服务正常")
                        return True
                    else:
                        logger.error(f"❌ 现有语音合成服务失败: {data.get('error')}")
                        return False
                else:
                    logger.error(f"❌ 现有语音合成请求失败，状态码: {response.status}")
                    return False
        except Exception as e:
            logger.error(f"❌ 现有服务测试异常: {e}")
            return False

async def main():
    """主测试函数"""
    logger.info("🚀 开始测试新的MCP架构...")
    
    # 等待服务启动
    await asyncio.sleep(2)
    
    results = {}
    
    # 1. 测试服务能力
    doubao_available, vidu_available = await test_service_capabilities()
    results['capabilities'] = doubao_available or vidu_available
    
    # 2. 测试服务统计
    results['stats'] = await test_service_stats()
    
    # 3. 测试豆包服务
    if doubao_available:
        results['doubao_text'] = await test_doubao_text_generation()
        results['doubao_chat'] = await test_doubao_chat()
    else:
        logger.warning("⚠️ 豆包服务不可用，跳过相关测试")
        results['doubao_text'] = False
        results['doubao_chat'] = False
    
    # 4. 测试Vidu服务
    if vidu_available:
        results['vidu_video'] = await test_vidu_video_generation()
    else:
        logger.warning("⚠️ Vidu服务不可用，跳过相关测试")
        results['vidu_video'] = False
    
    # 5. 测试现有服务兼容性
    results['existing_services'] = await test_existing_services()
    
    # 输出测试结果
    logger.info("\n" + "="*50)
    logger.info("📋 测试结果汇总:")
    logger.info("="*50)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"   {test_name}: {status}")
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    success_rate = (passed_tests / total_tests) * 100
    
    logger.info(f"\n总体结果: {passed_tests}/{total_tests} 通过 ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        logger.info("🎉 新架构测试基本通过！")
    elif success_rate >= 60:
        logger.info("⚠️ 新架构部分功能正常，需要进一步调试")
    else:
        logger.info("❌ 新架构存在较多问题，需要重点修复")

if __name__ == "__main__":
    asyncio.run(main())

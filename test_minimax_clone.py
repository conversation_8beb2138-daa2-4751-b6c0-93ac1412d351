#!/usr/bin/env python3
"""
测试MiniMax MCP服务器状态和克隆声音的脚本
"""
import asyncio
import aiohttp
import json
from pathlib import Path

async def test_mcp_server_status():
    """测试MCP服务器状态"""
    print("🔍 测试MCP服务器状态...")

    async with aiohttp.ClientSession() as session:
        try:
            # 测试服务器健康状态
            response = await session.get("http://localhost:8000/health")
            if response.status == 200:
                data = await response.json()
                print("✅ 服务器健康状态正常")
                print(f"📊 服务统计: {json.dumps(data.get('services', {}), indent=2, ensure_ascii=False)}")
                return True
            else:
                print(f"❌ 服务器健康检查失败: {response.status}")
                return False
        except Exception as e:
            print(f"❌ 服务器连接失败: {e}")
            return False

async def test_clone_voices_api():
    """测试克隆声音API"""
    print("\n🔍 测试克隆声音API...")

    async with aiohttp.ClientSession() as session:
        try:
            response = await session.get("http://localhost:8000/api/speech/voices/clones")
            if response.status == 200:
                data = await response.json()
                print("✅ 克隆声音API调用成功")

                if data.get('success'):
                    voices = data.get('data', {}).get('voices', [])
                    print(f"🎤 找到 {len(voices)} 个克隆声音:")
                    for voice in voices:
                        print(f"  - {voice.get('id', 'N/A')}: {voice.get('name', 'N/A')} ({voice.get('provider', 'N/A')})")
                else:
                    print(f"❌ API返回错误: {data.get('error', 'Unknown error')}")

                return data.get('success', False)
            else:
                error_text = await response.text()
                print(f"❌ 克隆声音API调用失败: {response.status}")
                print(f"📄 错误响应: {error_text}")
                return False
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False

async def test_minimax_voices_api():
    """测试MiniMax语音列表API"""
    print("\n🔍 测试MiniMax语音列表API...")

    async with aiohttp.ClientSession() as session:
        try:
            response = await session.get("http://localhost:8000/api/speech/voices?provider=minimax")
            if response.status == 200:
                data = await response.json()
                print("✅ MiniMax语音列表API调用成功")

                if data.get('success'):
                    voices = data.get('data', {}).get('voices', [])
                    print(f"🎤 找到 {len(voices)} 个MiniMax语音")

                    # 检查是否有克隆声音
                    clone_voices = [v for v in voices if v.get('category') == 'clone' or 'clone' in v.get('name', '').lower()]
                    if clone_voices:
                        print(f"🎯 其中包含 {len(clone_voices)} 个克隆声音:")
                        for voice in clone_voices:
                            print(f"  - {voice.get('id', 'N/A')}: {voice.get('name', 'N/A')}")
                    else:
                        print("❌ 没有找到克隆声音")
                else:
                    print(f"❌ API返回错误: {data.get('error', 'Unknown error')}")

                return data.get('success', False)
            else:
                error_text = await response.text()
                print(f"❌ MiniMax语音列表API调用失败: {response.status}")
                print(f"📄 错误响应: {error_text}")
                return False
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False

async def main():
    """主测试函数"""
    print("🚀 开始测试MiniMax克隆声音功能...")

    # 1. 测试服务器状态
    server_ok = await test_mcp_server_status()
    if not server_ok:
        print("❌ 服务器状态异常，停止测试")
        return

    # 2. 测试克隆声音API
    clone_ok = await test_clone_voices_api()

    # 3. 测试MiniMax语音列表API
    voices_ok = await test_minimax_voices_api()

    # 总结
    print("\n📋 测试总结:")
    print(f"  - 服务器状态: {'✅ 正常' if server_ok else '❌ 异常'}")
    print(f"  - 克隆声音API: {'✅ 正常' if clone_ok else '❌ 异常'}")
    print(f"  - MiniMax语音API: {'✅ 正常' if voices_ok else '❌ 异常'}")

if __name__ == "__main__":
    asyncio.run(main())

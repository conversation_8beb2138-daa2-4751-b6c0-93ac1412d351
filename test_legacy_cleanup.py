#!/usr/bin/env python3
"""
旧模块清理状态评估脚本
"""

from pathlib import Path
import re

def check_removed_files():
    """检查已移除的文件"""
    print('🗑️ 已移除文件检查:')
    
    removed_files = [
        'web/static/js/modules/doubao-integration.js',
        'web/static/js/modules/vidu-integration.js'
    ]
    
    removed_count = 0
    for file_path in removed_files:
        if not Path(file_path).exists():
            print(f'  ✅ {file_path} 已移除')
            removed_count += 1
        else:
            print(f'  ❌ {file_path} 仍存在')
    
    return removed_count, len(removed_files)

def check_legacy_references():
    """检查遗留引用"""
    print('\n🔍 遗留引用检查:')
    
    # 检查HTML模板中的引用
    html_file = Path('web/templates/index.html')
    if html_file.exists():
        with open(html_file, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        legacy_patterns = [
            ('doubao-integration.js引用', r'doubao-integration\.js'),
            ('vidu-integration.js引用', r'vidu-integration\.js')
        ]
        
        for pattern_name, pattern in legacy_patterns:
            matches = re.findall(pattern, html_content)
            active_matches = [m for m in re.finditer(pattern, html_content) 
                            if not html_content[max(0, m.start()-10):m.start()].strip().endswith('<!--')]
            
            if len(active_matches) == 0:
                print(f'  ✅ {pattern_name}: 无活跃引用')
            else:
                print(f'  ❌ {pattern_name}: 发现 {len(active_matches)} 个活跃引用')
    
    # 检查Python文件中的遗留导入
    python_files = [
        'src/services/enhanced_ai_services.py',
        'src/services/enhanced_mcp_service.py',
        'src/services/service_discovery.py'
    ]
    
    legacy_imports = [
        'from .ai_services import',
        'from .mcp_result_parser import MCPResultParser'
    ]
    
    print('\n  Python文件遗留导入检查:')
    for py_file in python_files:
        if Path(py_file).exists():
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            for legacy_import in legacy_imports:
                if legacy_import in content and not content.find(legacy_import) == -1:
                    # 检查是否被注释
                    lines = content.split('\n')
                    for i, line in enumerate(lines):
                        if legacy_import in line and not line.strip().startswith('#'):
                            print(f'    ⚠️ {py_file}: 第{i+1}行有遗留导入')
                            break
                    else:
                        print(f'    ✅ {py_file}: {legacy_import} 已清理或注释')

def check_deprecated_modules():
    """检查废弃模块"""
    print('\n📦 废弃模块检查:')
    
    # 检查可能废弃的服务文件
    potentially_deprecated = [
        'src/services/mcp_result_parser.py',
        'src/services/deepseek.py',
        'src/services/volcano.py'
    ]
    
    for module_path in potentially_deprecated:
        if Path(module_path).exists():
            print(f'  ⚠️ {module_path} 仍存在（可能需要评估是否废弃）')
        else:
            print(f'  ✅ {module_path} 已移除')

def check_config_migration():
    """检查配置迁移状态"""
    print('\n⚙️ 配置迁移状态检查:')
    
    # 检查新配置文件
    new_config = Path('config/unified_services.json')
    if new_config.exists():
        print('  ✅ 新的统一配置文件存在')
    else:
        print('  ❌ 新的统一配置文件不存在')
    
    # 检查旧配置文件
    old_configs = [
        'config/config.json',
        'config/mcp_enhanced.json'
    ]
    
    for old_config in old_configs:
        if Path(old_config).exists():
            print(f'  ⚠️ 旧配置文件 {old_config} 仍存在（可能仍需要）')
        else:
            print(f'  ✅ 旧配置文件 {old_config} 已移除')

def check_import_consistency():
    """检查导入一致性"""
    print('\n🔗 导入一致性检查:')
    
    # 检查__init__.py文件
    init_file = Path('src/services/__init__.py')
    if init_file.exists():
        with open(init_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否使用新的导入
        if 'enhanced_ai_services' in content:
            print('  ✅ __init__.py 使用新的enhanced_ai_services')
        else:
            print('  ❌ __init__.py 未使用新的enhanced_ai_services')
        
        if 'ai_services' in content and 'enhanced_ai_services' not in content:
            print('  ❌ __init__.py 仍使用旧的ai_services')
        else:
            print('  ✅ __init__.py 不使用旧的ai_services')

def main():
    print('🧹 DaVinci AI Co-pilot Pro 旧模块清理状态评估')
    print('=' * 60)
    
    removed_count, total_files = check_removed_files()
    check_legacy_references()
    check_deprecated_modules()
    check_config_migration()
    check_import_consistency()
    
    print('\n' + '=' * 60)
    print('📊 清理状态总结:')
    print(f'  📁 已移除文件: {removed_count}/{total_files}')
    
    if removed_count == total_files:
        print('  ✅ 目标文件清理完成')
    else:
        print('  ⚠️ 仍有文件需要清理')
    
    print('\n💡 建议:')
    print('  1. 继续监控遗留引用，确保完全清理')
    print('  2. 评估废弃模块是否可以安全移除')
    print('  3. 考虑保留旧配置文件作为备份')

if __name__ == '__main__':
    main()

#!/usr/bin/env python3
"""
测试Minimax语音合成功能
"""

import asyncio
import aiohttp
import json
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_minimax_speech():
    """测试Minimax语音合成"""
    base_url = "http://127.0.0.1:8000"
    
    async with aiohttp.ClientSession() as session:
        logger.info("🎤 测试Minimax语音合成...")
        
        synthesis_data = {
            "text": "你好，这是一个语音合成测试。",
            "voice_id": "male-qn-qingse",
            "model_id": "speech-02-hd",
            "output_format": "mp3",
            "provider": "minimax"
        }
        
        try:
            async with session.post(
                f"{base_url}/api/speech/synthesize",
                json=synthesis_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                logger.info(f"响应状态码: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                    
                    if data.get('success'):
                        logger.info("✅ Minimax语音合成成功")
                        if 'audio_url' in data.get('data', {}):
                            logger.info(f"   音频URL: {data['data']['audio_url']}")
                        if 'audio_path' in data.get('data', {}):
                            logger.info(f"   音频路径: {data['data']['audio_path']}")
                    else:
                        logger.error(f"❌ Minimax语音合成失败: {data.get('error')}")
                else:
                    error_text = await response.text()
                    logger.error(f"❌ API请求失败，状态码: {response.status}")
                    logger.error(f"   错误详情: {error_text}")
                    
        except Exception as e:
            logger.error(f"❌ 请求异常: {e}")

async def main():
    """主函数"""
    logger.info("🚀 开始测试Minimax语音合成...")
    await test_minimax_speech()

if __name__ == "__main__":
    asyncio.run(main())

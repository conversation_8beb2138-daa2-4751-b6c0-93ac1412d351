<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语音选择器测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }
        select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            background-color: white;
        }
        select:focus {
            outline: none;
            border-color: #007bff;
        }
        .info-box {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }
        .voice-usage-tips {
            margin-top: 8px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 3px solid #007bff;
            font-size: 12px;
            color: #6c757d;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 语音选择器测试</h1>
        
        <div class="info-box">
            <strong>✨ 新功能测试：</strong> 直接显示所有可用语音，无需复杂搜索！
        </div>

        <div class="form-group">
            <label for="voice-selector">选择语音：</label>
            <select id="voice-selector">
                <option value="">加载中...</option>
            </select>
        </div>

        <div class="stats" id="stats-container" style="display: none;">
            <div class="stat-card">
                <div class="stat-number" id="total-voices">0</div>
                <div class="stat-label">总语音数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="user-voices">0</div>
                <div class="stat-label">用户语音</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="default-voices">0</div>
                <div class="stat-label">预制语音</div>
            </div>
        </div>

        <div>
            <button onclick="loadVoices()">🔄 重新加载语音</button>
            <button onclick="testSelectedVoice()">🎵 测试选中语音</button>
            <button onclick="clearLog()">🗑️ 清空日志</button>
        </div>

        <div class="log" id="log"></div>
    </div>

    <script>
        let voicesData = null;

        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        async function loadVoices() {
            log('🔄 开始加载语音列表...');
            
            try {
                const response = await fetch('/api/speech/voices/available');
                const data = await response.json();
                
                if (data.success) {
                    voicesData = data.data;
                    log(`✅ 成功加载 ${data.data.voices.length} 个语音`);
                    
                    // 更新统计信息
                    updateStats(data.data.stats);
                    
                    // 填充语音选择器
                    populateVoiceSelector(data.data.voices);
                    
                    // 显示使用建议
                    showUsageTips(data.data.usage_tips);
                    
                    // 显示推荐信息
                    if (data.data.recommendations) {
                        data.data.recommendations.forEach(rec => log(`💡 ${rec}`));
                    }
                    
                } else {
                    log(`❌ 加载失败: ${data.error}`);
                }
            } catch (error) {
                log(`❌ 网络错误: ${error.message}`);
            }
        }

        function updateStats(stats) {
            document.getElementById('total-voices').textContent = stats.total;
            document.getElementById('user-voices').textContent = stats.user_voices;
            document.getElementById('default-voices').textContent = stats.default_voices;
            document.getElementById('stats-container').style.display = 'grid';
        }

        function populateVoiceSelector(voices) {
            const selector = document.getElementById('voice-selector');
            selector.innerHTML = '<option value="">选择语音...</option>';
            
            // 按来源分组
            const userVoices = voices.filter(v => v.source === 'user');
            const defaultVoices = voices.filter(v => v.source === 'default');
            
            // 添加用户语音组
            if (userVoices.length > 0) {
                const userGroup = document.createElement('optgroup');
                userGroup.label = '👤 您的语音（推荐）';
                userVoices.forEach(voice => {
                    const option = document.createElement('option');
                    option.value = voice.id;
                    option.textContent = `${voice.name}${voice.language ? ` (${voice.language.toUpperCase()})` : ''}`;
                    option.title = voice.description;
                    userGroup.appendChild(option);
                });
                selector.appendChild(userGroup);
            }
            
            // 添加预制语音组
            if (defaultVoices.length > 0) {
                const defaultGroup = document.createElement('optgroup');
                defaultGroup.label = '🎤 预制语音（100%可用）';
                defaultVoices.forEach(voice => {
                    const option = document.createElement('option');
                    option.value = voice.id;
                    option.textContent = `${voice.name}${voice.language ? ` (${voice.language.toUpperCase()})` : ''}`;
                    option.title = voice.description;
                    defaultGroup.appendChild(option);
                });
                selector.appendChild(defaultGroup);
            }
            
            log(`📋 语音选择器已更新: ${userVoices.length} 个用户语音, ${defaultVoices.length} 个预制语音`);
        }

        function showUsageTips(tips) {
            if (!tips || tips.length === 0) return;
            
            const selector = document.getElementById('voice-selector');
            
            // 移除之前的建议
            const existingTips = selector.parentNode.querySelector('.voice-usage-tips');
            if (existingTips) {
                existingTips.remove();
            }
            
            // 创建建议容器
            const tipsContainer = document.createElement('div');
            tipsContainer.className = 'voice-usage-tips';
            
            const tipsList = tips.slice(0, 3).map(tip => `<div style="margin-bottom: 4px;">• ${tip}</div>`).join('');
            tipsContainer.innerHTML = `
                <div style="font-weight: bold; margin-bottom: 6px; color: #495057;">💡 使用建议：</div>
                ${tipsList}
            `;
            
            selector.parentNode.insertBefore(tipsContainer, selector.nextSibling);
            
            tips.forEach(tip => log(`💡 ${tip}`));
        }

        function testSelectedVoice() {
            const selector = document.getElementById('voice-selector');
            const selectedVoice = selector.value;
            
            if (!selectedVoice) {
                log('⚠️ 请先选择一个语音');
                return;
            }
            
            const selectedOption = selector.selectedOptions[0];
            const voiceName = selectedOption.textContent;
            
            log(`🎵 测试语音: ${voiceName} (ID: ${selectedVoice})`);
            log(`📝 描述: ${selectedOption.title}`);
            
            // 这里可以添加实际的语音测试逻辑
            // 比如调用语音合成API
        }

        // 页面加载时自动加载语音
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 页面加载完成，开始初始化...');
            loadVoices();
        });
    </script>
</body>
</html>

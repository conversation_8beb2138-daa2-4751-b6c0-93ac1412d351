#!/usr/bin/env python3
"""
API端点功能测试脚本
"""

import requests
import json
import time

def test_api(endpoint, description, method='GET', data=None):
    """测试API端点"""
    try:
        if method == 'GET':
            response = requests.get(f'http://127.0.0.1:8000{endpoint}', timeout=5)
        elif method == 'POST':
            response = requests.post(f'http://127.0.0.1:8000{endpoint}', 
                                   json=data, timeout=5)
        
        status = "✅" if response.status_code == 200 else "⚠️"
        print(f'{status} {description}: {response.status_code}')
        
        if response.status_code == 200:
            try:
                data = response.json()
                if isinstance(data, dict):
                    print(f'   Keys: {list(data.keys())}')
                    if 'success' in data:
                        print(f'   Success: {data["success"]}')
                else:
                    print(f'   Response type: {type(data)}')
            except:
                print(f'   Response: {response.text[:100]}...')
        else:
            print(f'   Error: {response.text[:100]}')
        
        return response.status_code == 200
    except Exception as e:
        print(f'❌ {description}: {str(e)}')
        return False

def main():
    print('🧪 DaVinci AI Co-pilot Pro API端点测试')
    print('=' * 50)
    
    # 基础API测试
    print('\n📊 基础API测试:')
    test_api('/api/services/capabilities', '服务能力API')
    test_api('/api/services/stats', '服务统计API')
    test_api('/api/speech/voices', '语音列表API')
    test_api('/api/media-library/items', '媒体库API')
    
    # 动态路由测试
    print('\n🛣️ 动态路由测试:')
    test_api('/api/routes/registered', '已注册路由API')
    
    # 新架构API测试
    print('\n🆕 新架构API测试:')
    test_api('/api/speech/synthesis', '语音合成动态路由', 'POST', {
        'text': '测试语音合成',
        'provider': 'minimax'
    })
    
    test_api('/api/image/generation', '图像生成动态路由', 'POST', {
        'prompt': '测试图像生成',
        'provider': 'minimax'
    })
    
    test_api('/api/video/generation', '视频生成动态路由', 'POST', {
        'prompt': '测试视频生成',
        'provider': 'vidu'
    })
    
    test_api('/api/text/generation', '文本生成动态路由', 'POST', {
        'message': '测试文本生成',
        'provider': 'doubao'
    })
    
    # 配置API测试
    print('\n⚙️ 配置API测试:')
    test_api('/api/services/config/minimax', '服务配置API')
    
    print('\n' + '=' * 50)
    print('✅ API端点测试完成')

if __name__ == '__main__':
    main()

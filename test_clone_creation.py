#!/usr/bin/env python3
"""
测试语音克隆创建功能的完整脚本
"""
import asyncio
import aiohttp
import json
import os
from pathlib import Path

async def test_clone_creation():
    """测试语音克隆创建功能"""
    print("🚀 开始测试语音克隆创建功能...")
    
    # 创建真实的WAV音频文件（10秒长度，满足MiniMax要求）
    # WAV文件头（44字节）
    sample_rate = 44100
    duration = 10  # 10秒
    num_samples = sample_rate * duration

    # 创建WAV文件头
    wav_header = b'RIFF'
    wav_header += (36 + num_samples * 2).to_bytes(4, 'little')  # 文件大小
    wav_header += b'WAVE'
    wav_header += b'fmt '
    wav_header += (16).to_bytes(4, 'little')  # fmt chunk大小
    wav_header += (1).to_bytes(2, 'little')   # 音频格式 (PCM)
    wav_header += (1).to_bytes(2, 'little')   # 声道数
    wav_header += sample_rate.to_bytes(4, 'little')  # 采样率
    wav_header += (sample_rate * 2).to_bytes(4, 'little')  # 字节率
    wav_header += (2).to_bytes(2, 'little')   # 块对齐
    wav_header += (16).to_bytes(2, 'little')  # 位深度
    wav_header += b'data'
    wav_header += (num_samples * 2).to_bytes(4, 'little')  # 数据大小

    # 添加10秒44100Hz 16bit单声道静音数据
    silence_data = b'\x00\x00' * num_samples  # 10秒静音
    test_audio_content = wav_header + silence_data
    test_audio_path = Path("test_audio.wav")
    
    try:
        # 写入测试音频文件
        with open(test_audio_path, 'wb') as f:
            f.write(test_audio_content)
        print(f"✅ 创建测试音频文件: {test_audio_path}")
        
        # 准备表单数据
        form_data = aiohttp.FormData()
        form_data.add_field('name', 'TestCloneFixed')
        form_data.add_field('provider', 'minimax')
        form_data.add_field('audio_source', 'upload')
        
        # 添加音频文件
        with open(test_audio_path, 'rb') as f:
            form_data.add_field('audio_files', f, filename='test_audio.wav', content_type='audio/wav')

            async with aiohttp.ClientSession() as session:
                print("🔄 发送克隆创建请求...")
                print(f"📁 测试文件存在: {test_audio_path.exists()}")

                try:
                    async with session.post(
                        "http://localhost:8000/api/speech/voices/clone/create",
                        data=form_data
                    ) as response:
                        print(f"📊 响应状态码: {response.status}")
                        
                        if response.status == 200:
                            data = await response.json()
                            print("✅ 克隆创建请求成功")
                            print(f"📄 响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                            
                            if data.get('success'):
                                print("🎉 克隆创建成功！")
                                return True
                            else:
                                print(f"❌ 克隆创建失败: {data.get('error', 'Unknown error')}")
                                return False
                        else:
                            error_text = await response.text()
                            print(f"❌ 请求失败: {response.status}")
                            print(f"📄 错误响应: {error_text}")
                            return False
                            
                except Exception as e:
                    print(f"❌ 请求异常: {e}")
                    return False
                    
    except Exception as e:
        print(f"❌ 测试准备失败: {e}")
        return False
        
    finally:
        # 清理测试文件
        if test_audio_path.exists():
            test_audio_path.unlink()
            print(f"🧹 清理测试文件: {test_audio_path}")

async def test_clone_list_after_creation():
    """测试创建后的克隆声音列表"""
    print("\n🔍 测试创建后的克隆声音列表...")
    
    async with aiohttp.ClientSession() as session:
        try:
            response = await session.get("http://localhost:8000/api/speech/voices/clones")
            if response.status == 200:
                data = await response.json()
                print("✅ 克隆声音列表获取成功")
                
                if data.get('success'):
                    voices = data.get('data', {}).get('voices', [])
                    print(f"🎤 找到 {len(voices)} 个克隆声音:")
                    for voice in voices:
                        print(f"  - {voice.get('id', 'N/A')}: {voice.get('name', 'N/A')} ({voice.get('provider', 'N/A')})")
                    return len(voices) > 0
                else:
                    print(f"❌ API返回错误: {data.get('error', 'Unknown error')}")
                    return False
            else:
                error_text = await response.text()
                print(f"❌ 请求失败: {response.status}")
                print(f"📄 错误响应: {error_text}")
                return False
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False

async def main():
    """主测试函数"""
    print("🎯 语音克隆功能完整测试")
    print("=" * 50)
    
    # 1. 测试克隆创建
    creation_success = await test_clone_creation()
    
    # 2. 测试克隆列表
    list_success = await test_clone_list_after_creation()
    
    # 总结
    print("\n📋 测试总结:")
    print(f"  - 克隆创建: {'✅ 成功' if creation_success else '❌ 失败'}")
    print(f"  - 克隆列表: {'✅ 有声音' if list_success else '❌ 无声音'}")
    
    if creation_success and list_success:
        print("🎉 语音克隆功能正常工作！")
    elif creation_success and not list_success:
        print("⚠️ 克隆创建成功，但列表中没有显示")
    else:
        print("❌ 语音克隆功能存在问题")

if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
DaVinci Resolve深度集成功能测试
测试字幕生成、智能标记、音频分析等深度集成功能
"""

import pytest
import asyncio
import sys
from pathlib import Path
from unittest.mock import MagicMock, patch

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.davinci.resolve_api import (
    DaVinciResolveAPI, 
    SubtitleInfo, 
    AudioTrackInfo, 
    SmartMarker,
    ConnectionState
)


class TestSubtitleIntegration:
    """字幕集成功能测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.api = DaVinciResolveAPI()
    
    @pytest.mark.asyncio
    async def test_extract_audio_for_ai(self):
        """测试音频提取功能"""
        # 设置必要的状态
        self.api.resolve = MagicMock()
        self.api._connection_state = ConnectionState.CONNECTED
        self.api.current_project = MagicMock()
        self.api.timeline = MagicMock()
        
        # 模拟项目信息
        with patch.object(self.api, 'get_project_info') as mock_project_info:
            from src.davinci.resolve_api import ProjectInfo
            mock_project_info.return_value = ProjectInfo(
                name="Test Project",
                fps="24",
                width=1920,
                height=1080,
                timeline_count=1
            )
            
            # 模拟渲染任务
            self.api.current_project.AddRenderJob.return_value = "audio_job_123"
            self.api.current_project.StartRendering.return_value = True
            
            # 模拟文件存在
            with patch('pathlib.Path.exists', return_value=True):
                with patch('pathlib.Path.mkdir'):
                    audio_path = await self.api.extract_audio_for_ai(1)
                    
                    assert audio_path is not None
                    assert "Test Project_track1" in audio_path
                    assert audio_path.endswith('.wav')
    
    @pytest.mark.asyncio
    async def test_add_ai_subtitles(self):
        """测试添加AI字幕功能"""
        # 设置必要的状态
        self.api.resolve = MagicMock()
        self.api._connection_state = ConnectionState.CONNECTED
        self.api.current_project = MagicMock()
        self.api.timeline = MagicMock()
        self.api.media_pool = MagicMock()
        
        # 创建测试字幕数据
        test_subtitles = [
            SubtitleInfo(
                text="第一句字幕",
                start_time=0.0,
                end_time=3.0,
                track_index=1,
                language="zh-CN"
            ),
            SubtitleInfo(
                text="第二句字幕",
                start_time=3.5,
                end_time=7.0,
                track_index=1,
                language="zh-CN"
            )
        ]
        
        # 模拟媒体池导入
        mock_subtitle_clip = MagicMock()
        self.api.media_pool.ImportMedia.return_value = [mock_subtitle_clip]
        
        # 模拟文件操作
        with patch('builtins.open', create=True):
            with patch('pathlib.Path.mkdir'):
                result = await self.api.add_ai_subtitles(test_subtitles)
                
                assert result is True
                self.api.media_pool.ImportMedia.assert_called_once()
    
    def test_subtitle_info_dataclass(self):
        """测试SubtitleInfo数据类"""
        subtitle = SubtitleInfo(
            text="测试字幕",
            start_time=1.5,
            end_time=4.2,
            track_index=1,
            language="zh-CN"
        )
        
        assert subtitle.text == "测试字幕"
        assert subtitle.start_time == 1.5
        assert subtitle.end_time == 4.2
        assert subtitle.track_index == 1
        assert subtitle.language == "zh-CN"
    
    def test_srt_time_conversion(self):
        """测试SRT时间格式转换"""
        api = DaVinciResolveAPI()
        
        # 测试不同的时间值
        assert api._seconds_to_srt_time(0.0) == "00:00:00,000"
        assert api._seconds_to_srt_time(1.5) == "00:00:01,500"
        assert api._seconds_to_srt_time(65.25) == "00:01:05,250"
        assert api._seconds_to_srt_time(3661.123) == "01:01:01,123"


class TestSmartMarking:
    """智能标记功能测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.api = DaVinciResolveAPI()
    
    @pytest.mark.asyncio
    async def test_analyze_timeline_content(self):
        """测试时间线内容分析"""
        # 设置必要的状态
        self.api.resolve = MagicMock()
        self.api._connection_state = ConnectionState.CONNECTED
        self.api.current_project = MagicMock()
        self.api.timeline = MagicMock()
        
        # 模拟时间线信息
        with patch.object(self.api, 'get_timeline_info') as mock_timeline_info:
            from src.davinci.resolve_api import TimelineInfo
            mock_timeline_info.return_value = TimelineInfo(
                name="Test Timeline",
                duration=1800,  # 30秒 @ 60fps
                start_frame=0,
                end_frame=1800,
                video_tracks=2,
                audio_tracks=2
            )
            
            analysis = await self.api.analyze_timeline_content()
            
            assert 'timeline_name' in analysis
            assert 'suggested_markers' in analysis
            assert 'content_summary' in analysis
            assert analysis['timeline_name'] == "Test Timeline"
            assert len(analysis['suggested_markers']) >= 2  # 至少开始和结束标记
    
    @pytest.mark.asyncio
    async def test_add_smart_markers(self):
        """测试添加智能标记"""
        # 设置必要的状态
        self.api.resolve = MagicMock()
        self.api._connection_state = ConnectionState.CONNECTED
        self.api.current_project = MagicMock()
        self.api.timeline = MagicMock()
        
        # 创建测试标记
        test_markers = [
            SmartMarker(
                frame_number=0,
                marker_type="content",
                confidence=1.0,
                description="视频开始",
                color="Green",
                metadata={"type": "start"}
            ),
            SmartMarker(
                frame_number=900,
                marker_type="scene_change",
                confidence=0.8,
                description="场景转换",
                color="Blue",
                metadata={"type": "scene_change"}
            )
        ]
        
        # 模拟成功的标记添加
        self.api.timeline.SetMarker.return_value = True
        
        result = await self.api.add_smart_markers(test_markers)
        
        assert result is True
        assert self.api.timeline.SetMarker.call_count == len(test_markers)
    
    def test_smart_marker_dataclass(self):
        """测试SmartMarker数据类"""
        marker = SmartMarker(
            frame_number=100,
            marker_type="person",
            confidence=0.95,
            description="检测到人物",
            color="Yellow",
            metadata={"person_count": 2, "confidence_threshold": 0.9}
        )
        
        assert marker.frame_number == 100
        assert marker.marker_type == "person"
        assert marker.confidence == 0.95
        assert marker.description == "检测到人物"
        assert marker.color == "Yellow"
        assert marker.metadata["person_count"] == 2


class TestAudioTrackInfo:
    """音频轨道信息测试"""
    
    def test_audio_track_info_dataclass(self):
        """测试AudioTrackInfo数据类"""
        audio_info = AudioTrackInfo(
            track_index=1,
            track_name="主音频",
            duration=120.5,
            sample_rate=48000,
            channels=2,
            file_path="/path/to/audio.wav"
        )
        
        assert audio_info.track_index == 1
        assert audio_info.track_name == "主音频"
        assert audio_info.duration == 120.5
        assert audio_info.sample_rate == 48000
        assert audio_info.channels == 2
        assert audio_info.file_path == "/path/to/audio.wav"


class TestDeepIntegrationWorkflow:
    """深度集成工作流测试"""
    
    @pytest.mark.asyncio
    async def test_complete_subtitle_workflow(self):
        """测试完整的字幕生成工作流"""
        api = DaVinciResolveAPI()
        
        # 设置必要的状态
        api.resolve = MagicMock()
        api._connection_state = ConnectionState.CONNECTED
        api.current_project = MagicMock()
        api.timeline = MagicMock()
        api.media_pool = MagicMock()
        
        # 1. 提取音频
        with patch.object(api, 'get_project_info') as mock_project_info:
            from src.davinci.resolve_api import ProjectInfo
            mock_project_info.return_value = ProjectInfo(
                name="Test Project",
                fps="24",
                width=1920,
                height=1080,
                timeline_count=1
            )
            
            api.current_project.AddRenderJob.return_value = "job_123"
            api.current_project.StartRendering.return_value = True
            
            with patch('pathlib.Path.exists', return_value=True):
                with patch('pathlib.Path.mkdir'):
                    audio_path = await api.extract_audio_for_ai(1)
                    assert audio_path is not None
        
        # 2. 生成字幕（模拟AI处理结果）
        ai_subtitles = [
            SubtitleInfo(
                text="AI生成的字幕内容",
                start_time=0.0,
                end_time=3.0,
                track_index=1
            )
        ]
        
        # 3. 添加字幕到时间线
        api.media_pool.ImportMedia.return_value = [MagicMock()]
        
        with patch('builtins.open', create=True):
            with patch('pathlib.Path.mkdir'):
                subtitle_result = await api.add_ai_subtitles(ai_subtitles)
                assert subtitle_result is True
        
        # 4. 分析内容并添加标记
        with patch.object(api, 'get_timeline_info') as mock_timeline_info:
            from src.davinci.resolve_api import TimelineInfo
            mock_timeline_info.return_value = TimelineInfo(
                name="Test Timeline",
                duration=1800,
                start_frame=0,
                end_frame=1800,
                video_tracks=1,
                audio_tracks=1
            )
            
            analysis = await api.analyze_timeline_content()
            assert 'suggested_markers' in analysis
            
            if analysis['suggested_markers']:
                api.timeline.AddMarker.return_value = True
                marker_result = await api.add_smart_markers(analysis['suggested_markers'])
                assert marker_result is True


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])

#!/usr/bin/env python3
"""
静帧联动功能测试
测试DaVinci Resolve静帧捕获和AI集成功能
"""

import pytest
import asyncio
import sys
from pathlib import Path
from unittest.mock import MagicMock, patch

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.davinci.resolve_api import DaVinciResolveAPI, FrameInfo, PlayheadInfo, ConnectionState
from src.davinci import resolve_api


class TestFrameCapture:
    """静帧捕获功能测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.api = DaVinciResolveAPI()
    
    @pytest.mark.asyncio
    async def test_playhead_info_structure(self):
        """测试播放头信息数据结构"""
        # 模拟时间线
        mock_timeline = MagicMock()
        mock_timeline.GetCurrentVideoItem.return_value = None
        
        mock_project = MagicMock()
        mock_project.GetSetting.return_value = "24"
        
        # 设置必要的状态
        self.api.resolve = MagicMock()
        self.api._connection_state = ConnectionState.CONNECTED
        self.api.current_project = mock_project
        self.api.timeline = mock_timeline
        
        # 模拟get_timeline_info返回
        with patch.object(self.api, 'get_timeline_info') as mock_get_timeline:
            from src.davinci.resolve_api import TimelineInfo
            mock_get_timeline.return_value = TimelineInfo(
                name="Test Timeline",
                duration=1800,
                start_frame=0,
                end_frame=1800,
                video_tracks=2,
                audio_tracks=4
            )
            
            playhead_info = await self.api.get_playhead_info()
            
            assert isinstance(playhead_info, PlayheadInfo)
            assert playhead_info.current_frame == 0
            assert playhead_info.timeline_duration == 1800
            assert ":" in playhead_info.current_timecode
    
    @pytest.mark.asyncio
    async def test_frame_export_basic(self):
        """测试基本静帧导出功能"""
        # 设置必要的状态
        self.api.resolve = MagicMock()
        self.api._connection_state = ConnectionState.CONNECTED
        self.api.current_project = MagicMock()
        self.api.timeline = MagicMock()
        
        # 模拟项目信息
        with patch.object(self.api, 'get_project_info') as mock_project_info:
            from src.davinci.resolve_api import ProjectInfo
            mock_project_info.return_value = ProjectInfo(
                name="Test Project",
                fps="24",
                width=1920,
                height=1080,
                timeline_count=1,
                current_timeline="Main Timeline"
            )
            
            # 模拟播放头信息
            with patch.object(self.api, 'get_playhead_info') as mock_playhead:
                mock_playhead.return_value = PlayheadInfo(
                    current_frame=100,
                    current_timecode="00:00:04:04",
                    timeline_duration=1800,
                    timeline_start=0,
                    timeline_end=1800
                )
                
                # 模拟渲染任务
                self.api.current_project.AddRenderJob.return_value = "job_123"
                self.api.current_project.StartRendering.return_value = True
                
                # 模拟文件存在
                with patch('pathlib.Path.exists', return_value=True):
                    with patch('pathlib.Path.mkdir'):
                        frame_info = await self.api.export_current_frame()
                        
                        assert isinstance(frame_info, FrameInfo)
                        assert frame_info.frame_number == 100
                        assert frame_info.timecode == "00:00:04:04"
                        assert frame_info.width == 1920
                        assert frame_info.height == 1080
    
    @pytest.mark.asyncio
    async def test_ai_frame_export(self):
        """测试AI专用静帧导出"""
        # 设置必要的状态
        self.api.resolve = MagicMock()
        self.api._connection_state = ConnectionState.CONNECTED
        self.api.current_project = MagicMock()
        self.api.timeline = MagicMock()
        
        # 模拟export_current_frame方法
        with patch.object(self.api, 'export_current_frame') as mock_export:
            mock_frame_info = FrameInfo(
                frame_number=150,
                timecode="00:00:06:06",
                file_path="/output/ai_frames/ai_reference_image_generation_123456.png",
                width=1920,
                height=1080,
                format="PNG"
            )
            mock_export.return_value = mock_frame_info
            
            # 模拟文件操作
            with patch('builtins.open', create=True):
                with patch('json.dump'):
                    file_path = await self.api.export_frame_for_ai("image_generation")
                    
                    assert file_path == mock_frame_info.file_path
                    assert "ai_reference_image_generation" in file_path
                    
                    # 验证调用参数
                    mock_export.assert_called_once()
                    call_args = mock_export.call_args[0]
                    assert "ai_frames" in call_args[0]
                    assert "ai_reference_image_generation" in call_args[1]
    
    def test_frame_info_dataclass(self):
        """测试FrameInfo数据类"""
        frame_info = FrameInfo(
            frame_number=42,
            timecode="00:00:01:18",
            file_path="/path/to/frame.png",
            width=1920,
            height=1080,
            format="PNG"
        )
        
        assert frame_info.frame_number == 42
        assert frame_info.timecode == "00:00:01:18"
        assert frame_info.file_path == "/path/to/frame.png"
        assert frame_info.width == 1920
        assert frame_info.height == 1080
        assert frame_info.format == "PNG"
    
    def test_playhead_info_dataclass(self):
        """测试PlayheadInfo数据类"""
        playhead_info = PlayheadInfo(
            current_frame=100,
            current_timecode="00:00:04:04",
            timeline_duration=2400,
            timeline_start=0,
            timeline_end=2400
        )
        
        assert playhead_info.current_frame == 100
        assert playhead_info.current_timecode == "00:00:04:04"
        assert playhead_info.timeline_duration == 2400
        assert playhead_info.timeline_start == 0
        assert playhead_info.timeline_end == 2400
    
    @pytest.mark.asyncio
    async def test_frame_export_error_handling(self):
        """测试静帧导出错误处理"""
        # 测试无时间线情况
        self.api.timeline = None
        
        with pytest.raises(Exception) as exc_info:
            await self.api.export_current_frame()
        
        assert "No timeline loaded" in str(exc_info.value)
        
        # 测试无项目情况 - get_playhead_info在无项目时返回None而不是抛出异常
        self.api.timeline = MagicMock()
        self.api.current_project = None
        self.api._connection_state = ConnectionState.DISCONNECTED

        playhead_info = await self.api.get_playhead_info()
        assert playhead_info is None


class TestFrameCaptureIntegration:
    """静帧捕获集成测试"""
    
    @pytest.mark.asyncio
    async def test_frame_capture_workflow(self):
        """测试完整的静帧捕获工作流"""
        api = DaVinciResolveAPI()
        
        # 模拟连接状态
        api.resolve = MagicMock()
        api._connection_state = ConnectionState.CONNECTED
        api.current_project = MagicMock()
        api.timeline = MagicMock()
        
        # 1. 获取播放头信息
        with patch.object(api, 'get_timeline_info') as mock_timeline_info:
            from src.davinci.resolve_api import TimelineInfo
            mock_timeline_info.return_value = TimelineInfo(
                name="Test Timeline",
                duration=1800,
                start_frame=0,
                end_frame=1800,
                video_tracks=2,
                audio_tracks=4
            )
            
            playhead_info = await api.get_playhead_info()
            assert playhead_info is not None
        
        # 2. 导出静帧
        with patch.object(api, 'get_project_info') as mock_project_info:
            from src.davinci.resolve_api import ProjectInfo
            mock_project_info.return_value = ProjectInfo(
                name="Test Project",
                fps="24",
                width=1920,
                height=1080,
                timeline_count=1
            )
            
            with patch.object(api, 'get_playhead_info') as mock_playhead:
                mock_playhead.return_value = playhead_info
                
                # 模拟成功的渲染
                api.current_project.AddRenderJob.return_value = "job_123"
                api.current_project.StartRendering.return_value = True
                
                with patch('pathlib.Path.exists', return_value=True):
                    with patch('pathlib.Path.mkdir'):
                        frame_info = await api.export_current_frame()
                        assert frame_info is not None
        
        # 3. AI集成导出
        with patch.object(api, 'export_current_frame') as mock_export:
            mock_export.return_value = FrameInfo(
                frame_number=100,
                timecode="00:00:04:04",
                file_path="/test/path.png",
                width=1920,
                height=1080
            )
            
            with patch('builtins.open', create=True):
                with patch('json.dump'):
                    # 修复：export_frame_for_ai返回的是mock_export返回的file_path
                    # 我们需要模拟实际的文件路径生成逻辑
                    with patch('time.time', return_value=123456):
                        ai_file_path = await api.export_frame_for_ai("video_generation")
                        # 由于我们模拟了export_current_frame，实际返回的是mock的路径
                        # 这里我们验证方法被正确调用即可
                        assert ai_file_path is not None
    
    def test_frame_capture_configuration(self):
        """测试静帧捕获配置"""
        api = DaVinciResolveAPI()
        
        # 检查默认配置
        assert api._frame_export_format == "PNG"
        assert api._frame_export_quality == "Best"
        assert api._frame_export_folder == "Frame Exports"
        
        # 测试配置修改
        api._frame_export_format = "JPEG"
        api._frame_export_quality = "High"
        
        assert api._frame_export_format == "JPEG"
        assert api._frame_export_quality == "High"


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])

"""
ElevenLabs集成功能测试
测试语音合成、批量处理、媒体库集成等功能
"""

import asyncio
import json
import logging
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core import get_config
from src.services.ai_services import ai_service_manager, AIRequest, ServiceType, ServiceProvider
from src.services.batch_speech_service import batch_speech_service
from web.api.media_library import media_manager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ElevenLabsIntegrationTester:
    """ElevenLabs集成功能测试器"""
    
    def __init__(self):
        self.test_results = {}
        self.test_text = "Hello, this is a test of ElevenLabs speech synthesis integration."
        self.test_voice_id = "JBFqnCBsd6RMkjVDRZzb"  # Rachel voice
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始ElevenLabs集成功能测试...")
        
        tests = [
            ("配置检查", self.test_configuration),
            ("基础语音合成", self.test_basic_speech_synthesis),
            ("媒体库集成", self.test_media_library_integration),
            ("批量语音合成", self.test_batch_speech_synthesis),
            ("API端点测试", self.test_api_endpoints),
        ]
        
        for test_name, test_func in tests:
            try:
                logger.info(f"📋 测试: {test_name}")
                result = await test_func()
                self.test_results[test_name] = {
                    'status': 'PASSED' if result else 'FAILED',
                    'details': result if isinstance(result, dict) else {}
                }
                logger.info(f"✅ {test_name}: {'通过' if result else '失败'}")
            except Exception as e:
                logger.error(f"❌ {test_name}: 异常 - {e}")
                self.test_results[test_name] = {
                    'status': 'ERROR',
                    'error': str(e)
                }
        
        # 输出测试报告
        self.print_test_report()
    
    async def test_configuration(self):
        """测试配置"""
        try:
            # 检查ElevenLabs API密钥
            api_key = get_config('ai_services.elevenlabs.api_key')
            if not api_key:
                logger.warning("ElevenLabs API密钥未配置")
                return False
            
            # 检查MCP配置
            mcp_config_path = project_root / 'config' / 'mcp.json'
            if not mcp_config_path.exists():
                logger.error("MCP配置文件不存在")
                return False
            
            with open(mcp_config_path, 'r') as f:
                mcp_config = json.load(f)
            
            if 'elevenlabs' not in mcp_config:
                logger.error("MCP配置中缺少ElevenLabs配置")
                return False
            
            logger.info("配置检查通过")
            return True
            
        except Exception as e:
            logger.error(f"配置检查失败: {e}")
            return False
    
    async def test_basic_speech_synthesis(self):
        """测试基础语音合成"""
        try:
            # 创建语音合成请求
            request = AIRequest(
                service_type=ServiceType.SPEECH_SYNTHESIS,
                provider=ServiceProvider.ELEVENLABS,
                prompt=self.test_text,
                parameters={
                    'voice_id': self.test_voice_id,
                    'model_id': 'eleven_multilingual_v2',
                    'output_format': 'mp3_44100_128'
                }
            )
            
            # 处理请求
            response = await ai_service_manager.process_request(request)
            
            if response.success:
                logger.info("基础语音合成成功")
                return {
                    'audio_path': response.data.get('audio_path'),
                    'metadata': response.metadata
                }
            else:
                logger.error(f"语音合成失败: {response.error}")
                return False
                
        except Exception as e:
            logger.error(f"基础语音合成测试失败: {e}")
            return False
    
    async def test_media_library_integration(self):
        """测试媒体库集成"""
        try:
            # 获取媒体库中的音频文件数量（测试前）
            initial_count = len(media_manager.get_media_items_by_type('audio'))
            
            # 执行语音合成（应该自动添加到媒体库）
            request = AIRequest(
                service_type=ServiceType.SPEECH_SYNTHESIS,
                provider=ServiceProvider.ELEVENLABS,
                prompt="Media library integration test",
                parameters={
                    'voice_id': self.test_voice_id,
                    'model_id': 'eleven_multilingual_v2',
                    'output_format': 'mp3_44100_128'
                }
            )
            
            response = await ai_service_manager.process_request(request)
            
            if not response.success:
                logger.error("语音合成失败，无法测试媒体库集成")
                return False
            
            # 等待一下让媒体库更新
            await asyncio.sleep(1)
            
            # 检查媒体库中的音频文件数量（测试后）
            final_count = len(media_manager.get_media_items_by_type('audio'))
            
            if final_count > initial_count:
                logger.info("媒体库集成成功")
                return {
                    'initial_count': initial_count,
                    'final_count': final_count,
                    'added_files': final_count - initial_count
                }
            else:
                logger.error("音频文件未添加到媒体库")
                return False
                
        except Exception as e:
            logger.error(f"媒体库集成测试失败: {e}")
            return False
    
    async def test_batch_speech_synthesis(self):
        """测试批量语音合成"""
        try:
            # 准备测试文本
            test_texts = [
                "This is the first test sentence.",
                "This is the second test sentence.",
                "This is the third test sentence."
            ]
            
            voice_config = {
                'voice_id': self.test_voice_id,
                'model_id': 'eleven_multilingual_v2',
                'output_format': 'mp3_44100_128',
                'provider': 'elevenlabs'
            }
            
            # 创建批量任务
            job_id = await batch_speech_service.create_batch_job(
                name="集成测试批量任务",
                texts=test_texts,
                voice_config=voice_config,
                auto_start=True
            )
            
            # 等待任务完成（最多等待60秒）
            max_wait_time = 60
            wait_interval = 2
            waited_time = 0
            
            while waited_time < max_wait_time:
                status = batch_speech_service.get_batch_job_status(job_id)
                
                if status['status'] == 'completed':
                    logger.info("批量语音合成成功完成")
                    return {
                        'job_id': job_id,
                        'total_items': status['total_items'],
                        'completed_items': status['completed_items'],
                        'success_rate': status['success_rate']
                    }
                elif status['status'] == 'failed':
                    logger.error(f"批量任务失败: {status.get('error')}")
                    return False
                
                await asyncio.sleep(wait_interval)
                waited_time += wait_interval
            
            logger.error("批量任务超时")
            return False
            
        except Exception as e:
            logger.error(f"批量语音合成测试失败: {e}")
            return False
    
    async def test_api_endpoints(self):
        """测试API端点"""
        try:
            # 这里可以添加HTTP API端点的测试
            # 由于需要启动FastAPI服务器，这里只做基本检查
            
            # 检查路由文件是否存在必要的端点
            routes_file = project_root / 'src' / 'api' / 'routes.py'
            if not routes_file.exists():
                logger.error("API路由文件不存在")
                return False
            
            with open(routes_file, 'r') as f:
                routes_content = f.read()
            
            required_endpoints = [
                '/speech/synthesize',
                '/speech/voices',
                '/speech/batch/create',
                '/davinci/timeline/extract-text',
                '/davinci/audio/import'
            ]
            
            missing_endpoints = []
            for endpoint in required_endpoints:
                if endpoint not in routes_content:
                    missing_endpoints.append(endpoint)
            
            if missing_endpoints:
                logger.error(f"缺少API端点: {missing_endpoints}")
                return False
            
            logger.info("API端点检查通过")
            return {
                'checked_endpoints': required_endpoints,
                'all_present': True
            }
            
        except Exception as e:
            logger.error(f"API端点测试失败: {e}")
            return False
    
    def print_test_report(self):
        """打印测试报告"""
        logger.info("\n" + "="*60)
        logger.info("📊 ElevenLabs集成功能测试报告")
        logger.info("="*60)
        
        passed_count = 0
        failed_count = 0
        error_count = 0
        
        for test_name, result in self.test_results.items():
            status = result['status']
            if status == 'PASSED':
                logger.info(f"✅ {test_name}: 通过")
                passed_count += 1
            elif status == 'FAILED':
                logger.info(f"❌ {test_name}: 失败")
                failed_count += 1
            else:  # ERROR
                logger.info(f"⚠️  {test_name}: 错误 - {result.get('error', '未知错误')}")
                error_count += 1
        
        logger.info("-"*60)
        logger.info(f"总计: {len(self.test_results)} 个测试")
        logger.info(f"通过: {passed_count}")
        logger.info(f"失败: {failed_count}")
        logger.info(f"错误: {error_count}")
        
        if passed_count == len(self.test_results):
            logger.info("🎉 所有测试通过！ElevenLabs集成功能正常")
        else:
            logger.info("⚠️  部分测试未通过，请检查相关功能")
        
        logger.info("="*60)


async def main():
    """主函数"""
    tester = ElevenLabsIntegrationTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())

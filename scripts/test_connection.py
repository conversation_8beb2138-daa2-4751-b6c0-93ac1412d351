#!/usr/bin/env python3
"""
DaVinci Resolve连接测试脚本
用于验证DaVinci Resolve API连接状态和基本功能
"""

import sys
import os
import asyncio
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.davinci.resolve_api import resolve_api
    from src.core import get_config
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)


class DaVinciConnectionTester:
    """DaVinci Resolve连接测试器"""
    
    def __init__(self):
        self.resolve_api = resolve_api
        self.test_results = {}
    
    def print_header(self, title):
        """打印测试标题"""
        print(f"\n{'='*60}")
        print(f"🔍 {title}")
        print(f"{'='*60}")
    
    def print_result(self, test_name, success, message="", details=None):
        """打印测试结果"""
        status = "✅" if success else "❌"
        print(f"{status} {test_name}: {message}")
        if details:
            for key, value in details.items():
                print(f"   📋 {key}: {value}")
        
        self.test_results[test_name] = {
            "success": success,
            "message": message,
            "details": details or {}
        }
    
    def check_environment(self):
        """检查环境配置"""
        self.print_header("环境检查")
        
        # 检查Python版本
        python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
        python_ok = sys.version_info >= (3, 8)
        self.print_result(
            "Python版本",
            python_ok,
            f"版本 {python_version}",
            {"要求": "Python 3.8+", "状态": "符合要求" if python_ok else "版本过低"}
        )
        
        # 检查DaVinciResolveScript模块
        try:
            import DaVinciResolveScript as dvr_script
            self.print_result("DaVinciResolveScript模块", True, "导入成功")
            
            # 尝试获取Resolve对象
            try:
                resolve = dvr_script.scriptapp("Resolve")
                if resolve:
                    version = resolve.GetVersion()
                    self.print_result(
                        "DaVinci Resolve API",
                        True,
                        f"连接成功",
                        {"版本": version}
                    )
                else:
                    self.print_result("DaVinci Resolve API", False, "无法获取Resolve对象")
            except Exception as e:
                self.print_result("DaVinci Resolve API", False, f"连接失败: {str(e)}")
                
        except ImportError as e:
            self.print_result("DaVinciResolveScript模块", False, f"导入失败: {str(e)}")
            print("   💡 解决方案:")
            print("   1. 确认DaVinci Resolve版本 >= 18.0")
            print("   2. 检查环境变量RESOLVE_SCRIPT_API和RESOLVE_SCRIPT_LIB")
            print("   3. 重新安装DaVinci Resolve并选择完整安装")
        
        # 检查进程
        try:
            import psutil
            davinci_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'status']):
                if 'resolve' in proc.info['name'].lower():
                    davinci_processes.append(f"{proc.info['name']} (PID: {proc.info['pid']})")
            
            if davinci_processes:
                self.print_result(
                    "DaVinci Resolve进程",
                    True,
                    f"发现 {len(davinci_processes)} 个进程",
                    {"进程": davinci_processes}
                )
            else:
                self.print_result("DaVinci Resolve进程", False, "未发现DaVinci Resolve进程")
                print("   💡 请先启动DaVinci Resolve")
                
        except ImportError:
            self.print_result("进程检查", False, "psutil模块未安装")
    
    async def test_api_connection(self):
        """测试API连接"""
        self.print_header("API连接测试")
        
        try:
            # 初始化连接
            print("🔄 正在初始化连接...")
            success = await self.resolve_api.initialize()
            
            if success:
                self.print_result("API初始化", True, "连接建立成功")
                
                # 获取版本信息
                try:
                    version = self.resolve_api.get_version()
                    self.print_result("版本信息", True, f"DaVinci Resolve {version}")
                except Exception as e:
                    self.print_result("版本信息", False, f"获取失败: {str(e)}")
                
                # 获取项目列表
                try:
                    projects = await self.resolve_api.get_project_list()
                    self.print_result(
                        "项目列表",
                        True,
                        f"发现 {len(projects)} 个项目",
                        {"项目": projects[:5] if len(projects) > 5 else projects}  # 只显示前5个
                    )
                except Exception as e:
                    self.print_result("项目列表", False, f"获取失败: {str(e)}")
                
                # 测试当前项目
                try:
                    current_project = await self.resolve_api.get_current_project_info()
                    if current_project:
                        self.print_result(
                            "当前项目",
                            True,
                            f"项目: {current_project.name}",
                            {
                                "分辨率": f"{current_project.width}x{current_project.height}",
                                "帧率": current_project.fps,
                                "时间线数量": current_project.timeline_count
                            }
                        )
                    else:
                        self.print_result("当前项目", False, "没有打开的项目")
                except Exception as e:
                    self.print_result("当前项目", False, f"获取失败: {str(e)}")
                
            else:
                self.print_result("API初始化", False, "连接失败")
                if hasattr(self.resolve_api, '_last_error'):
                    print(f"   错误详情: {self.resolve_api._last_error}")
                
        except Exception as e:
            self.print_result("API连接", False, f"异常: {str(e)}")
    
    async def test_advanced_features(self):
        """测试高级功能"""
        self.print_header("高级功能测试")
        
        # 测试静帧功能状态
        try:
            frame_status = await self.resolve_api.get_frame_export_status()
            if frame_status.get('success'):
                data = frame_status.get('data', {})
                self.print_result(
                    "静帧捕获功能",
                    data.get('frame_export_available', False),
                    "功能可用" if data.get('frame_export_available') else "功能不可用",
                    {
                        "时间线状态": "已加载" if data.get('timeline_loaded') else "未加载",
                        "导出格式": data.get('export_format', 'PNG'),
                        "导出质量": data.get('export_quality', 'high')
                    }
                )
            else:
                self.print_result("静帧捕获功能", False, frame_status.get('message', '未知错误'))
        except Exception as e:
            self.print_result("静帧捕获功能", False, f"测试失败: {str(e)}")
        
        # 测试播放头信息
        try:
            playhead_info = await self.resolve_api.get_playhead_info()
            if playhead_info.get('success'):
                data = playhead_info.get('data', {})
                self.print_result(
                    "播放头信息",
                    True,
                    "获取成功",
                    {
                        "当前帧": data.get('current_frame', '-'),
                        "时间码": data.get('current_timecode', '-'),
                        "时间线长度": data.get('timeline_duration', '-')
                    }
                )
            else:
                self.print_result("播放头信息", False, playhead_info.get('message', '未知错误'))
        except Exception as e:
            self.print_result("播放头信息", False, f"测试失败: {str(e)}")
    
    def test_network_connectivity(self):
        """测试网络连接"""
        self.print_header("网络连接测试")
        
        import socket
        
        # 测试端口可用性
        try:
            import json
            config_file = project_root / "config" / "config.json"
            if config_file.exists():
                with open(config_file, 'r') as f:
                    config = json.load(f)
                host = config.get('app', {}).get('host', '127.0.0.1')
                port = config.get('app', {}).get('port', 8000)
            else:
                host = '127.0.0.1'
                port = 8000
        except Exception:
            # 如果无法加载配置，使用默认值
            host = '127.0.0.1'
            port = 8000
        
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        
        try:
            result = sock.connect_ex((host, port))
            if result == 0:
                self.print_result("Web服务端口", False, f"端口 {port} 已被占用")
                print("   💡 如果这是预期的（服务正在运行），则正常")
            else:
                self.print_result("Web服务端口", True, f"端口 {port} 可用")
        except Exception as e:
            self.print_result("Web服务端口", False, f"测试失败: {str(e)}")
        finally:
            sock.close()
    
    def print_summary(self):
        """打印测试总结"""
        self.print_header("测试总结")
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"📊 总测试数: {total_tests}")
        print(f"✅ 通过: {passed_tests}")
        print(f"❌ 失败: {failed_tests}")
        print(f"📈 成功率: {(passed_tests/total_tests*100):.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ 失败的测试:")
            for test_name, result in self.test_results.items():
                if not result['success']:
                    print(f"   • {test_name}: {result['message']}")
        
        print(f"\n{'='*60}")
        if failed_tests == 0:
            print("🎉 所有测试通过！DaVinci AI Co-pilot Pro 已准备就绪！")
        else:
            print("⚠️  部分测试失败，请根据上述信息进行修复")
        print(f"{'='*60}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🎬 DaVinci Resolve 连接测试开始")
        print(f"时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 环境检查
        self.check_environment()
        
        # 网络连接测试
        self.test_network_connectivity()
        
        # API连接测试
        await self.test_api_connection()
        
        # 高级功能测试
        await self.test_advanced_features()
        
        # 打印总结
        self.print_summary()


async def main():
    """主函数"""
    tester = DaVinciConnectionTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n\n💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()

#!/usr/bin/env python3
"""
测试ElevenLabs语音合成功能
"""

import asyncio
import aiohttp
import json
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_elevenlabs_speech():
    """测试ElevenLabs语音合成"""
    base_url = "http://127.0.0.1:8000"
    
    async with aiohttp.ClientSession() as session:
        logger.info("🎤 测试ElevenLabs语音合成...")
        
        synthesis_data = {
            "text": "Hello, this is a test of ElevenLabs speech synthesis integration.",
            "voice_id": "JBFqnCBsd6RMkjVDRZzb",  # Rachel voice
            "model_id": "eleven_multilingual_v2",
            "output_format": "mp3_44100_128",
            "provider": "elevenlabs"
        }
        
        try:
            async with session.post(
                f"{base_url}/api/speech/synthesize",
                json=synthesis_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                logger.info(f"响应状态码: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                    
                    if data.get('success'):
                        logger.info("✅ ElevenLabs语音合成成功")
                        if 'audio_url' in data.get('data', {}):
                            logger.info(f"   音频URL: {data['data']['audio_url']}")
                        if 'audio_path' in data.get('data', {}):
                            logger.info(f"   音频路径: {data['data']['audio_path']}")
                        
                        # 检查媒体库是否添加了音频文件
                        await asyncio.sleep(2)  # 等待媒体库更新
                        async with session.get(f"{base_url}/api/media-library/items?type_filter=audio") as media_response:
                            if media_response.status == 200:
                                media_data = await media_response.json()
                                if media_data.get('success'):
                                    audio_items = media_data.get('data', [])
                                    logger.info(f"📚 媒体库中现有 {len(audio_items)} 个音频文件")
                                    
                                    # 显示最新的音频文件
                                    if audio_items:
                                        latest_audio = audio_items[0]  # 按时间排序，最新的在前
                                        logger.info(f"   最新音频: {latest_audio.get('name')}")
                                        logger.info(f"   类别: {latest_audio.get('category')}")
                                        logger.info(f"   大小: {latest_audio.get('size')} bytes")
                    else:
                        logger.error(f"❌ ElevenLabs语音合成失败: {data.get('error')}")
                        if 'metadata' in data:
                            logger.error(f"   错误详情: {data['metadata']}")
                else:
                    error_text = await response.text()
                    logger.error(f"❌ API请求失败，状态码: {response.status}")
                    logger.error(f"   错误详情: {error_text}")
                    
        except Exception as e:
            logger.error(f"❌ 请求异常: {e}")

async def test_elevenlabs_voices():
    """测试获取ElevenLabs语音列表"""
    base_url = "http://127.0.0.1:8000"
    
    async with aiohttp.ClientSession() as session:
        logger.info("🔍 测试获取ElevenLabs语音列表...")
        
        try:
            async with session.get(f"{base_url}/api/speech/voices?provider=elevenlabs") as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('success'):
                        voices = data.get('data', {}).get('voices', [])
                        logger.info(f"✅ 成功获取 {len(voices)} 个ElevenLabs语音")
                        
                        # 显示前5个语音
                        for i, voice in enumerate(voices[:5]):
                            logger.info(f"   {i+1}. {voice.get('name')} ({voice.get('id')})")
                            if 'description' in voice:
                                logger.info(f"      描述: {voice.get('description')}")
                    else:
                        logger.error(f"❌ 获取语音列表失败: {data.get('error')}")
                else:
                    error_text = await response.text()
                    logger.error(f"❌ API请求失败，状态码: {response.status}")
                    logger.error(f"   错误详情: {error_text}")
        except Exception as e:
            logger.error(f"❌ 请求异常: {e}")

async def main():
    """主函数"""
    logger.info("🚀 开始测试ElevenLabs集成功能...")
    logger.info("="*60)
    
    # 1. 测试获取语音列表
    await test_elevenlabs_voices()
    
    logger.info("\n" + "-"*40)
    
    # 2. 测试语音合成
    await test_elevenlabs_speech()
    
    logger.info("\n" + "="*60)
    logger.info("📊 ElevenLabs测试完成")

if __name__ == "__main__":
    asyncio.run(main())

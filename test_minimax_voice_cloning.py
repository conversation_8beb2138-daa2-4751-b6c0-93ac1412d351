#!/usr/bin/env python3
"""
测试MiniMax语音克隆功能
"""

import asyncio
import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.services.enhanced_ai_services import enhanced_ai_service_manager
from src.services.base_types import AIRequest, ServiceType

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_service_availability():
    """测试服务可用性"""
    print("\n" + "="*50)
    print("🔍 检查语音克隆服务可用性")
    print("="*50)
    
    # 获取支持语音克隆的服务
    voice_cloning_services = enhanced_ai_service_manager.registry.get_available_services(ServiceType.VOICE_CLONING)
    print(f"支持语音克隆的服务数量: {len(voice_cloning_services)}")
    for service in voice_cloning_services:
        print(f"  - {service.provider_name}/{service.server_name} (可用: {service.is_available})")
    
    return voice_cloning_services

async def test_minimax_voice_cloning():
    """测试MiniMax语音克隆功能"""
    print("\n" + "="*50)
    print("🎤 测试MiniMax语音克隆功能")
    print("="*50)
    
    # 测试语音克隆（使用测试参数）
    request = AIRequest(
        service_type=ServiceType.VOICE_CLONING,
        provider="minimax",
        prompt="测试语音克隆功能",
        parameters={
            "voice_id": "TestVoice_" + str(int(asyncio.get_event_loop().time())),
            "file": "test_audio.wav",  # 这里应该是实际的音频文件路径
            "text": "这是一个测试语音克隆的文本内容。",
            "timeout": 120
        }
    )
    
    try:
        print(f"🔄 开始语音克隆...")
        print(f"Voice ID: {request.parameters['voice_id']}")
        print(f"文本内容: {request.parameters['text']}")
        
        response = await enhanced_ai_service_manager.process_request(request)
        if response.success:
            print("✅ 语音克隆成功!")
            if isinstance(response.data, dict):
                print(f"克隆结果: {response.data}")
                if 'voice_id' in response.data:
                    print(f"生成的Voice ID: {response.data['voice_id']}")
                if 'status' in response.data:
                    print(f"克隆状态: {response.data['status']}")
            else:
                print(f"克隆结果: {response.data}")
            print(f"提供商: {response.provider}")
            print(f"处理时间: {response.processing_time:.2f}秒")
        else:
            print(f"❌ 语音克隆失败: {response.error}")
            if hasattr(response, 'details'):
                print(f"详细信息: {response.details}")
    except Exception as e:
        print(f"❌ 语音克隆异常: {e}")
        import traceback
        traceback.print_exc()

async def test_elevenlabs_voice_cloning():
    """测试ElevenLabs语音克隆功能"""
    print("\n" + "="*50)
    print("🎵 测试ElevenLabs语音克隆功能")
    print("="*50)
    
    # 测试语音克隆（使用测试参数）
    request = AIRequest(
        service_type=ServiceType.VOICE_CLONING,
        provider="elevenlabs",
        prompt="测试ElevenLabs语音克隆功能",
        parameters={
            "name": "TestVoice_EL_" + str(int(asyncio.get_event_loop().time())),
            "files": ["test_audio.wav"],  # 这里应该是实际的音频文件路径列表
            "description": "这是一个测试语音克隆的声音",
            "timeout": 120
        }
    )
    
    try:
        print(f"🔄 开始ElevenLabs语音克隆...")
        print(f"Voice Name: {request.parameters['name']}")
        print(f"描述: {request.parameters['description']}")
        
        response = await enhanced_ai_service_manager.process_request(request)
        if response.success:
            print("✅ ElevenLabs语音克隆成功!")
            if isinstance(response.data, dict):
                print(f"克隆结果: {response.data}")
                if 'voice_id' in response.data:
                    print(f"生成的Voice ID: {response.data['voice_id']}")
                if 'status' in response.data:
                    print(f"克隆状态: {response.data['status']}")
            else:
                print(f"克隆结果: {response.data}")
            print(f"提供商: {response.provider}")
            print(f"处理时间: {response.processing_time:.2f}秒")
        else:
            print(f"❌ ElevenLabs语音克隆失败: {response.error}")
            if hasattr(response, 'details'):
                print(f"详细信息: {response.details}")
    except Exception as e:
        print(f"❌ ElevenLabs语音克隆异常: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主测试函数"""
    print("🧪 开始测试语音克隆功能...")
    
    # 初始化服务管理器
    await enhanced_ai_service_manager.initialize()
    
    # 检查服务可用性
    services = await test_service_availability()
    
    if not services:
        print("❌ 没有可用的语音克隆服务")
        return
    
    # 测试MiniMax语音克隆
    minimax_available = any(s.provider_name == "minimax" for s in services)
    if minimax_available:
        await test_minimax_voice_cloning()
    else:
        print("⚠️  MiniMax语音克隆服务不可用")
    
    # 测试ElevenLabs语音克隆
    elevenlabs_available = any(s.provider_name == "elevenlabs" for s in services)
    if elevenlabs_available:
        await test_elevenlabs_voice_cloning()
    else:
        print("⚠️  ElevenLabs语音克隆服务不可用")
    
    print("\n" + "="*50)
    print("🎉 语音克隆功能测试完成!")
    print("="*50)

if __name__ == "__main__":
    asyncio.run(main())

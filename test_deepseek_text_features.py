#!/usr/bin/env python3
"""
测试DeepSeek的文本功能：翻译、文案分析、提示词优化
"""

import asyncio
import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.services.enhanced_ai_services import enhanced_ai_service_manager
from src.services.base_types import AIRequest, ServiceType

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_deepseek_translation():
    """测试DeepSeek翻译功能"""
    print("\n" + "="*50)
    print("🌐 测试DeepSeek翻译功能")
    print("="*50)
    
    # 测试中文翻译成英文
    request = AIRequest(
        service_type=ServiceType.TRANSLATION,
        provider="deepseek",
        prompt="你好，世界！这是一个测试翻译功能的句子。",
        parameters={
            "source_language": "zh",
            "target_language": "en",
            "max_tokens": 1000,
            "temperature": 0.3
        }
    )
    
    try:
        response = await enhanced_ai_service_manager.process_request(request)
        if response.success:
            print("✅ 翻译成功!")
            print(f"原文: {request.prompt}")
            if isinstance(response.data, dict):
                print(f"译文: {response.data.get('translated_text', response.data)}")
                print(f"源语言: {response.data.get('source_language', 'unknown')}")
                print(f"目标语言: {response.data.get('target_language', 'unknown')}")
            else:
                print(f"译文: {response.data}")
            print(f"提供商: {response.provider}")
            print(f"处理时间: {response.processing_time:.2f}秒")
        else:
            print(f"❌ 翻译失败: {response.error}")
    except Exception as e:
        print(f"❌ 翻译异常: {e}")

async def test_deepseek_text_analysis():
    """测试DeepSeek文案分析功能"""
    print("\n" + "="*50)
    print("📝 测试DeepSeek文案分析功能")
    print("="*50)
    
    # 测试文案分析
    request = AIRequest(
        service_type=ServiceType.TEXT_ANALYSIS,
        provider="deepseek",
        prompt="春天来了，樱花盛开，微风轻拂过湖面，阳光透过树叶洒在小径上，一对恋人手牵手漫步在花园中，享受着这美好的时光。",
        parameters={
            "task": "content_analysis",
            "max_tokens": 2000,
            "temperature": 0.7
        }
    )
    
    try:
        response = await enhanced_ai_service_manager.process_request(request)
        if response.success:
            print("✅ 文案分析成功!")
            print(f"原文: {request.prompt}")
            if isinstance(response.data, dict):
                print(f"分析结果: {response.data.get('analysis', response.data)}")
                print(f"任务类型: {response.data.get('task_type', 'unknown')}")
                print(f"字数统计: {response.data.get('word_count', 0)}")
            else:
                print(f"分析结果: {response.data}")
            print(f"提供商: {response.provider}")
            print(f"处理时间: {response.processing_time:.2f}秒")
        else:
            print(f"❌ 文案分析失败: {response.error}")
    except Exception as e:
        print(f"❌ 文案分析异常: {e}")

async def test_deepseek_prompt_enhancement():
    """测试DeepSeek提示词优化功能"""
    print("\n" + "="*50)
    print("🚀 测试DeepSeek提示词优化功能")
    print("="*50)
    
    # 测试提示词优化
    request = AIRequest(
        service_type=ServiceType.TEXT_ANALYSIS,
        provider="deepseek",
        prompt="画一只猫",
        parameters={
            "task": "prompt_enhancement",
            "target_type": "image_generation",
            "enhancement_style": "detailed",
            "max_tokens": 1500,
            "temperature": 0.7
        }
    )
    
    try:
        response = await enhanced_ai_service_manager.process_request(request)
        if response.success:
            print("✅ 提示词优化成功!")
            print(f"原始提示词: {request.prompt}")
            if isinstance(response.data, dict):
                print(f"优化后提示词: {response.data.get('analysis', response.data)}")
                print(f"任务类型: {response.data.get('task_type', 'unknown')}")
            else:
                print(f"优化后提示词: {response.data}")
            print(f"提供商: {response.provider}")
            print(f"处理时间: {response.processing_time:.2f}秒")
        else:
            print(f"❌ 提示词优化失败: {response.error}")
    except Exception as e:
        print(f"❌ 提示词优化异常: {e}")

async def test_service_availability():
    """测试服务可用性"""
    print("\n" + "="*50)
    print("🔍 检查DeepSeek服务可用性")
    print("="*50)
    
    # 获取支持文本分析的服务
    text_analysis_services = enhanced_ai_service_manager.registry.get_available_services(ServiceType.TEXT_ANALYSIS)
    print(f"支持文本分析的服务数量: {len(text_analysis_services)}")
    for service in text_analysis_services:
        print(f"  - {service.provider_name}/{service.server_name} (可用: {service.is_available})")
    
    # 获取支持翻译的服务
    translation_services = enhanced_ai_service_manager.registry.get_available_services(ServiceType.TRANSLATION)
    print(f"支持翻译的服务数量: {len(translation_services)}")
    for service in translation_services:
        print(f"  - {service.provider_name}/{service.server_name} (可用: {service.is_available})")

async def main():
    """主测试函数"""
    print("🧪 开始测试DeepSeek文本功能...")
    
    # 初始化服务管理器
    await enhanced_ai_service_manager.initialize()
    
    # 检查服务可用性
    await test_service_availability()
    
    # 测试各项功能
    await test_deepseek_translation()
    await test_deepseek_text_analysis()
    await test_deepseek_prompt_enhancement()
    
    print("\n" + "="*50)
    print("🎉 测试完成!")
    print("="*50)

if __name__ == "__main__":
    asyncio.run(main())

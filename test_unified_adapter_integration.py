#!/usr/bin/env python3
"""
统一适配器架构集成测试
验证新的统一适配器系统是否正常工作
"""

import asyncio
import json
import logging
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_unified_adapter_system():
    """测试统一适配器系统"""
    print("🧪 DaVinci AI Co-pilot Pro - 统一适配器架构测试")
    print("=" * 60)
    
    try:
        # 测试1: 导入统一适配器模块
        print("\n📦 测试1: 导入统一适配器模块")
        try:
            from src.services.unified_service_adapter import (
                UnifiedRequest, UnifiedResponse, UnifiedServiceManager
            )
            from src.services.unified_compatibility_layer import (
                unified_compatibility_layer, UnifiedServiceIntegration
            )
            print("✅ 统一适配器模块导入成功")
        except ImportError as e:
            print(f"❌ 统一适配器模块导入失败: {e}")
            return False
        
        # 测试2: 检查配置文件
        print("\n📋 测试2: 检查适配器配置文件")
        config_path = project_root / "config" / "service_adapters.json"
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            adapters = config.get("adapters", {})
            print(f"✅ 配置文件存在，包含 {len(adapters)} 个适配器:")
            for adapter_name in adapters.keys():
                print(f"   - {adapter_name}")
        else:
            print(f"❌ 配置文件不存在: {config_path}")
            return False
        
        # 测试3: 创建统一服务管理器
        print("\n🔧 测试3: 创建统一服务管理器")
        try:
            service_manager = UnifiedServiceManager(config_dir=project_root / "config")
            capabilities = service_manager.get_available_capabilities()
            print(f"✅ 统一服务管理器创建成功")
            print(f"   可用能力: {list(capabilities.keys())}")
            
            for capability, providers in capabilities.items():
                print(f"   - {capability}: {providers}")
        except Exception as e:
            print(f"❌ 统一服务管理器创建失败: {e}")
            return False
        
        # 测试4: 测试兼容层
        print("\n🔄 测试4: 测试兼容层")
        try:
            from src.services.base_types import AIRequest, ServiceType
            
            # 创建测试请求
            test_request = AIRequest(
                service_type=ServiceType("speech_synthesis"),
                provider="minimax",
                prompt="这是一个测试语音合成的文本",
                parameters={
                    "voice_id": "male-qn-qingse",
                    "model": "speech-02-hd"
                }
            )
            
            print("✅ 兼容层测试请求创建成功")
            print(f"   服务类型: {test_request.service_type}")
            print(f"   提供商: {test_request.provider}")
            print(f"   参数: {test_request.parameters}")
            
        except Exception as e:
            print(f"❌ 兼容层测试失败: {e}")
            return False
        
        # 测试5: 测试统一服务集成
        print("\n🎯 测试5: 测试统一服务集成")
        try:
            unified_integration = UnifiedServiceIntegration()
            available_services = unified_integration.get_available_services()
            
            print("✅ 统一服务集成创建成功")
            print(f"   可用服务: {list(available_services.keys())}")
            
            for service_type, providers in available_services.items():
                print(f"   - {service_type}: {providers}")
                
        except Exception as e:
            print(f"❌ 统一服务集成测试失败: {e}")
            return False
        
        # 测试6: 测试参数获取
        print("\n📊 测试6: 测试参数获取")
        try:
            # 测试语音合成参数
            speech_params = unified_integration.get_service_info("SPEECH_SYNTHESIS", "minimax")
            print("✅ 语音合成参数获取成功")
            print(f"   能力: {speech_params.get('capability')}")
            print(f"   参数数量: {len(speech_params.get('parameters', []))}")
            
            # 测试文本生成参数
            text_params = unified_integration.get_service_info("TEXT_GENERATION", "deepseek")
            print("✅ 文本生成参数获取成功")
            print(f"   能力: {text_params.get('capability')}")
            print(f"   参数数量: {len(text_params.get('parameters', []))}")
            
        except Exception as e:
            print(f"❌ 参数获取测试失败: {e}")
            return False
        
        # 测试7: 测试动态类型系统
        print("\n🔄 测试7: 测试动态类型系统")
        try:
            from src.services.base_types import (
                get_dynamic_service_types, get_dynamic_service_providers,
                is_valid_service_type, is_valid_service_provider
            )
            
            # 获取动态类型
            service_types = get_dynamic_service_types()
            service_providers = get_dynamic_service_providers()
            
            print(f"✅ 动态类型系统正常")
            print(f"   服务类型数量: {len(service_types)}")
            print(f"   服务提供商数量: {len(service_providers)}")
            
            # 测试验证函数
            print(f"   speech_synthesis 有效: {is_valid_service_type('speech_synthesis')}")
            print(f"   minimax 有效: {is_valid_service_provider('minimax')}")
            
        except Exception as e:
            print(f"❌ 动态类型系统测试失败: {e}")
            return False
        
        print("\n🎉 所有测试通过！统一适配器架构集成成功！")
        print("\n📋 架构特性总结:")
        print("   ✅ 内部统一接口，外部动态适配")
        print("   ✅ 配置驱动的参数映射和响应解析")
        print("   ✅ 支持MCP和直接API两种适配方式")
        print("   ✅ 完全向后兼容现有代码")
        print("   ✅ 动态加载和重载配置")
        print("   ✅ 统一的错误处理和日志记录")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n🔄 测试向后兼容性")
    
    try:
        # 导入现有的服务管理器
        from src.services.ai_service_manager import ai_service_manager
        from src.services.base_types import AIRequest, ServiceType
        
        # 创建传统格式的请求
        request = AIRequest(
            service_type=ServiceType("text_generation"),
            provider="deepseek",
            prompt="测试文本生成功能",
            parameters={"max_tokens": 100}
        )
        
        print("✅ 传统请求格式创建成功")
        print(f"   服务类型: {request.service_type}")
        print(f"   提供商: {request.provider}")
        
        # 注意：这里不实际执行请求，只测试格式兼容性
        print("✅ 向后兼容性测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 向后兼容性测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始统一适配器架构集成测试")
    
    # 基础架构测试
    basic_test_result = await test_unified_adapter_system()
    
    # 向后兼容性测试
    compatibility_test_result = await test_backward_compatibility()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"   基础架构测试: {'✅ 通过' if basic_test_result else '❌ 失败'}")
    print(f"   向后兼容性测试: {'✅ 通过' if compatibility_test_result else '❌ 失败'}")
    
    if basic_test_result and compatibility_test_result:
        print("\n🎉 统一适配器架构集成测试完全成功！")
        print("   现有功能将自动使用新的统一适配器架构")
        print("   新服务商可通过配置文件快速接入")
        print("   API差异问题得到完美解决")
        return True
    else:
        print("\n❌ 部分测试失败，需要进一步调试")
        return False


if __name__ == "__main__":
    asyncio.run(main())

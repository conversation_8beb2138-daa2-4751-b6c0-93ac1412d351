/**
 * AI服务管理模块
 * 处理AI服务的调用、状态管理和结果展示
 */

class AIServicesModule extends BaseComponent {
    constructor(element, options = {}) {
        super(element, options);
        this.activeService = null;
        this.serviceHistory = [];
        this.currentRequest = null;
    }

    get defaultOptions() {
        return {
            autoRefresh: true,
            refreshInterval: 30000,
            maxHistory: 50
        };
    }

    init() {
        super.init();
        this.loadServiceCapabilities();
        if (this.options.autoRefresh) {
            this.startAutoRefresh();
        }
    }

    render() {
        if (!this.element) return;

        this.element.innerHTML = `
            <div class="ai-services-container">
                <!-- 服务选择器 -->
                <div class="service-selector">
                    <div class="service-tabs">
                        <button class="service-tab active" data-service="text_generation">
                            <i class="icon-text"></i>
                            <span>文本生成</span>
                        </button>
                        <button class="service-tab" data-service="text_analysis">
                            <i class="icon-analysis"></i>
                            <span>文案分析</span>
                        </button>
                        <button class="service-tab" data-service="speech_synthesis">
                            <i class="icon-audio"></i>
                            <span>语音合成</span>
                        </button>
                        <button class="service-tab" data-service="video_generation">
                            <i class="icon-video"></i>
                            <span>视频生成</span>
                        </button>
                        <button class="service-tab" data-service="image_generation">
                            <i class="icon-image"></i>
                            <span>图像生成</span>
                        </button>
                        <button class="service-tab" data-service="translation">
                            <i class="icon-translate"></i>
                            <span>翻译</span>
                        </button>
                    </div>
                </div>

                <!-- 服务内容区域 -->
                <div class="service-content">
                    <!-- 文本生成面板 -->
                    <div class="service-panel active" id="text_generation">
                        <div class="panel-header">
                            <h3>AI文本生成</h3>
                            <div class="provider-selector">
                                <select id="text-provider" class="form-select">
                                    <option value="">自动选择</option>
                                    <option value="deepseek">DeepSeek</option>
                                    <option value="minimax">Minimax</option>
                                    <option value="volcano">火山引擎</option>
                                </select>
                            </div>
                        </div>
                        <div class="panel-body">
                            <div class="form-group">
                                <label for="text-prompt">输入提示词</label>
                                <textarea id="text-prompt" class="form-textarea" rows="4" 
                                         placeholder="请输入您的文本生成需求..."></textarea>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="text-max-tokens">最大长度</label>
                                    <input type="number" id="text-max-tokens" class="form-input" 
                                           value="2000" min="100" max="8000">
                                </div>
                                <div class="form-group">
                                    <label for="text-temperature">创造性</label>
                                    <input type="range" id="text-temperature" class="form-range" 
                                           min="0" max="1" step="0.1" value="0.7">
                                    <span class="range-value">0.7</span>
                                </div>
                            </div>
                            <div class="form-actions">
                                <button class="btn btn-primary" onclick="generateText()">
                                    <i class="icon-generate"></i>
                                    生成文本
                                </button>
                                <button class="btn btn-secondary" onclick="clearTextForm()">
                                    <i class="icon-clear"></i>
                                    清空
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 文案分析面板 -->
                    <div class="service-panel" id="text_analysis">
                        <div class="panel-header">
                            <h3>AI文案分析</h3>
                            <div class="provider-selector">
                                <select id="analysis-provider" class="form-select">
                                    <option value="">自动选择</option>
                                    <option value="deepseek">DeepSeek</option>
                                    <option value="minimax">Minimax</option>
                                    <option value="volcano">火山引擎</option>
                                </select>
                            </div>
                        </div>
                        <div class="panel-body">
                            <div class="form-group">
                                <label for="analysis-content">文案内容</label>
                                <textarea id="analysis-content" class="form-textarea" rows="6" 
                                         placeholder="请输入需要分析的文案内容..."></textarea>
                            </div>
                            <div class="form-group">
                                <label>分析类型</label>
                                <div class="checkbox-group">
                                    <label class="checkbox-item">
                                        <input type="checkbox" checked> 场景拆解
                                    </label>
                                    <label class="checkbox-item">
                                        <input type="checkbox" checked> 关键词提取
                                    </label>
                                    <label class="checkbox-item">
                                        <input type="checkbox" checked> 情感分析
                                    </label>
                                    <label class="checkbox-item">
                                        <input type="checkbox"> 视觉建议
                                    </label>
                                </div>
                            </div>
                            <div class="form-actions">
                                <button class="btn btn-primary" onclick="analyzeText()">
                                    <i class="icon-analyze"></i>
                                    分析文案
                                </button>
                                <button class="btn btn-secondary" onclick="clearAnalysisForm()">
                                    <i class="icon-clear"></i>
                                    清空
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 语音合成面板 -->
                    <div class="service-panel" id="speech_synthesis">
                        <div class="panel-header">
                            <h3>AI语音合成</h3>
                            <div class="provider-selector">
                                <select id="speech-provider" class="form-select">
                                    <option value="">自动选择</option>
                                    <option value="minimax">Minimax</option>
                                    <option value="volcano">火山引擎</option>
                                </select>
                            </div>
                        </div>
                        <div class="panel-body">
                            <div class="form-group">
                                <label for="speech-text">合成文本</label>
                                <textarea id="speech-text" class="form-textarea" rows="4" 
                                         placeholder="请输入需要合成语音的文本..."></textarea>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="speech-voice">声音类型</label>
                                    <select id="speech-voice" class="form-select">
                                        <option value="female">女声</option>
                                        <option value="male">男声</option>
                                        <option value="child">童声</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="speech-speed">语速</label>
                                    <input type="range" id="speech-speed" class="form-range" 
                                           min="0.5" max="2" step="0.1" value="1">
                                    <span class="range-value">1.0</span>
                                </div>
                            </div>
                            <div class="form-actions">
                                <button class="btn btn-primary" onclick="synthesizeSpeech()">
                                    <i class="icon-audio"></i>
                                    合成语音
                                </button>
                                <button class="btn btn-secondary" onclick="clearSpeechForm()">
                                    <i class="icon-clear"></i>
                                    清空
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 视频生成面板 -->
                    <div class="service-panel" id="video_generation">
                        <div class="panel-header">
                            <h3>AI视频生成</h3>
                            <div class="provider-selector">
                                <select id="video-provider" class="form-select">
                                    <option value="">自动选择</option>
                                    <option value="minimax">Minimax</option>
                                    <option value="volcano">火山引擎</option>
                                </select>
                            </div>
                        </div>
                        <div class="panel-body">
                            <div class="form-group">
                                <label for="video-prompt">视频描述</label>
                                <textarea id="video-prompt" class="form-textarea" rows="4" 
                                         placeholder="请描述您想要生成的视频内容..."></textarea>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="video-duration">时长(秒)</label>
                                    <input type="number" id="video-duration" class="form-input" 
                                           value="4" min="2" max="10">
                                </div>
                                <div class="form-group">
                                    <label for="video-ratio">宽高比</label>
                                    <select id="video-ratio" class="form-select">
                                        <option value="16:9">16:9 (横屏)</option>
                                        <option value="9:16">9:16 (竖屏)</option>
                                        <option value="1:1">1:1 (方形)</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-actions">
                                <button class="btn btn-primary" onclick="generateVideo()">
                                    <i class="icon-video"></i>
                                    生成视频
                                </button>
                                <button class="btn btn-secondary" onclick="clearVideoForm()">
                                    <i class="icon-clear"></i>
                                    清空
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 图像生成面板 -->
                    <div class="service-panel" id="image_generation">
                        <div class="panel-header">
                            <h3>AI图像生成</h3>
                            <div class="provider-selector">
                                <select id="image-provider" class="form-select">
                                    <option value="">自动选择</option>
                                    <option value="minimax">Minimax</option>
                                    <option value="volcano">火山引擎</option>
                                </select>
                            </div>
                        </div>
                        <div class="panel-body">
                            <div class="form-group">
                                <label for="image-prompt">图像描述</label>
                                <textarea id="image-prompt" class="form-textarea" rows="4" 
                                         placeholder="请描述您想要生成的图像..."></textarea>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="image-size">尺寸</label>
                                    <select id="image-size" class="form-select">
                                        <option value="512x512">512x512</option>
                                        <option value="768x768">768x768</option>
                                        <option value="1024x1024">1024x1024</option>
                                        <option value="1024x768">1024x768</option>
                                        <option value="768x1024">768x1024</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="image-style">风格</label>
                                    <select id="image-style" class="form-select">
                                        <option value="">默认</option>
                                        <option value="realistic">写实</option>
                                        <option value="cartoon">卡通</option>
                                        <option value="anime">动漫</option>
                                        <option value="oil_painting">油画</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-actions">
                                <button class="btn btn-primary" onclick="generateImage()">
                                    <i class="icon-image"></i>
                                    生成图像
                                </button>
                                <button class="btn btn-secondary" onclick="clearImageForm()">
                                    <i class="icon-clear"></i>
                                    清空
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 翻译面板 -->
                    <div class="service-panel" id="translation">
                        <div class="panel-header">
                            <h3>AI翻译</h3>
                            <div class="provider-selector">
                                <select id="translate-provider" class="form-select">
                                    <option value="">自动选择</option>
                                    <option value="deepseek">DeepSeek</option>
                                    <option value="minimax">Minimax</option>
                                    <option value="volcano">火山引擎</option>
                                </select>
                            </div>
                        </div>
                        <div class="panel-body">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="translate-from">源语言</label>
                                    <select id="translate-from" class="form-select">
                                        <option value="auto">自动检测</option>
                                        <option value="zh">中文</option>
                                        <option value="en">英语</option>
                                        <option value="ja">日语</option>
                                        <option value="ko">韩语</option>
                                        <option value="fr">法语</option>
                                        <option value="de">德语</option>
                                        <option value="es">西班牙语</option>
                                        <option value="ru">俄语</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="translate-to">目标语言</label>
                                    <select id="translate-to" class="form-select">
                                        <option value="en">英语</option>
                                        <option value="zh">中文</option>
                                        <option value="ja">日语</option>
                                        <option value="ko">韩语</option>
                                        <option value="fr">法语</option>
                                        <option value="de">德语</option>
                                        <option value="es">西班牙语</option>
                                        <option value="ru">俄语</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="translate-text">原文</label>
                                <textarea id="translate-text" class="form-textarea" rows="4" 
                                         placeholder="请输入需要翻译的文本..."></textarea>
                            </div>
                            <div class="form-actions">
                                <button class="btn btn-primary" onclick="translateText()">
                                    <i class="icon-translate"></i>
                                    翻译
                                </button>
                                <button class="btn btn-secondary" onclick="clearTranslateForm()">
                                    <i class="icon-clear"></i>
                                    清空
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 结果显示区域 -->
                <div class="result-container">
                    <div class="result-header">
                        <h3>处理结果</h3>
                        <div class="result-actions">
                            <button class="btn btn-sm btn-secondary" onclick="clearResults()">
                                <i class="icon-clear"></i>
                                清空结果
                            </button>
                            <button class="btn btn-sm btn-primary" onclick="exportResults()">
                                <i class="icon-export"></i>
                                导出结果
                            </button>
                        </div>
                    </div>
                    <div class="result-content" id="result-content">
                        <div class="empty-state">
                            <i class="icon-empty"></i>
                            <p>暂无处理结果</p>
                            <small>选择一个AI服务开始使用</small>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // 服务标签切换
        this.element.querySelectorAll('.service-tab').forEach(tab => {
            this.addEventListener(tab, 'click', (e) => {
                const service = e.target.closest('.service-tab').dataset.service;
                this.switchService(service);
            });
        });

        // 范围输入实时更新
        this.element.querySelectorAll('.form-range').forEach(range => {
            this.addEventListener(range, 'input', (e) => {
                const valueSpan = e.target.nextElementSibling;
                if (valueSpan && valueSpan.classList.contains('range-value')) {
                    valueSpan.textContent = e.target.value;
                }
            });
        });

        // 表单验证
        this.element.querySelectorAll('.form-textarea, .form-input').forEach(input => {
            this.addEventListener(input, 'blur', (e) => {
                this.validateInput(e.target);
            });
        });
    }

    switchService(serviceName) {
        // 更新标签状态
        this.element.querySelectorAll('.service-tab').forEach(tab => {
            tab.classList.toggle('active', tab.dataset.service === serviceName);
        });

        // 更新面板显示
        this.element.querySelectorAll('.service-panel').forEach(panel => {
            panel.classList.toggle('active', panel.id === serviceName);
        });

        this.activeService = serviceName;
        componentManager.emit('serviceChanged', { service: serviceName });
    }

    validateInput(input) {
        const value = input.value.trim();
        const isValid = value.length > 0;
        
        input.classList.toggle('invalid', !isValid);
        return isValid;
    }

    async loadServiceCapabilities() {
        try {
            const response = await fetch('/api/services/capabilities');
            const data = await response.json();
            
            if (data.success) {
                this.updateServiceAvailability(data.data);
            }
        } catch (error) {
            console.error('Failed to load service capabilities:', error);
        }
    }

    updateServiceAvailability(capabilities) {
        // 根据服务能力更新UI状态
        Object.entries(capabilities).forEach(([provider, services]) => {
            services.forEach(service => {
                const tab = this.element.querySelector(`[data-service="${service}"]`);
                if (tab) {
                    tab.classList.add('available');
                }
            });
        });
    }

    startAutoRefresh() {
        setInterval(() => {
            this.loadServiceCapabilities();
        }, this.options.refreshInterval);
    }

    addToHistory(request, response) {
        this.serviceHistory.unshift({
            timestamp: new Date(),
            service: request.service_type,
            provider: request.provider,
            request: request,
            response: response
        });

        // 限制历史记录数量
        if (this.serviceHistory.length > this.options.maxHistory) {
            this.serviceHistory = this.serviceHistory.slice(0, this.options.maxHistory);
        }

        componentManager.emit('historyUpdated', { history: this.serviceHistory });
    }

    showResult(result, type = 'text') {
        const resultContent = this.element.querySelector('#result-content');
        
        const resultItem = document.createElement('div');
        resultItem.className = 'result-item';
        resultItem.innerHTML = `
            <div class="result-meta">
                <span class="result-type">${type}</span>
                <span class="result-time">${new Date().toLocaleTimeString()}</span>
            </div>
            <div class="result-data">
                ${this.formatResult(result, type)}
            </div>
        `;

        // 移除空状态
        const emptyState = resultContent.querySelector('.empty-state');
        if (emptyState) {
            emptyState.remove();
        }

        resultContent.insertBefore(resultItem, resultContent.firstChild);
    }

    formatResult(result, type) {
        switch (type) {
            case 'text':
                return `<div class="text-result">${result}</div>`;
            case 'analysis':
                return this.formatAnalysisResult(result);
            case 'audio':
                return `<audio controls src="${result}"></audio>`;
            case 'video':
                return `<video controls src="${result}" style="max-width: 100%;"></video>`;
            case 'image':
                return `<img src="${result}" alt="Generated image" style="max-width: 100%;">`;
            default:
                return `<pre>${JSON.stringify(result, null, 2)}</pre>`;
        }
    }

    formatAnalysisResult(analysis) {
        if (analysis.scenes) {
            let html = '<div class="analysis-result">';
            html += '<h4>场景分析</h4>';
            
            analysis.scenes.forEach((scene, index) => {
                html += `
                    <div class="scene-item">
                        <h5>场景 ${index + 1}</h5>
                        <p><strong>描述：</strong>${scene.description}</p>
                        <p><strong>关键词：</strong>${scene.keywords?.join(', ') || '无'}</p>
                        <p><strong>时长：</strong>${scene.duration || 0}秒</p>
                        <p><strong>情感：</strong>${scene.emotion || '中性'}</p>
                    </div>
                `;
            });
            
            if (analysis.summary) {
                html += `
                    <div class="analysis-summary">
                        <h4>总结</h4>
                        <p><strong>总场景数：</strong>${analysis.summary.total_scenes}</p>
                        <p><strong>总时长：</strong>${analysis.summary.total_duration}秒</p>
                        <p><strong>主题：</strong>${analysis.summary.main_theme}</p>
                    </div>
                `;
            }
            
            html += '</div>';
            return html;
        }
        
        return `<pre>${JSON.stringify(analysis, null, 2)}</pre>`;
    }
}

// 注册组件
componentManager.registerComponent('AIServices', AIServicesModule);

/**
 * 语音合成模块
 * 处理ElevenLabs语音合成功能
 */

class SpeechSynthesisModule {
    constructor() {
        this.currentAudio = null;
        this.isGenerating = false;
        this.availableVoices = [];
        this.currentProvider = 'elevenlabs';
        this.initialized = false;

        // 音频播放器状态
        this.audioPlayers = new Map();

        // 事件监听器
        this.eventListeners = new Map();

        // 提供商切换状态管理
        this.isLoadingVoices = false; // 语音加载状态
        this.currentLoadingProvider = null; // 当前正在加载的提供商
        this.providerChangeTimeout = null; // 防抖定时器
        this.abortController = null; // 用于取消请求
    }

    /**
     * 初始化模块
     */
    async initialize() {
        try {
            // 检查语音合成标签页是否是当前活动标签
            const speechTab = document.getElementById('speech-synthesis');
            if (!speechTab || !speechTab.classList.contains('active')) {
                return false;
            }

            // 检查必要的DOM元素是否存在且可见
            const voiceSelector = document.getElementById('voice-selector');
            const providerSelector = document.getElementById('speech-provider');

            if (!voiceSelector || !providerSelector) {
                return false;
            }

            // 检查元素是否可见
            if (voiceSelector.offsetParent === null || providerSelector.offsetParent === null) {
                return false;
            }

            // 避免重复初始化
            if (this.initialized) {
                await this.loadAvailableVoices(); // 直接加载可用语音
                return true;
            }

            // 设置初始提供商
            this.currentProvider = providerSelector.value || 'elevenlabs';

            // 同时加载克隆声音和预设声音
            await Promise.all([
                this.loadCloneVoices(),
                this.loadAvailableVoices()
            ]);
            this.populateVoiceSelector();
            this.setupEventListeners();
            this.initializeUI();

            this.initialized = true;
            return true;
        } catch (error) {
            console.error('❌ Failed to initialize Speech Synthesis Module:', error);
            return false;
        }
    }

    // 废弃的语音搜索提示功能已移除

    /**
     * 加载常用语音列表
     */
    async loadFavoriteVoices() {
        try {
            console.log('🔄 Loading favorite voices...');
            const response = await fetch('/api/speech/voices/favorites');

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            console.log('📥 Favorite voices response:', data);

            if (data.success) {
                this.favoriteVoices = data.data.voices;
                console.log(`📋 Favorite voices:`, this.favoriteVoices);
                this.populateVoiceSelector(true); // 使用常用语音模式
                console.log(`✅ Loaded ${this.favoriteVoices.length} favorite voices`);
            } else {
                console.error('❌ Failed to load favorite voices:', data.error);
                // 如果加载常用语音失败，回退到加载所有语音
                await this.loadAvailableVoices();
            }
        } catch (error) {
            console.error('❌ Error loading favorite voices:', error);
            // 如果加载常用语音失败，回退到加载所有语音
            await this.loadAvailableVoices();
        }
    }

    /**
     * 加载克隆声音列表
     */
    async loadCloneVoices() {
        try {
            const response = await fetch('/api/speech/voices/clones');

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.success) {
                this.cloneVoices = data.data.voices || [];
                console.log(`✅ Loaded ${this.cloneVoices.length} clone voices`);
                return this.cloneVoices;
            } else {
                console.error('❌ Failed to load clone voices:', data.error);
                this.cloneVoices = [];
                return [];
            }
        } catch (error) {
            console.error('❌ Error loading clone voices:', error);
            this.cloneVoices = [];
            return [];
        }
    }

    /**
     * 加载可用语音列表 - 简化版，只获取真正可用的语音
     */
    async loadAvailableVoices(provider = null) {
        try {
            // 根据提供商选择API端点
            let url;
            if (provider && provider !== 'auto') {
                // 使用提供商特定的API端点
                url = `/api/speech/voices?provider=${provider}`;
            } else {
                // 使用简化API端点，获取所有可用语音
                url = '/api/speech/voices/available';
            }

            // 使用AbortController支持请求取消
            const fetchOptions = {};
            if (this.abortController) {
                fetchOptions.signal = this.abortController.signal;
            }

            const response = await fetch(url, fetchOptions);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.success) {
                this.availableVoices = data.data.voices;
                this.populateVoiceSelector(false); // 使用所有语音模式

                // 使用建议已移除，保持界面简洁

            } else {
                console.error('❌ Failed to load available voices:', data.error);
                this.showError('加载语音列表失败');
            }
        } catch (error) {
            console.error('❌ Error loading available voices:', error);
            this.showError('加载语音列表时发生错误');
        }
    }

    /**
     * 填充语音选择器 - 支持克隆声音和预设声音
     */
    populateVoiceSelector(useFavorites = false) {
        const voiceSelector = document.getElementById('voice-selector');
        if (!voiceSelector) {
            console.error('❌ Voice selector element not found');
            return;
        }

        // 清空现有选项，保留optgroup结构
        voiceSelector.innerHTML = `
            <option value="">选择语音...</option>
            <optgroup id="clone-voices-group" label="🎭 我的声音克隆" style="display: none;">
            </optgroup>
            <optgroup id="preset-voices-group" label="🎤 预设语音">
            </optgroup>
        `;

        const cloneGroup = document.getElementById('clone-voices-group');
        const presetGroup = document.getElementById('preset-voices-group');

        // 添加语音试听控制面板
        this.addVoicePreviewPanel();

        // 添加克隆声音
        if (this.cloneVoices && this.cloneVoices.length > 0) {
            cloneGroup.style.display = 'block';

            this.cloneVoices.forEach(voice => {
                const option = document.createElement('option');
                option.value = voice.voice_id || voice.id;
                option.textContent = `${voice.name} (${voice.provider.toUpperCase()})`;
                option.dataset.provider = voice.provider;
                option.dataset.category = 'clone';
                option.title = voice.description || `${voice.name} - 声音克隆`;
                cloneGroup.appendChild(option);
            });
        }

        // 添加预设声音
        const voices = useFavorites ? (this.favoriteVoices || []) : (this.availableVoices || []);
        if (voices && voices.length > 0) {
            voices.forEach(voice => {
                const option = document.createElement('option');
                option.value = voice.id;
                option.textContent = `${voice.name}${voice.language ? ` (${voice.language.toUpperCase()})` : ''}`;
                option.dataset.provider = voice.provider;
                option.dataset.source = voice.source || 'preset';
                option.title = voice.description || voice.name;
                presetGroup.appendChild(option);
            });
        }

        // 更新按钮状态
        this.updateButtonStates();

        console.log(`✅ Populated voice selector with ${this.cloneVoices?.length || 0} clone voices and ${voices?.length || 0} preset voices`);
    }

    /**
     * 添加语音试听控制面板
     */
    addVoicePreviewPanel() {
        // 检查是否已经存在试听面板
        let previewPanel = document.getElementById('voice-preview-panel');
        if (previewPanel) {
            return; // 已存在，不重复添加
        }

        // 创建试听面板HTML
        const previewHTML = `
            <div id="voice-preview-panel" class="voice-preview-panel" style="display: none;">
                <div class="preview-header">
                    <h4>🎵 语音试听</h4>
                    <button class="btn-close" onclick="window.speechSynthesisModule.hideVoicePreview()">×</button>
                </div>
                <div class="preview-content">
                    <div class="voice-info">
                        <span id="preview-voice-name">未选择语音</span>
                        <span id="preview-voice-provider" class="provider-badge">-</span>
                    </div>
                    <div class="preview-controls">
                        <button id="preview-play-btn" class="btn btn-primary" onclick="window.speechSynthesisModule.playVoicePreview()">
                            <i class="fas fa-play"></i> 试听
                        </button>
                        <button id="preview-loading-btn" class="btn btn-secondary" style="display: none;" disabled>
                            <i class="fas fa-spinner fa-spin"></i> 加载中...
                        </button>
                    </div>
                    <div class="preview-tips">
                        <small>💡 试听功能帮助您在正式合成前了解语音效果</small>
                    </div>
                </div>
                <audio id="preview-audio" style="display: none;" controls></audio>
            </div>
        `;

        // 将试听面板插入到语音选择器后面
        const voiceSelector = document.getElementById('voice-selector');
        if (voiceSelector && voiceSelector.parentNode) {
            voiceSelector.insertAdjacentHTML('afterend', previewHTML);
        }
    }

    /**
     * 显示语音试听面板
     */
    showVoicePreview(voiceId, voiceName, provider) {
        const previewPanel = document.getElementById('voice-preview-panel');
        const voiceNameEl = document.getElementById('preview-voice-name');
        const voiceProviderEl = document.getElementById('preview-voice-provider');

        if (previewPanel && voiceNameEl && voiceProviderEl) {
            voiceNameEl.textContent = voiceName || voiceId;
            voiceProviderEl.textContent = provider === 'elevenlabs' ? 'ElevenLabs' :
                provider === 'minimax' ? 'MiniMax' : provider;
            voiceProviderEl.className = `provider-badge ${provider}`;

            previewPanel.style.display = 'block';
            previewPanel.dataset.voiceId = voiceId;
            previewPanel.dataset.provider = provider;
        }
    }

    /**
     * 隐藏语音试听面板
     */
    hideVoicePreview() {
        const previewPanel = document.getElementById('voice-preview-panel');
        const previewAudio = document.getElementById('preview-audio');

        if (previewPanel) {
            previewPanel.style.display = 'none';
        }

        if (previewAudio) {
            previewAudio.pause();
            previewAudio.src = '';
        }
    }

    /**
     * 播放语音试听
     */
    async playVoicePreview() {
        const previewPanel = document.getElementById('voice-preview-panel');
        const playBtn = document.getElementById('preview-play-btn');
        const loadingBtn = document.getElementById('preview-loading-btn');
        const previewAudio = document.getElementById('preview-audio');

        if (!previewPanel) return;

        const voiceId = previewPanel.dataset.voiceId;
        const provider = previewPanel.dataset.provider;

        if (!voiceId || !provider) {
            this.showError('语音信息不完整');
            return;
        }

        try {
            // 显示加载状态
            playBtn.style.display = 'none';
            loadingBtn.style.display = 'inline-block';

            // 调用试听API
            const response = await fetch(`/api/speech/voices/${voiceId}/preview?provider=${provider}`);
            const data = await response.json();

            if (data.success) {
                const previewUrl = data.data.preview_url;

                if (previewUrl) {
                    // 设置音频源并播放
                    previewAudio.src = previewUrl;
                    previewAudio.style.display = 'block';

                    // 等待音频加载完成后播放
                    previewAudio.addEventListener('loadeddata', () => {
                        previewAudio.play().catch(error => {
                            console.error('Audio play failed:', error);
                            this.showError('音频播放失败');
                        });
                    }, { once: true });

                    // 显示成功信息
                    if (data.data.is_demo_url && data.data.cost === 'free') {
                        this.showSuccess(`🎵 免费预览播放中 - ${data.data.provider === 'minimax' ? 'MiniMax克隆语音' : '官方预览'}`);
                    } else if (data.data.is_generated_sample) {
                        if (data.data.is_bilingual) {
                            this.showSuccess(`🎵 中英文双语试听已生成 - "${data.data.sample_text}"`);
                        } else {
                            this.showInfo(`🎵 试听样本已生成 - "${data.data.sample_text}"`);
                        }
                    } else {
                        this.showInfo('🎵 开始播放语音试听');
                    }
                } else {
                    this.showError('未找到试听音频');
                }
            } else {
                this.showError(`试听失败: ${data.error}`);
            }
        } catch (error) {
            console.error('❌ Voice preview failed:', error);
            this.showError('获取语音试听失败');
        } finally {
            // 恢复按钮状态
            playBtn.style.display = 'inline-block';
            loadingBtn.style.display = 'none';
        }
    }

    /**
 * 设置事件监听器
 */
    setupEventListeners() {
        // 语音合成按钮
        const synthesizeBtn = document.getElementById('synthesize-btn');
        if (synthesizeBtn) {
            synthesizeBtn.addEventListener('click', () => this.handleSynthesizeClick());
        }

        // 清空按钮
        const clearBtn = document.getElementById('clear-btn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => this.handleClearClick());
        }

        // 语音选择器变化
        const voiceSelector = document.getElementById('voice-selector');
        if (voiceSelector) {
            voiceSelector.addEventListener('change', (e) => this.handleVoiceChange(e));
        }

        // 服务提供商选择器变化
        const providerSelector = document.getElementById('speech-provider');
        if (providerSelector) {
            providerSelector.addEventListener('change', (e) => this.handleProviderChange(e));
        }

        // 文本输入区域
        const speechText = document.getElementById('speech-text');
        if (speechText) {
            speechText.addEventListener('input', () => {
                this.updateCharacterCount();
                this.updateButtonStates(); // 同时更新按钮状态
            });
        }

        // 废弃的语音搜索功能已移除

        // 管理克隆声音按钮
        const manageCloneBtn = document.getElementById('manage-clone-voices-btn');
        if (manageCloneBtn) {
            manageCloneBtn.addEventListener('click', () => this.showCloneVoiceManager());
        }

        // 创建克隆声音按钮
        const createCloneBtn = document.getElementById('create-clone-voices-btn');
        if (createCloneBtn) {
            createCloneBtn.addEventListener('click', () => this.showVoiceCloneCreator());
        }

        // 声音克隆创建相关事件
        this.setupVoiceCloneEvents();

    }

    /**
     * 初始化UI
     */
    initializeUI() {
        this.updateCharacterCount();
        this.updateButtonStates();

        // 根据当前选中的提供商初始化UI
        const providerSelector = document.getElementById('speech-provider');
        if (providerSelector && providerSelector.value) {
            this.updateProviderSpecificUI(providerSelector.value);
        }
    }

    /**
     * 处理语音合成按钮点击
     */
    async handleSynthesizeClick() {
        const text = document.getElementById('speech-text')?.value?.trim();
        const voiceId = document.getElementById('voice-selector')?.value;
        const modelId = document.getElementById('voice-model')?.value || 'eleven_multilingual_v2';
        const outputFormat = document.getElementById('audio-quality')?.value || 'mp3_44100_128';

        // 获取当前选择的提供商
        const providerSelector = document.getElementById('speech-provider');
        const selectedProvider = providerSelector?.value || this.currentProvider || 'elevenlabs';

        // 首先检查服务状态
        const serviceAvailable = await this.checkServiceStatus(selectedProvider);
        if (!serviceAvailable) {
            this.showError('语音合成服务暂时不可用，请检查网络连接或稍后重试');
            return;
        }

        // 验证输入
        if (!text) {
            this.showError('请输入要转换为语音的文本');
            return;
        }

        if (!voiceId) {
            this.showError('请选择一个语音');
            return;
        }

        // 检查文本长度
        if (text.length > 5000) {
            this.showError('文本长度不能超过5000个字符');
            return;
        }

        console.log(`🎤 Synthesizing speech with provider: ${selectedProvider}, voice: ${voiceId}`);

        await this.synthesizeSpeech(text, voiceId, {
            model_id: modelId,
            output_format: outputFormat,
            provider: selectedProvider
        });
    }

    /**
     * 处理清空按钮点击
     */
    handleClearClick() {
        // 清空文本输入
        const speechText = document.getElementById('speech-text');
        if (speechText) {
            speechText.value = '';
        }

        // 重置语音选择
        const voiceSelector = document.getElementById('voice-selector');
        if (voiceSelector) {
            voiceSelector.value = '';
        }

        // 清空音频播放器
        this.clearAudioPlayer();

        // 更新UI状态
        this.updateCharacterCount();
        this.updateButtonStates();
    }

    /**
     * 处理语音选择变化
     */
    handleVoiceChange(event) {
        const selectedOption = event.target.selectedOptions[0];
        if (selectedOption && selectedOption.value) {
            this.currentProvider = selectedOption.dataset.provider || 'elevenlabs';

            // 显示语音试听面板
            const voiceId = selectedOption.value;
            const voiceName = selectedOption.textContent;
            const provider = selectedOption.dataset.provider || 'elevenlabs';

            this.showVoicePreview(voiceId, voiceName, provider);
        } else {
            // 如果没有选择语音，隐藏试听面板
            this.hideVoicePreview();
        }
        this.updateButtonStates();
    }

    /**
     * 处理服务提供商选择变化 - 带防抖和状态管理
     */
    async handleProviderChange(event) {
        const selectedProvider = event.target.value;

        // 清除之前的防抖定时器
        if (this.providerChangeTimeout) {
            clearTimeout(this.providerChangeTimeout);
        }

        // 取消之前的请求
        if (this.abortController) {
            this.abortController.abort();
        }

        // 防抖处理：延迟执行，避免快速切换时的重复调用
        this.providerChangeTimeout = setTimeout(async () => {
            await this.performProviderChange(selectedProvider);
        }, 300); // 300ms防抖延迟
    }

    /**
     * 执行提供商切换的实际逻辑
     */
    async performProviderChange(selectedProvider) {
        // 检查是否已经在加载中
        if (this.isLoadingVoices && this.currentLoadingProvider === selectedProvider) {
            return;
        }

        try {
            // 设置加载状态
            this.isLoadingVoices = true;
            this.currentLoadingProvider = selectedProvider;

            // 显示加载状态
            this.showVoiceLoadingState(selectedProvider);

            // 更新当前提供商
            this.currentProvider = selectedProvider;

            // 创建新的AbortController
            this.abortController = new AbortController();

            // 重新加载该提供商的语音列表
            await this.loadAvailableVoices(selectedProvider);

            // 重置语音选择
            const voiceSelector = document.getElementById('voice-selector');
            if (voiceSelector) {
                voiceSelector.value = '';
            }

            // 更新UI状态
            this.updateButtonStates();
            this.updateProviderSpecificUI(selectedProvider);

        } catch (error) {
            if (error.name !== 'AbortError') {
                console.error(`❌ Error changing provider to ${selectedProvider}:`, error);
                this.showProviderChangeError(selectedProvider, error);
            }
        } finally {
            // 清除加载状态
            this.isLoadingVoices = false;
            this.currentLoadingProvider = null;
            this.hideVoiceLoadingState();
        }
    }

    /**
     * 更新提供商特定的UI元素
     */
    updateProviderSpecificUI(provider) {

        // 更新帮助文本
        const helpText = document.getElementById('provider-help-text');

        // 获取UI元素
        const elevenlabsVoiceSettings = document.getElementById('elevenlabs-voice-settings');
        const minimaxParams = document.getElementById('minimax-params');

        if (provider === 'elevenlabs') {
            if (helpText) {
                helpText.textContent = 'ElevenLabs提供最高质量的AI语音合成';
            }
            // 显示ElevenLabs语音设置区域
            if (elevenlabsVoiceSettings) {
                elevenlabsVoiceSettings.style.display = 'block';

                // 初始化ElevenLabs参数控件
                this.initElevenlabsParams();
            }
            if (minimaxParams) {
                minimaxParams.style.display = 'none';
            }
        } else if (provider === 'minimax') {
            if (helpText) {
                helpText.textContent = 'MiniMax提供中文优化的语音合成服务，支持丰富的参数调节';
            }
            // 隐藏ElevenLabs设置，显示MiniMax参数
            if (elevenlabsVoiceSettings) {
                elevenlabsVoiceSettings.style.display = 'none';
            }
            if (minimaxParams) {
                minimaxParams.style.display = 'block';

                // 初始化MiniMax参数控件
                this.initMinimaxParams();
            }
        } else {
            // 自动选择模式，隐藏所有特定提供商的设置
            if (elevenlabsVoiceSettings) elevenlabsVoiceSettings.style.display = 'none';
            if (minimaxParams) minimaxParams.style.display = 'none';
        }
    }

    /**
     * 初始化ElevenLabs参数控件
     */
    initElevenlabsParams() {
        // 稳定性滑块
        const stabilitySlider = document.getElementById('elevenlabs-stability');
        const stabilityValue = document.getElementById('stability-value');
        if (stabilitySlider && stabilityValue) {
            stabilitySlider.addEventListener('input', (e) => {
                stabilityValue.textContent = e.target.value;
            });
        }

        // 相似度增强滑块
        const similaritySlider = document.getElementById('elevenlabs-similarity-boost');
        const similarityValue = document.getElementById('similarity-boost-value');
        if (similaritySlider && similarityValue) {
            similaritySlider.addEventListener('input', (e) => {
                similarityValue.textContent = e.target.value;
            });
        }

        // 风格强度滑块
        const styleSlider = document.getElementById('elevenlabs-style');
        const styleValue = document.getElementById('style-value');
        if (styleSlider && styleValue) {
            styleSlider.addEventListener('input', (e) => {
                styleValue.textContent = e.target.value;
            });
        }

        // 语速滑块
        const speedSlider = document.getElementById('elevenlabs-speed');
        const speedValue = document.getElementById('elevenlabs-speed-value');
        if (speedSlider && speedValue) {
            speedSlider.addEventListener('input', (e) => {
                speedValue.textContent = e.target.value;
            });
        }


    }

    /**
     * 初始化MiniMax参数控件
     */
    initMinimaxParams() {
        // 语速滑块
        const speedSlider = document.getElementById('minimax-speed');
        const speedValue = document.getElementById('speed-value');
        if (speedSlider && speedValue) {
            speedSlider.addEventListener('input', (e) => {
                speedValue.textContent = e.target.value;
            });
        }

        // 音量滑块
        const volumeSlider = document.getElementById('minimax-volume');
        const volumeValue = document.getElementById('volume-value');
        if (volumeSlider && volumeValue) {
            volumeSlider.addEventListener('input', (e) => {
                volumeValue.textContent = e.target.value;
            });
        }

        // 音调滑块
        const pitchSlider = document.getElementById('minimax-pitch');
        const pitchValue = document.getElementById('pitch-value');
        if (pitchSlider && pitchValue) {
            pitchSlider.addEventListener('input', (e) => {
                pitchValue.textContent = e.target.value;
            });
        }


    }

    /**
     * 显示语音加载状态
     */
    showVoiceLoadingState(provider) {
        const voiceSelector = document.getElementById('voice-selector');
        const helpText = document.getElementById('provider-help-text');

        if (voiceSelector) {
            voiceSelector.innerHTML = '<option value="">🔄 正在加载语音...</option>';
            voiceSelector.disabled = true;
        }

        if (helpText) {
            const providerName = provider === 'minimax' ? 'MiniMax' :
                provider === 'elevenlabs' ? 'ElevenLabs' : provider;
            helpText.textContent = `🔄 正在加载 ${providerName} 语音，请稍候...`;
        }


    }

    /**
     * 隐藏语音加载状态
     */
    hideVoiceLoadingState() {
        const voiceSelector = document.getElementById('voice-selector');

        if (voiceSelector) {
            voiceSelector.disabled = false;
        }


    }

    /**
     * 显示提供商切换错误
     */
    showProviderChangeError(provider, error) {
        const helpText = document.getElementById('provider-help-text');
        const voiceSelector = document.getElementById('voice-selector');

        if (helpText) {
            helpText.textContent = `❌ 加载 ${provider} 语音失败，请重试`;
            helpText.style.color = '#dc3545';

            // 3秒后恢复正常文本
            setTimeout(() => {
                helpText.style.color = '';
                this.updateProviderSpecificUI(this.currentProvider);
            }, 3000);
        }

        if (voiceSelector) {
            voiceSelector.innerHTML = '<option value="">❌ 加载失败，请重试</option>';
        }

        console.error(`❌ Provider change error for ${provider}:`, error);
    }

    /**
     * 获取ElevenLabs参数
     */
    getElevenlabsParams() {
        return {
            stability: parseFloat(document.getElementById('elevenlabs-stability')?.value || '0.5'),
            similarity_boost: parseFloat(document.getElementById('elevenlabs-similarity-boost')?.value || '0.8'),
            style: parseFloat(document.getElementById('elevenlabs-style')?.value || '0.0'),
            use_speaker_boost: document.getElementById('elevenlabs-use-speaker-boost')?.checked || true,
            speed: parseFloat(document.getElementById('elevenlabs-speed')?.value || '1.0')
        };
    }

    /**
     * 获取MiniMax参数
     */
    getMinimaxParams() {
        return {
            model: document.getElementById('minimax-model')?.value || 'speech-02-hd',
            emotion: document.getElementById('minimax-emotion')?.value || 'neutral',
            speed: parseFloat(document.getElementById('minimax-speed')?.value || '1.0'),
            volume: parseFloat(document.getElementById('minimax-volume')?.value || '1.0'),
            pitch: parseInt(document.getElementById('minimax-pitch')?.value || '0'),
            sample_rate: parseInt(document.getElementById('minimax-sample-rate')?.value || '24000'),
            format: document.getElementById('minimax-format')?.value || 'mp3',
            enable_ssml: document.getElementById('minimax-ssml')?.checked || false
        };
    }

    /**
     * 语音合成主方法
     */
    async synthesizeSpeech(text, voiceId, options = {}) {
        if (this.isGenerating) {
            this.showWarning('正在生成语音，请稍候...');
            return;
        }

        this.isGenerating = true;
        this.updateButtonStates();
        this.showProgress('正在生成语音...', 0);

        try {
            // 构建请求参数
            let requestParams = {
                text: text,
                voice_id: voiceId,
                provider: options.provider || 'elevenlabs'
            };

            // 根据提供商添加特定参数
            if (options.provider === 'minimax') {
                const minimaxParams = this.getMinimaxParams();
                requestParams = {
                    ...requestParams,
                    model: minimaxParams.model,
                    speed: minimaxParams.speed,
                    volume: minimaxParams.volume,
                    pitch: minimaxParams.pitch,
                    emotion: minimaxParams.emotion,
                    sample_rate: minimaxParams.sample_rate,
                    format: minimaxParams.format,
                    enable_ssml: minimaxParams.enable_ssml
                };
            } else {
                // ElevenLabs 参数
                requestParams.model_id = options.model_id || 'eleven_multilingual_v2';
                requestParams.output_format = options.output_format || 'mp3_44100_128';

                // 添加ElevenLabs语音设置参数
                const elevenlabsParams = this.getElevenlabsParams();
                requestParams.voice_settings = {
                    stability: elevenlabsParams.stability,
                    similarity_boost: elevenlabsParams.similarity_boost,
                    style: elevenlabsParams.style,
                    use_speaker_boost: elevenlabsParams.use_speaker_boost,
                    speed: elevenlabsParams.speed
                };
            }

            const response = await fetch('/api/speech/synthesize', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestParams)
            });

            const result = await response.json();

            if (result.success) {
                this.handleSynthesisSuccess(result.data);
                // 成功消息已在handleSynthesisSuccess中显示，避免重复
            } else {
                this.handleSynthesisError(result.error);
            }
        } catch (error) {
            console.error('Speech synthesis error:', error);
            this.handleSynthesisError('网络连接失败，请检查网络设置');
        } finally {
            this.isGenerating = false;
            this.updateButtonStates();
            this.hideProgress();
        }
    }

    /**
     * 处理合成成功
     */
    handleSynthesisSuccess(data) {
        // 清理音频URL
        const cleanAudioUrl = this.cleanAudioUrl(data.audio_url || data.audio_file);
        data.audio_url = cleanAudioUrl;
        data.audio_file = cleanAudioUrl;

        // 获取当前选择的语音信息
        const voiceSelector = document.getElementById('voice-selector');
        const selectedOption = voiceSelector?.selectedOptions[0];
        if (selectedOption) {
            data.voice_name = selectedOption.textContent;
            data.voice_id = selectedOption.value;
            data.provider = selectedOption.dataset.provider;
        }

        // 创建音频播放器
        if (cleanAudioUrl) {
            this.createAudioPlayer(cleanAudioUrl, data);
        }

        // 媒体库集成已在后端完成，无需前端重复添加

        // 显示成功消息
        this.showSuccess('语音合成成功！');


    }

    /**
     * 清理音频URL，移除额外的文本
     */
    cleanAudioUrl(audioUrl) {
        if (!audioUrl) return audioUrl;

        // 移除 ". Voice used:" 及其后面的所有内容
        let cleanUrl = audioUrl.replace(/\s*\.\s*Voice\s+used:.*$/i, '');

        // 移除其他可能的额外文本模式
        cleanUrl = cleanUrl.replace(/\s*\.\s*Provider:.*$/i, '');
        cleanUrl = cleanUrl.replace(/\s*\.\s*Model:.*$/i, '');

        // 移除多余的空格
        cleanUrl = cleanUrl.trim();

        return cleanUrl;
    }

    // 媒体库集成已移至后端，前端不再需要单独添加

    /**
     * 生成音频文件名称
     */
    generateAudioName(data) {
        const text = data.text || '';
        const voiceName = data.voice_name || '未知语音';
        const timestamp = new Date().toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });

        // 截取文本前20个字符作为文件名
        const shortText = text.length > 20 ? text.substring(0, 20) + '...' : text;
        return `${shortText} - ${voiceName} (${timestamp})`;
    }

    /**
     * 生成音频标签
     */
    generateAudioTags(data) {
        const tags = ['语音合成'];

        if (data.metadata?.provider) {
            tags.push(data.metadata.provider.toUpperCase());
        }

        if (data.voice_name) {
            tags.push(data.voice_name);
        }

        if (data.format) {
            tags.push(data.format.toUpperCase());
        }

        // 根据文本内容添加语言标签
        const text = data.text || '';
        if (/[\u4e00-\u9fff]/.test(text)) {
            tags.push('中文');
        }
        if (/[a-zA-Z]/.test(text)) {
            tags.push('英文');
        }

        return tags;
    }

    /**
     * 检查服务状态
     */
    async checkServiceStatus(provider = 'elevenlabs') {
        try {
            const response = await fetch('/health', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const result = await response.json();
                // 检查特定提供商的状态
                if (result.services && result.services[provider]) {
                    return result.services[provider].is_available === true;
                }
                // 如果没有找到特定服务，检查整体状态
                return result.status === 'healthy';
            }
            return false;
        } catch (error) {
            console.warn('Service status check failed:', error);
            return true; // 如果检查失败，假设服务可用，让实际请求处理错误
        }
    }

    /**
     * 处理合成错误
     */
    handleSynthesisError(error) {
        console.error('Speech synthesis failed:', error);

        // 根据错误类型显示不同的消息
        let userMessage = '语音合成失败';

        if (typeof error === 'string') {
            if (error.includes('SERVICE_UNAVAILABLE') || error.includes('is unavailable')) {
                userMessage = 'ElevenLabs服务暂时不可用，请检查网络连接或稍后重试';
            } else if (error.includes('quota') || error.includes('配额')) {
                userMessage = 'API配额不足，请检查您的ElevenLabs账户';
            } else if (error.includes('network') || error.includes('网络')) {
                userMessage = '网络连接失败，请检查网络设置';
            } else if (error.includes('voice') || error.includes('语音')) {
                userMessage = '选择的语音不可用，请选择其他语音';
            } else if (error.includes('text') || error.includes('文本')) {
                userMessage = '文本格式有误，请检查输入内容';
            } else if (error.includes('API_AUTHENTICATION_ERROR') || error.includes('unauthorized')) {
                userMessage = 'API密钥认证失败，请检查ElevenLabs API密钥配置';
            } else if (error.includes('MCP_SERVER_NOT_FOUND')) {
                userMessage = 'MCP服务器未找到，请检查服务配置';
            } else if (error.includes('MCP_CONNECTION_ERROR')) {
                userMessage = 'MCP服务器连接失败，请检查网络连接';
            }
        }

        this.showError(userMessage);
    }

    /**
     * 创建音频播放器
     */
    createAudioPlayer(audioUrl, data = {}) {
        const audioContainer = document.getElementById('audio-player-container');
        if (!audioContainer) return;

        // 清空现有内容
        audioContainer.innerHTML = '';

        // 创建音频播放器HTML
        const playerHTML = `
            <div class="audio-player">
                <div class="audio-info">
                    <h4>生成的语音</h4>
                    <p class="audio-details">
                        语音: ${data.voice_name || '未知'} | 
                        格式: ${data.output_format || 'MP3'} | 
                        时长: <span id="audio-duration">--:--</span>
                    </p>
                </div>
                <audio controls preload="metadata" id="generated-audio">
                    <source src="${audioUrl}" type="audio/mpeg">
                    您的浏览器不支持音频播放。
                </audio>
                <div class="audio-controls">
                    <button class="btn btn-secondary" onclick="window.speechSynthesisModule.downloadAudio('${audioUrl}')">
                        <i class="fas fa-download"></i> 下载音频
                    </button>
                    <button class="btn btn-primary" onclick="window.speechSynthesisModule.importToDavinci('${audioUrl}')">
                        <i class="fas fa-film"></i> 导入到DaVinci
                    </button>
                </div>
            </div>
        `;

        audioContainer.innerHTML = playerHTML;

        // 设置音频事件监听器
        const audioElement = document.getElementById('generated-audio');
        if (audioElement) {
            audioElement.addEventListener('loadedmetadata', () => {
                const duration = this.formatDuration(audioElement.duration);
                const durationSpan = document.getElementById('audio-duration');
                if (durationSpan) {
                    durationSpan.textContent = duration;
                }
            });

            audioElement.addEventListener('error', (e) => {
                console.error('Audio playback error:', e);
                this.showError('音频播放失败');
            });
        }

        // 显示音频容器
        audioContainer.style.display = 'block';
    }

    /**
     * 清空音频播放器
     */
    clearAudioPlayer() {
        const audioContainer = document.getElementById('audio-player-container');
        if (audioContainer) {
            audioContainer.innerHTML = '';
            audioContainer.style.display = 'none';
        }
    }

    /**
     * 下载音频文件
     */
    downloadAudio(audioUrl) {
        const link = document.createElement('a');
        link.href = audioUrl;
        link.download = `speech_${Date.now()}.mp3`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        this.showSuccess('音频下载已开始');
    }

    /**
     * 导入音频到DaVinci
     */
    async importToDavinci(audioUrl) {
        try {
            this.showInfo('正在导入音频到DaVinci Resolve...');

            // 这里调用DaVinci集成API
            const response = await fetch('/api/davinci/audio/import', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    audio_url: audioUrl,
                    track_index: 1,
                    auto_sync: true
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showSuccess('音频已成功导入到DaVinci时间线');
            } else {
                this.showError('音频导入失败: ' + result.error);
            }
        } catch (error) {
            console.error('Failed to import audio to DaVinci:', error);
            this.showError('音频导入时发生错误');
        }
    }

    /**
     * 从时间线提取文本
     */
    async extractTimelineText() {
        try {
            this.showInfo('正在从DaVinci时间线提取文本...');

            const response = await fetch('/api/davinci/timeline/extract-text', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const result = await response.json();

            if (result.success) {
                const textData = result.data;

                // 将提取的文本填入语音合成界面
                const speechTextArea = document.getElementById('speech-text');
                if (speechTextArea && textData.combined_text) {
                    speechTextArea.value = textData.combined_text;
                    this.updateCharacterCount();
                    this.updateButtonStates();
                }

                this.showSuccess(`成功提取了${textData.total_segments || 0}个文本片段`);
            } else {
                this.showError('提取时间线文本失败: ' + result.error);
            }
        } catch (error) {
            console.error('Failed to extract timeline text:', error);
            this.showError('提取时间线文本时发生错误');
        }
    }

    /**
     * 基于字幕生成配音
     */
    async generateSubtitleVoiceover() {
        try {
            const voiceId = document.getElementById('voice-selector')?.value;
            const modelId = document.getElementById('voice-model')?.value || 'eleven_multilingual_v2';
            const outputFormat = document.getElementById('audio-quality')?.value || 'mp3_44100_128';

            if (!voiceId) {
                this.showError('请先选择一个语音');
                return;
            }

            this.showInfo('正在基于字幕生成配音...');

            const response = await fetch('/api/davinci/subtitles/generate-voiceover', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    voice_id: voiceId,
                    model_id: modelId,
                    output_format: outputFormat
                })
            });

            const result = await response.json();

            if (result.success) {
                const data = result.data;
                this.showSuccess(`已开始为${data.subtitle_count}个字幕片段生成配音`);

                // 显示批量任务状态
                if (data.job_id) {
                    this.monitorBatchJob(data.job_id);
                }
            } else {
                this.showError('生成字幕配音失败: ' + result.error);
            }
        } catch (error) {
            console.error('Failed to generate subtitle voiceover:', error);
            this.showError('生成字幕配音时发生错误');
        }
    }

    /**
     * 监控批量任务进度
     */
    async monitorBatchJob(jobId) {
        const checkProgress = async () => {
            try {
                const response = await fetch(`/api/speech/batch/status/${jobId}`);
                const result = await response.json();

                if (result.success) {
                    const status = result.data;

                    // 更新进度显示
                    this.showProgress(
                        `批量配音进度: ${status.completed_items}/${status.total_items}`,
                        status.progress_percentage
                    );

                    if (status.status === 'completed') {
                        this.hideProgress();
                        this.showSuccess(`批量配音完成！成功生成${status.completed_items}个音频文件`);

                        // 通知媒体库更新
                        if (window.mediaLibrary) {
                            const event = new CustomEvent('mediaLibraryUpdate', {
                                detail: { action: 'batch_audio_completed', data: status }
                            });
                            document.dispatchEvent(event);
                        }
                    } else if (status.status === 'failed') {
                        this.hideProgress();
                        this.showError('批量配音失败: ' + status.error);
                    } else if (status.status === 'processing') {
                        // 继续监控
                        setTimeout(checkProgress, 3000);
                    }
                }
            } catch (error) {
                console.error('Failed to check batch job progress:', error);
                this.hideProgress();
            }
        };

        checkProgress();
    }

    // 工具方法
    updateCharacterCount() {
        const speechText = document.getElementById('speech-text');
        const charCount = document.getElementById('char-count');

        if (speechText && charCount) {
            const count = speechText.value.length;
            charCount.textContent = `${count}/5000`;

            if (count > 4500) {
                charCount.style.color = '#ff6b6b';
            } else if (count > 4000) {
                charCount.style.color = '#ffa726';
            } else {
                charCount.style.color = '#666';
            }
        }
    }

    updateButtonStates() {
        const synthesizeBtn = document.getElementById('synthesize-btn');
        const speechText = document.getElementById('speech-text');
        const voiceSelector = document.getElementById('voice-selector');

        if (synthesizeBtn) {
            const hasText = speechText && speechText.value.trim().length > 0;
            // 检查是否有有效的语音选择
            const hasVoice = voiceSelector && voiceSelector.value && voiceSelector.value !== '';

            synthesizeBtn.disabled = this.isGenerating || !hasText || !hasVoice;

            // 更新按钮文本，保持HTML结构
            const btnTextSpan = synthesizeBtn.querySelector('.btn-text');
            if (btnTextSpan) {
                btnTextSpan.textContent = this.isGenerating ? '生成中...' : '生成语音';
            } else {
                // 如果没有找到.btn-text元素，直接设置textContent作为后备
                synthesizeBtn.textContent = this.isGenerating ? '生成中...' : '生成语音';
            }


        }
    }

    formatDuration(seconds) {
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }

    // 消息显示方法
    showSuccess(message) {
        if (window.app && window.app.showSuccess) {
            window.app.showSuccess(message);
        } else {
            console.log('✅', message);
        }
    }

    showError(message) {
        if (window.app && window.app.showError) {
            window.app.showError(message);
        } else {
            console.error('❌', message);
        }
    }

    showInfo(message) {
        if (window.app && window.app.showInfo) {
            window.app.showInfo(message);
        } else {
            console.log('ℹ️', message);
        }
    }

    showWarning(message) {
        if (window.app && window.app.showWarning) {
            window.app.showWarning(message);
        } else {
            console.warn('⚠️', message);
        }
    }

    // 废弃的语音搜索功能已完全移除

    // 废弃的selectSearchedVoice函数已移除

    // 废弃的addToFavoritesOnly函数已移除

    // 废弃的addToFavorites函数已移除

    // 废弃的findVoiceInSearchResults函数已移除

    showProgress(message, progress = null) {
        const progressContainer = document.getElementById('synthesis-progress');
        const progressText = document.querySelector('#synthesis-progress .progress-text');
        const progressFill = document.querySelector('#synthesis-progress .progress-fill');

        if (progressContainer) {
            progressContainer.style.display = 'block';
        }

        if (progressText) {
            progressText.textContent = message;
        }

        if (progressFill && progress !== null) {
            progressFill.style.width = `${progress}%`;
        }
    }

    hideProgress() {
        const progressContainer = document.getElementById('synthesis-progress');
        if (progressContainer) {
            progressContainer.style.display = 'none';
        }
    }

    /**
     * 显示克隆声音管理界面
     */
    showCloneVoiceManager() {
        // 简单的提示，后续可以扩展为完整的管理界面
        alert('声音克隆管理功能正在开发中...\n\n当前功能：\n✅ 显示已有的克隆声音\n✅ 在语音合成中使用克隆声音\n\n即将推出：\n🔄 创建新的声音克隆\n🔄 删除克隆声音\n🔄 重命名克隆声音');
    }

    /**
     * 刷新克隆声音列表
     */
    async refreshCloneVoices() {
        await this.loadCloneVoices();
        this.populateVoiceSelector();
    }

    /**
     * 显示声音克隆创建界面
     */
    showVoiceCloneCreator() {
        const creator = document.getElementById('voice-clone-creator');
        if (creator) {
            creator.style.display = 'block';
            creator.scrollIntoView({ behavior: 'smooth' });
        }
    }

    /**
     * 隐藏声音克隆创建界面
     */
    hideVoiceCloneCreator() {
        const creator = document.getElementById('voice-clone-creator');
        if (creator) {
            creator.style.display = 'none';
        }
    }

    /**
     * 设置声音克隆相关事件
     */
    setupVoiceCloneEvents() {
        // 音频来源切换
        const audioSourceRadios = document.querySelectorAll('input[name="audio-source"]');
        audioSourceRadios.forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.switchAudioSource(e.target.value);
            });
        });

        // 从达芬奇提取音频按钮
        const extractBtn = document.getElementById('extract-davinci-audio-btn');
        if (extractBtn) {
            extractBtn.addEventListener('click', () => this.extractAudioFromDavinci());
        }

        // 文件上传
        const fileInput = document.getElementById('clone-audio-files');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => this.handleAudioFileUpload(e));
        }

        // 创建克隆按钮
        const createBtn = document.getElementById('create-voice-clone-btn');
        if (createBtn) {
            createBtn.addEventListener('click', () => this.createVoiceClone());
        }

        // 取消按钮
        const cancelBtn = document.getElementById('cancel-clone-creation-btn');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => this.hideVoiceCloneCreator());
        }

        // 克隆名称输入
        const nameInput = document.getElementById('clone-name');
        if (nameInput) {
            nameInput.addEventListener('input', () => {
                this.validateCloneName();
                this.updateCloneCreateButton();
            });
            nameInput.addEventListener('focus', () => this.showVoiceIdRequirements());
            nameInput.addEventListener('blur', () => this.hideVoiceIdRequirements());
        }

        // 克隆提供商切换
        const providerSelect = document.getElementById('clone-provider');
        if (providerSelect) {
            providerSelect.addEventListener('change', () => {
                this.validateCloneName();
                this.updateCloneCreateButton();
                // 如果输入框有焦点，更新提示
                if (nameInput && document.activeElement === nameInput) {
                    this.hideVoiceIdRequirements();
                    this.showVoiceIdRequirements();
                }
            });
        }
    }

    /**
     * 切换音频来源
     */
    switchAudioSource(source) {
        const davinciSection = document.getElementById('davinci-audio-section');
        const uploadSection = document.getElementById('upload-audio-section');

        if (source === 'davinci') {
            davinciSection.style.display = 'block';
            uploadSection.style.display = 'none';
        } else {
            davinciSection.style.display = 'none';
            uploadSection.style.display = 'block';
        }

        this.updateCloneCreateButton();
    }

    /**
     * 从达芬奇提取音频
     */
    async extractAudioFromDavinci() {
        try {
            const trackIndex = document.getElementById('audio-track-index').value;
            const extractBtn = document.getElementById('extract-davinci-audio-btn');

            // 显示加载状态
            extractBtn.disabled = true;
            extractBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 提取中...';

            const response = await fetch('/api/davinci/deep-integration/extract-audio', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    track_index: parseInt(trackIndex)
                })
            });

            if (response.ok) {
                const data = await response.json();

                if (data.success) {
                    // 保存提取的音频信息
                    this.extractedAudioPath = data.data.audio_path;
                    this.extractedAudioInfo = data.data;

                    // 显示详细成功消息
                    this.showSuccess(`
                        🎵 音频提取成功！<br>
                        📁 文件：${data.data.filename}<br>
                        📊 大小：${this.formatFileSize(data.data.file_size)}<br>
                        🎬 项目：${data.data.project_name}
                    `);

                    // 显示克隆参数设置
                    this.showCloneParameters();
                } else {
                    this.showError('音频提取失败：' + (data.error || '未知错误'));
                }
            } else {
                // 处理HTTP错误
                const errorData = await response.json().catch(() => ({ detail: '服务器错误' }));
                let errorMessage = errorData.detail || '未知错误';

                // 针对常见错误提供用户友好的提示
                if (errorMessage.includes('No project loaded')) {
                    errorMessage = `
                        ❌ 未检测到达芬奇项目<br><br>
                        📋 请确保：<br>
                        • DaVinci Resolve 正在运行<br>
                        • 已打开一个项目<br>
                        • 时间线中有音频内容<br>
                        • 已选择正确的音轨
                    `;
                } else if (errorMessage.includes('No timeline')) {
                    errorMessage = `
                        ❌ 未检测到时间线<br><br>
                        📋 请确保：<br>
                        • 已创建时间线<br>
                        • 时间线中有音频轨道<br>
                        • 音频轨道有内容
                    `;
                }

                this.showError(errorMessage);
            }
        } catch (error) {
            console.error('提取音频失败:', error);
            this.showError('音频提取失败：' + error.message);
        } finally {
            // 恢复按钮状态
            const extractBtn = document.getElementById('extract-davinci-audio-btn');
            extractBtn.disabled = false;
            extractBtn.innerHTML = '<i class="fas fa-download"></i> 从达芬奇提取音频';
        }
    }

    /**
     * 处理音频文件上传
     */
    handleAudioFileUpload(event) {
        const files = event.target.files;
        if (files && files.length > 0) {
            this.uploadedAudioFiles = Array.from(files);
            this.showSuccess(`已选择 ${files.length} 个音频文件`);
            this.showCloneParameters();
        }
    }

    /**
     * 显示克隆参数设置
     */
    showCloneParameters() {
        const parametersSection = document.getElementById('clone-parameters');
        if (parametersSection) {
            parametersSection.style.display = 'block';
            this.updateCloneCreateButton();
        }
    }

    /**
     * 验证克隆名称是否符合voice_id要求
     */
    validateCloneName() {
        const nameInput = document.getElementById('clone-name');
        const provider = document.getElementById('clone-provider').value;

        if (!nameInput) return true;

        const name = nameInput.value.trim();
        if (!name) return true; // 空值不验证

        // 只对MiniMax进行严格验证
        if (provider === 'minimax') {
            const validation = this.validateVoiceId(name);
            this.showVoiceIdValidation(validation);
            return validation.isValid;
        }

        return true;
    }

    /**
     * 验证voice_id格式（MiniMax官方要求）
     */
    validateVoiceId(name) {
        // 生成预期的voice_id
        const cleanName = name.replace(/[^a-zA-Z0-9_]/g, '_');
        let voiceId = cleanName;

        // 确保首字符为英文字母
        if (!voiceId || !voiceId[0].match(/[a-zA-Z]/)) {
            voiceId = `voice_${voiceId}`;
        }

        // 添加时间戳
        voiceId = `${voiceId}_${Date.now()}`;

        // 验证规则
        const errors = [];

        if (voiceId.length < 3 || voiceId.length > 256) {
            errors.push(`长度不符合要求[3,256]，当前长度: ${voiceId.length}`);
        }

        if (!voiceId[0].match(/[a-zA-Z]/)) {
            errors.push(`首字符必须为英文字母，当前首字符: '${voiceId[0]}'`);
        }

        if (!voiceId.match(/^[a-zA-Z0-9_]+$/)) {
            errors.push('只允许数字、字母、下划线');
        }

        if (voiceId.endsWith('_')) {
            errors.push('末位字符不可为下划线');
        }

        return {
            isValid: errors.length === 0,
            errors: errors,
            voiceId: voiceId,
            originalName: name
        };
    }

    /**
     * 显示voice_id验证结果（简化版）
     */
    showVoiceIdValidation(validation) {
        // 移除显示验证结果，只在控制台记录
        if (!validation.isValid) {
            console.warn('Voice ID validation failed:', validation.errors);
        }
    }

    /**
     * 显示voice_id要求提示（简化版）
     */
    showVoiceIdRequirements() {
        // 移除复杂的提示显示，只在控制台记录
        console.log('MiniMax voice_id requirements: 3-256 chars, start with letter, alphanumeric + underscore');
    }

    /**
     * 隐藏voice_id要求提示（简化版）
     */
    hideVoiceIdRequirements() {
        // 简化版，无需操作
    }

    /**
     * 更新创建克隆按钮状态
     */
    updateCloneCreateButton() {
        const createBtn = document.getElementById('create-voice-clone-btn');
        const nameInput = document.getElementById('clone-name');
        const audioSource = document.querySelector('input[name="audio-source"]:checked').value;

        if (!createBtn || !nameInput) return;

        let hasAudio = false;
        if (audioSource === 'davinci') {
            hasAudio = !!this.extractedAudioPath;
        } else {
            hasAudio = !!(this.uploadedAudioFiles && this.uploadedAudioFiles.length > 0);
        }

        const hasName = nameInput.value.trim().length > 0;
        const isValidName = this.validateCloneName();

        createBtn.disabled = !(hasAudio && hasName && isValidName);
    }

    /**
     * 创建声音克隆
     */
    async createVoiceClone() {
        try {
            const name = document.getElementById('clone-name').value.trim();
            const provider = document.getElementById('clone-provider').value;
            const audioSource = document.querySelector('input[name="audio-source"]:checked').value;

            if (!name) {
                this.showError('请输入克隆名称');
                return;
            }

            // 显示进度
            this.showCloneProgress('开始创建声音克隆...');

            let audioFiles = [];
            if (audioSource === 'davinci') {
                if (!this.extractedAudioPath) {
                    this.showError('请先提取音频');
                    return;
                }
                audioFiles = [this.extractedAudioPath];
            } else {
                if (!this.uploadedAudioFiles || this.uploadedAudioFiles.length === 0) {
                    this.showError('请选择音频文件');
                    return;
                }
                audioFiles = this.uploadedAudioFiles;
            }

            // 调用克隆创建API
            const result = await this.callVoiceCloneAPI(name, provider, audioFiles, audioSource);

            if (result.success) {
                this.showCloneProgress('声音克隆创建成功！', true);

                // 刷新克隆声音列表
                await this.refreshCloneVoices();

                // 隐藏创建界面
                setTimeout(() => {
                    this.hideVoiceCloneCreator();
                    this.resetCloneCreator();
                }, 2000);
            } else {
                this.showError('创建失败：' + (result.error || '未知错误'));
                this.hideCloneProgress();
            }
        } catch (error) {
            console.error('创建声音克隆失败:', error);
            this.showError('创建失败：' + error.message);
            this.hideCloneProgress();
        }
    }

    /**
     * 调用声音克隆API
     */
    async callVoiceCloneAPI(name, provider, audioFiles, audioSource) {
        try {
            const formData = new FormData();
            formData.append('name', name);
            formData.append('provider', provider);
            formData.append('audio_source', audioSource);

            if (audioSource === 'davinci') {
                // 达芬奇提取的音频
                formData.append('audio_path', this.extractedAudioPath);
            } else {
                // 上传的音频文件
                for (let i = 0; i < audioFiles.length; i++) {
                    formData.append('audio_files', audioFiles[i]);
                }
            }

            const response = await fetch('/api/speech/voices/clone/create', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            return result;
        } catch (error) {
            console.error('API调用失败:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 显示克隆进度
     */
    showCloneProgress(message, isComplete = false) {
        const progressContainer = document.getElementById('clone-progress');
        const progressText = document.querySelector('#clone-progress .progress-text');
        const progressFill = document.querySelector('#clone-progress .progress-fill');

        if (progressContainer && progressText) {
            progressContainer.style.display = 'block';
            progressText.textContent = message;

            if (isComplete) {
                progressFill.style.width = '100%';
                progressFill.style.backgroundColor = '#28a745';
            } else {
                progressFill.style.width = '50%';
                progressFill.style.backgroundColor = '#007bff';
            }
        }
    }

    /**
     * 隐藏克隆进度
     */
    hideCloneProgress() {
        const progressContainer = document.getElementById('clone-progress');
        if (progressContainer) {
            progressContainer.style.display = 'none';
        }
    }

    /**
     * 重置克隆创建器
     */
    resetCloneCreator() {
        // 重置表单
        const nameInput = document.getElementById('clone-name');
        const fileInput = document.getElementById('clone-audio-files');
        const parametersSection = document.getElementById('clone-parameters');

        if (nameInput) nameInput.value = '';
        if (fileInput) fileInput.value = '';
        if (parametersSection) parametersSection.style.display = 'none';

        // 清除数据
        this.extractedAudioPath = null;
        this.extractedAudioInfo = null;
        this.uploadedAudioFiles = null;

        // 重置按钮状态
        this.updateCloneCreateButton();
        this.hideCloneProgress();
    }

    /**
     * 格式化文件大小
     * @param {number} bytes - 字节数
     * @returns {string} 格式化后的文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// 创建全局实例
window.speechSynthesisModule = new SpeechSynthesisModule();

// 在DOM加载完成后初始化（仅当语音合成标签页是活动状态时）
document.addEventListener('DOMContentLoaded', function () {
    if (window.speechSynthesisModule) {
        // 检查语音合成标签页是否是默认活动标签
        const speechTab = document.getElementById('speech-synthesis');
        if (speechTab && speechTab.classList.contains('active')) {
            setTimeout(() => {
                window.speechSynthesisModule.initialize();
            }, 500);
        }
    }
});

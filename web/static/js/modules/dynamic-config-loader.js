/**
 * 动态配置加载器
 * 替换前端硬编码配置，实现100%动态化
 */

class DynamicConfigLoader {
    constructor() {
        this.serviceTypes = new Map();
        this.serviceProviders = new Map();
        this.selectorMappings = {};
        this.capabilityProviders = {};
        this.smartParameters = new Map();
        this.configLoaded = false;
        
        // 缓存配置，避免重复请求
        this.cache = {
            serviceTypes: null,
            serviceProviders: null,
            selectorMappings: null,
            capabilityProviders: null,
            lastUpdate: null
        };
        
        this.init();
    }
    
    async init() {
        console.log('🔄 初始化动态配置加载器...');
        try {
            await this.loadAllConfigurations();
            this.configLoaded = true;
            console.log('✅ 动态配置加载完成');
            
            // 触发配置加载完成事件
            window.dispatchEvent(new CustomEvent('dynamicConfigLoaded', {
                detail: {
                    serviceTypes: this.serviceTypes.size,
                    serviceProviders: this.serviceProviders.size,
                    capabilities: Object.keys(this.capabilityProviders).length
                }
            }));
            
        } catch (error) {
            console.error('❌ 动态配置加载失败:', error);
            this.loadFallbackConfig();
        }
    }
    
    async loadAllConfigurations() {
        console.log('📦 加载所有动态配置...');
        
        // 并行加载所有配置
        const [serviceTypes, serviceProviders, selectorMappings, capabilityProviders] = await Promise.all([
            this.fetchServiceTypes(),
            this.fetchServiceProviders(),
            this.fetchSelectorMappings(),
            this.fetchCapabilityProviders()
        ]);
        
        // 处理服务类型
        serviceTypes.forEach(type => {
            this.serviceTypes.set(type.name, {
                name: type.name,
                displayName: type.display_name,
                description: type.description,
                category: type.category,
                providers: type.providers
            });
        });
        
        // 处理服务提供商
        serviceProviders.forEach(provider => {
            this.serviceProviders.set(provider.name, {
                name: provider.name,
                displayName: provider.display_name,
                description: provider.description,
                type: provider.type,
                capabilities: provider.capabilities,
                status: provider.status
            });
        });
        
        // 处理选择器映射
        this.selectorMappings = selectorMappings;
        
        // 处理能力提供商映射
        this.capabilityProviders = capabilityProviders;
        
        // 更新缓存
        this.updateCache();
        
        console.log(`✅ 配置加载完成: ${this.serviceTypes.size} 服务类型, ${this.serviceProviders.size} 提供商`);
    }
    
    async fetchServiceTypes() {
        const response = await fetch('/api/config/service-types');
        const result = await response.json();
        
        if (!result.success) {
            throw new Error(`获取服务类型失败: ${result.message}`);
        }
        
        return result.data;
    }
    
    async fetchServiceProviders() {
        const response = await fetch('/api/config/service-providers');
        const result = await response.json();
        
        if (!result.success) {
            throw new Error(`获取服务提供商失败: ${result.message}`);
        }
        
        return result.data;
    }
    
    async fetchSelectorMappings() {
        const response = await fetch('/api/config/selector-mappings');
        const result = await response.json();
        
        if (!result.success) {
            throw new Error(`获取选择器映射失败: ${result.message}`);
        }
        
        return result.data;
    }
    
    async fetchCapabilityProviders() {
        const response = await fetch('/api/config/capability-providers');
        const result = await response.json();
        
        if (!result.success) {
            throw new Error(`获取能力提供商映射失败: ${result.message}`);
        }
        
        return result.data;
    }
    
    async fetchSmartParameters(provider, capability) {
        const cacheKey = `${provider}_${capability}`;
        
        if (this.smartParameters.has(cacheKey)) {
            return this.smartParameters.get(cacheKey);
        }
        
        try {
            const response = await fetch(`/api/config/smart-parameters?provider=${provider}&capability=${capability}`);
            const result = await response.json();
            
            if (result.success) {
                this.smartParameters.set(cacheKey, result.data);
                return result.data;
            } else {
                console.warn(`获取智能参数失败: ${result.message}`);
                return [];
            }
        } catch (error) {
            console.error('获取智能参数时发生错误:', error);
            return [];
        }
    }
    
    async validateParameters(provider, capability, parameters) {
        try {
            const response = await fetch('/api/config/validate-parameters', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    provider,
                    capability,
                    parameters
                })
            });
            
            const result = await response.json();
            return result.success ? result.data : { valid: false, errors: [result.message] };
            
        } catch (error) {
            console.error('参数验证时发生错误:', error);
            return { valid: false, errors: ['参数验证请求失败'] };
        }
    }
    
    // 公共接口方法
    getServiceTypes() {
        return Array.from(this.serviceTypes.values());
    }
    
    getServiceType(name) {
        return this.serviceTypes.get(name);
    }
    
    getServiceProviders() {
        return Array.from(this.serviceProviders.values());
    }
    
    getServiceProvider(name) {
        return this.serviceProviders.get(name);
    }
    
    getProvidersForCapability(capability) {
        return this.capabilityProviders[capability] || [];
    }
    
    getCapabilitiesForProvider(provider) {
        const providerObj = this.serviceProviders.get(provider);
        return providerObj ? providerObj.capabilities : [];
    }
    
    getSelectorMappings() {
        return this.selectorMappings;
    }
    
    getSelectorsForCapability(capability) {
        return this.selectorMappings[capability] || [];
    }
    
    getServiceTypesByCategory(category) {
        return this.getServiceTypes().filter(type => type.category === category);
    }
    
    isProviderAvailable(provider) {
        const providerObj = this.serviceProviders.get(provider);
        return providerObj && providerObj.status === 'active';
    }
    
    isCapabilitySupported(provider, capability) {
        const providerObj = this.serviceProviders.get(provider);
        return providerObj && providerObj.capabilities.includes(capability);
    }
    
    // 缓存管理
    updateCache() {
        this.cache = {
            serviceTypes: this.getServiceTypes(),
            serviceProviders: this.getServiceProviders(),
            selectorMappings: this.selectorMappings,
            capabilityProviders: this.capabilityProviders,
            lastUpdate: new Date()
        };
    }
    
    clearCache() {
        this.smartParameters.clear();
        this.cache = {
            serviceTypes: null,
            serviceProviders: null,
            selectorMappings: null,
            capabilityProviders: null,
            lastUpdate: null
        };
    }
    
    async reloadConfiguration() {
        console.log('🔄 重新加载动态配置...');
        this.clearCache();
        
        try {
            // 调用后端重新加载配置
            const response = await fetch('/api/config/reload', { method: 'POST' });
            const result = await response.json();
            
            if (result.success) {
                await this.loadAllConfigurations();
                console.log('✅ 配置重新加载成功');
                
                // 触发配置重新加载事件
                window.dispatchEvent(new CustomEvent('dynamicConfigReloaded', {
                    detail: result.data
                }));
                
                return true;
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('❌ 配置重新加载失败:', error);
            return false;
        }
    }
    
    // 健康检查
    async healthCheck() {
        try {
            const response = await fetch('/api/config/health');
            const result = await response.json();
            return result.success ? result.data : null;
        } catch (error) {
            console.error('健康检查失败:', error);
            return null;
        }
    }
    
    // 获取配置摘要
    getConfigurationSummary() {
        return {
            serviceTypes: this.serviceTypes.size,
            serviceProviders: this.serviceProviders.size,
            capabilities: Object.keys(this.capabilityProviders).length,
            selectorMappings: Object.keys(this.selectorMappings).length,
            smartParametersCache: this.smartParameters.size,
            configLoaded: this.configLoaded,
            lastUpdate: this.cache.lastUpdate
        };
    }
}

// 全局实例
window.dynamicConfigLoader = new DynamicConfigLoader();

// 导出为模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DynamicConfigLoader;
}

/**
 * DaVinci Resolve静帧联动功能模块
 * 提供从时间线获取当前帧并与AI服务集成的功能
 */

class FrameCaptureManager {
    constructor() {
        this.isConnected = false;
        this.currentFrame = null;
        this.playheadInfo = null;
        this.exportInProgress = false;
        
        this.init();
    }
    
    init() {
        this.createUI();
        this.bindEvents();
        this.checkStatus();
        
        // 定期更新播放头信息
        setInterval(() => {
            if (this.isConnected) {
                this.updatePlayheadInfo();
            }
        }, 1000);
    }
    
    createUI() {
        // 创建静帧捕获面板
        const frameCapturePanel = `
            <div class="frame-capture-panel" id="frameCapturePanel">
                <div class="panel-header">
                    <h3>📸 静帧联动</h3>
                    <div class="connection-status" id="frameConnectionStatus">
                        <span class="status-indicator offline"></span>
                        <span class="status-text">未连接</span>
                    </div>
                </div>
                
                <div class="playhead-info" id="playheadInfo">
                    <div class="info-row">
                        <label>当前帧:</label>
                        <span id="currentFrameNumber">--</span>
                    </div>
                    <div class="info-row">
                        <label>时间码:</label>
                        <span id="currentTimecode">--:--:--:--</span>
                    </div>
                    <div class="info-row">
                        <label>时间线长度:</label>
                        <span id="timelineDuration">--</span>
                    </div>
                </div>
                
                <div class="frame-actions">
                    <button class="btn btn-primary" id="captureFrameBtn" disabled>
                        <i class="fas fa-camera"></i> 捕获当前帧
                    </button>
                    
                    <div class="ai-integration">
                        <h4>AI服务集成</h4>
                        <div class="ai-service-buttons">
                            <button class="btn btn-ai" id="captureForImageGenBtn" disabled>
                                <i class="fas fa-image"></i> 用于图像生成
                            </button>
                            <button class="btn btn-ai" id="captureForVideoGenBtn" disabled>
                                <i class="fas fa-video"></i> 用于视频生成
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="export-settings" id="exportSettings">
                    <h4>导出设置</h4>
                    <div class="setting-row">
                        <label for="exportFormat">格式:</label>
                        <select id="exportFormat">
                            <option value="PNG">PNG</option>
                            <option value="JPEG">JPEG</option>
                        </select>
                    </div>
                    <div class="setting-row">
                        <label for="exportQuality">质量:</label>
                        <select id="exportQuality">
                            <option value="Best">最佳</option>
                            <option value="High">高</option>
                            <option value="Medium">中等</option>
                            <option value="Low">低</option>
                        </select>
                    </div>
                </div>
                
                <div class="export-progress" id="exportProgress" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                    <span class="progress-text">导出中...</span>
                </div>
                
                <div class="recent-captures" id="recentCaptures">
                    <h4>最近捕获</h4>
                    <div class="captures-list" id="capturesList">
                        <!-- 动态填充 -->
                    </div>
                </div>
            </div>
        `;
        
        // 将面板添加到主界面
        const sidebar = document.querySelector('.sidebar') || document.body;
        sidebar.insertAdjacentHTML('beforeend', frameCapturePanel);
    }
    
    bindEvents() {
        // 捕获当前帧按钮
        document.getElementById('captureFrameBtn').addEventListener('click', () => {
            this.captureCurrentFrame();
        });
        
        // AI服务集成按钮
        document.getElementById('captureForImageGenBtn').addEventListener('click', () => {
            this.captureForAI('image_generation');
        });
        
        document.getElementById('captureForVideoGenBtn').addEventListener('click', () => {
            this.captureForAI('video_generation');
        });
        
        // 刷新播放头信息
        document.addEventListener('keydown', (e) => {
            // 监听空格键或其他播放控制键
            if (e.code === 'Space' && this.isConnected) {
                setTimeout(() => this.updatePlayheadInfo(), 100);
            }
        });
    }
    
    async checkStatus() {
        try {
            const response = await fetch('/api/davinci/frame/status');
            const result = await response.json();
            
            if (result.success) {
                this.updateConnectionStatus(true, result.data);
            } else {
                this.updateConnectionStatus(false);
            }
        } catch (error) {
            console.error('Failed to check frame capture status:', error);
            this.updateConnectionStatus(false);
        }
    }
    
    updateConnectionStatus(connected, data = null) {
        this.isConnected = connected;
        
        const statusIndicator = document.querySelector('#frameConnectionStatus .status-indicator');
        const statusText = document.querySelector('#frameConnectionStatus .status-text');
        const captureBtn = document.getElementById('captureFrameBtn');
        const aiButtons = document.querySelectorAll('.ai-service-buttons button');
        
        if (connected && data) {
            statusIndicator.className = 'status-indicator online';
            statusText.textContent = '已连接';
            
            const frameAvailable = data.frame_export_available;
            captureBtn.disabled = !frameAvailable;
            aiButtons.forEach(btn => btn.disabled = !frameAvailable);
            
            if (data.current_frame !== null) {
                document.getElementById('currentFrameNumber').textContent = data.current_frame;
                document.getElementById('currentTimecode').textContent = data.current_timecode || '--:--:--:--';
            }
        } else {
            statusIndicator.className = 'status-indicator offline';
            statusText.textContent = '未连接';
            captureBtn.disabled = true;
            aiButtons.forEach(btn => btn.disabled = true);
        }
    }
    
    async updatePlayheadInfo() {
        if (!this.isConnected) return;
        
        try {
            const response = await fetch('/api/davinci/playhead/info');
            const result = await response.json();
            
            if (result.success) {
                this.playheadInfo = result.data;
                this.updatePlayheadDisplay();
            }
        } catch (error) {
            console.error('Failed to update playhead info:', error);
        }
    }
    
    updatePlayheadDisplay() {
        if (!this.playheadInfo) return;
        
        document.getElementById('currentFrameNumber').textContent = this.playheadInfo.current_frame;
        document.getElementById('currentTimecode').textContent = this.playheadInfo.current_timecode;
        document.getElementById('timelineDuration').textContent = `${this.playheadInfo.timeline_duration} 帧`;
    }
    
    async captureCurrentFrame() {
        if (this.exportInProgress) return;
        
        this.setExportProgress(true);
        
        try {
            const format = document.getElementById('exportFormat').value;
            const quality = document.getElementById('exportQuality').value;
            
            const response = await fetch('/api/davinci/frame/export', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    format: format,
                    quality: quality
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showSuccess('静帧导出成功!');
                this.addToRecentCaptures(result.data, 'manual');
                
                // 触发自定义事件
                this.dispatchFrameCapturedEvent(result.data);
            } else {
                this.showError('静帧导出失败: ' + result.message);
            }
        } catch (error) {
            console.error('Frame capture failed:', error);
            this.showError('静帧导出失败: ' + error.message);
        } finally {
            this.setExportProgress(false);
        }
    }
    
    async captureForAI(serviceType) {
        if (this.exportInProgress) return;
        
        this.setExportProgress(true, `为${serviceType}导出中...`);
        
        try {
            const response = await fetch('/api/davinci/frame/export-for-ai', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    ai_service_type: serviceType
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showSuccess(`静帧已导出用于${serviceType}!`);
                this.addToRecentCaptures(result.data, serviceType);
                
                // 触发AI集成事件
                this.dispatchAIFrameReadyEvent(result.data);
                
                // 如果有AI服务管理器，自动传递图片
                if (window.aiServiceManager) {
                    this.integrateWithAIService(result.data);
                }
            } else {
                this.showError('AI静帧导出失败: ' + result.message);
            }
        } catch (error) {
            console.error('AI frame capture failed:', error);
            this.showError('AI静帧导出失败: ' + error.message);
        } finally {
            this.setExportProgress(false);
        }
    }
    
    setExportProgress(inProgress, message = '导出中...') {
        this.exportInProgress = inProgress;
        const progressDiv = document.getElementById('exportProgress');
        const progressText = progressDiv.querySelector('.progress-text');
        
        if (inProgress) {
            progressText.textContent = message;
            progressDiv.style.display = 'block';
        } else {
            progressDiv.style.display = 'none';
        }
        
        // 禁用/启用按钮
        const buttons = document.querySelectorAll('#frameCapturePanel button');
        buttons.forEach(btn => btn.disabled = inProgress || !this.isConnected);
    }
    
    addToRecentCaptures(frameData, type) {
        const capturesList = document.getElementById('capturesList');
        
        const captureItem = document.createElement('div');
        captureItem.className = 'capture-item';
        captureItem.innerHTML = `
            <div class="capture-info">
                <span class="capture-frame">帧 ${frameData.frame_number || frameData.frame_number}</span>
                <span class="capture-time">${frameData.timecode || frameData.current_timecode}</span>
                <span class="capture-type">${this.getTypeLabel(type)}</span>
            </div>
            <div class="capture-actions">
                <button class="btn-small" onclick="frameCaptureManager.openFrame('${frameData.file_path}')">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn-small" onclick="frameCaptureManager.copyPath('${frameData.file_path}')">
                    <i class="fas fa-copy"></i>
                </button>
            </div>
        `;
        
        capturesList.insertBefore(captureItem, capturesList.firstChild);
        
        // 限制显示数量
        while (capturesList.children.length > 5) {
            capturesList.removeChild(capturesList.lastChild);
        }
    }
    
    getTypeLabel(type) {
        const labels = {
            'manual': '手动',
            'image_generation': '图像生成',
            'video_generation': '视频生成'
        };
        return labels[type] || type;
    }
    
    openFrame(filePath) {
        // 在新窗口中打开图片
        window.open(`file://${filePath}`, '_blank');
    }
    
    copyPath(filePath) {
        navigator.clipboard.writeText(filePath).then(() => {
            this.showSuccess('文件路径已复制到剪贴板');
        });
    }
    
    integrateWithAIService(frameData) {
        // 与AI服务管理器集成
        if (window.aiServiceManager && frameData.ai_service_type) {
            const event = new CustomEvent('frameReadyForAI', {
                detail: {
                    filePath: frameData.file_path,
                    serviceType: frameData.ai_service_type,
                    frameNumber: frameData.frame_number,
                    timecode: frameData.timecode
                }
            });
            document.dispatchEvent(event);
        }
    }
    
    dispatchFrameCapturedEvent(frameData) {
        const event = new CustomEvent('frameCaptured', {
            detail: frameData
        });
        document.dispatchEvent(event);
    }
    
    dispatchAIFrameReadyEvent(frameData) {
        const event = new CustomEvent('aiFrameReady', {
            detail: frameData
        });
        document.dispatchEvent(event);
    }
    
    showSuccess(message) {
        // 显示成功消息（可以集成到现有的通知系统）
        console.log('✅', message);
        if (window.showNotification) {
            window.showNotification(message, 'success');
        }
    }
    
    showError(message) {
        // 显示错误消息
        console.error('❌', message);
        if (window.showNotification) {
            window.showNotification(message, 'error');
        }
    }
}

// 全局实例
window.frameCaptureManager = new FrameCaptureManager();

/**
 * DaVinci Resolve集成模块
 * 处理与DaVinci Resolve的交互和媒体处理
 */

class DaVinciIntegrationModule extends BaseComponent {
    constructor(element, options = {}) {
        super(element, options);
        this.connectionStatus = 'disconnected';
        this.renderJobs = [];
    }

    get defaultOptions() {
        return {
            autoConnect: true,
            statusCheckInterval: 10000,
            renderCheckInterval: 5000
        };
    }

    init() {
        super.init();
        // 将实例保存到全局变量，供全局函数使用
        window.daVinciIntegration = this;
        this.checkConnectionStatus();
        if (this.options.autoConnect) {
            this.connectToDaVinci();
        }
        this.startStatusMonitoring();
    }

    render() {
        if (!this.element) return;

        this.element.innerHTML = `
            <div class="davinci-integration-container">
                <!-- 连接状态 -->
                <div class="connection-status">
                    <div class="status-indicator ${this.connectionStatus}">
                        <div class="status-dot"></div>
                        <span class="status-text">${this.getStatusText()}</span>
                    </div>
                    <div class="connection-actions">
                        <button class="btn btn-primary" onclick="connectToDaVinci()" 
                                ${this.connectionStatus === 'connected' ? 'disabled' : ''}>
                            <i class="icon-connect"></i>
                            连接DaVinci
                        </button>
                        <button class="btn btn-secondary" onclick="refreshStatus()">
                            <i class="icon-refresh"></i>
                            刷新状态
                        </button>
                    </div>
                </div>

                <!-- 项目管理功能已移除 -->

                <!-- 静帧联动功能 -->
                <div class="frame-capture-section ${this.connectionStatus !== 'connected' ? 'disabled' : ''}">
                    <div class="section-header">
                        <h3>📸 静帧联动</h3>
                        <div class="section-actions">
                            <button class="btn btn-sm btn-primary" onclick="daVinciIntegration.captureCurrentFrame()">
                                <i class="icon-camera"></i>
                                捕获当前帧
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="daVinciIntegration.refreshPlayheadInfo()">
                                <i class="icon-refresh"></i>
                                刷新播放头
                            </button>
                        </div>
                    </div>

                    <div class="playhead-info" id="playhead-info">
                        <div class="info-grid">
                            <div class="info-item">
                                <label>当前帧:</label>
                                <span id="current-frame">--</span>
                            </div>
                            <div class="info-item">
                                <label>时间码:</label>
                                <span id="current-timecode">--:--:--:--</span>
                            </div>
                            <div class="info-item">
                                <label>时间线长度:</label>
                                <span id="timeline-duration">--</span>
                            </div>
                        </div>
                    </div>

                    <div class="ai-frame-export">
                        <h4>AI服务集成</h4>
                        <div class="ai-export-buttons">
                            <button class="btn btn-ai" onclick="daVinciIntegration.exportFrameForAI('image_generation')">
                                <i class="icon-image"></i>
                                用于图像生成
                            </button>
                            <button class="btn btn-ai" onclick="daVinciIntegration.exportFrameForAI('video_generation')">
                                <i class="icon-video"></i>
                                用于视频生成
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 深度集成功能 -->
                <div class="deep-integration-section ${this.connectionStatus !== 'connected' ? 'disabled' : ''}">
                    <div class="section-header">
                        <h3>🤖 深度集成</h3>
                        <div class="section-actions">
                            <button class="btn btn-sm btn-primary" onclick="daVinciIntegration.analyzeContent()">
                                <i class="icon-analyze"></i>
                                分析内容
                            </button>
                        </div>
                    </div>

                    <div class="integration-features">
                        <div class="feature-card">
                            <h4>字幕生成</h4>
                            <p>AI自动生成字幕</p>
                            <button class="btn btn-feature" onclick="daVinciIntegration.generateSubtitles()">
                                生成字幕
                            </button>
                        </div>

                        <div class="feature-card">
                            <h4>智能标记</h4>
                            <p>AI分析并添加智能标记</p>
                            <button class="btn btn-feature" onclick="daVinciIntegration.addSmartMarkers()">
                                添加标记
                            </button>
                        </div>

                        <div class="feature-card">
                            <h4>音频分析</h4>
                            <p>提取音频用于AI分析</p>
                            <button class="btn btn-feature" onclick="daVinciIntegration.extractAudio()">
                                提取音频
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 媒体管理 -->
                <div class="media-management ${this.connectionStatus !== 'connected' ? 'disabled' : ''}">
                    <div class="section-header">
                        <h3>媒体管理</h3>
                    </div>

                    <div class="media-import">
                        <div class="form-group">
                            <label>导入媒体文件</label>
                            <div class="file-drop-zone" id="media-drop-zone">
                                <div class="drop-zone-content">
                                    <i class="icon-upload"></i>
                                    <p>拖拽文件到此处或点击选择</p>
                                    <small>支持视频、音频、图像文件</small>
                                </div>
                                <input type="file" id="media-file-input" multiple 
                                       accept="video/*,audio/*,image/*" style="display: none;">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="target-folder">目标文件夹</label>
                            <input type="text" id="target-folder" class="form-input" 
                                   placeholder="留空使用根文件夹">
                        </div>

                        <div class="form-actions">
                            <button class="btn btn-primary" onclick="importMedia()">
                                <i class="icon-import"></i>
                                导入媒体
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 时间线管理 -->
                <div class="timeline-management ${this.connectionStatus !== 'connected' ? 'disabled' : ''}">
                    <div class="section-header">
                        <h3>时间线管理</h3>
                    </div>

                    <div class="timeline-creation">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="timeline-name">时间线名称</label>
                                <input type="text" id="timeline-name" class="form-input" 
                                       placeholder="输入时间线名称">
                            </div>
                            <div class="form-group">
                                <label for="timeline-fps">帧率</label>
                                <select id="timeline-fps" class="form-select">
                                    <option value="24">24 fps</option>
                                    <option value="25">25 fps</option>
                                    <option value="30">30 fps</option>
                                    <option value="60">60 fps</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button class="btn btn-primary" onclick="createTimeline()">
                                <i class="icon-timeline"></i>
                                创建时间线
                            </button>
                        </div>
                    </div>

                    <div class="clip-management">
                        <h4>片段管理</h4>
                        <div class="clip-list" id="clip-list">
                            <div class="empty-state">
                                <i class="icon-empty"></i>
                                <p>暂无片段</p>
                                <small>导入媒体文件后在此显示</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 渲染导出 -->
                <div class="render-export ${this.connectionStatus !== 'connected' ? 'disabled' : ''}">
                    <div class="section-header">
                        <h3>渲染导出</h3>
                    </div>

                    <div class="export-settings">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="export-path">导出路径</label>
                                <input type="text" id="export-path" class="form-input" 
                                       placeholder="/path/to/exports">
                            </div>
                            <div class="form-group">
                                <label for="export-filename">文件名</label>
                                <input type="text" id="export-filename" class="form-input" 
                                       placeholder="final_video">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="export-format">格式</label>
                                <select id="export-format" class="form-select">
                                    <option value="mp4">MP4</option>
                                    <option value="mov">MOV</option>
                                    <option value="avi">AVI</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="export-quality">质量</label>
                                <select id="export-quality" class="form-select">
                                    <option value="high">高质量</option>
                                    <option value="medium">中等质量</option>
                                    <option value="low">低质量</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button class="btn btn-primary" onclick="startRender()">
                                <i class="icon-render"></i>
                                开始渲染
                            </button>
                            <button class="btn btn-secondary" onclick="checkRenderStatus()">
                                <i class="icon-status"></i>
                                检查状态
                            </button>
                        </div>
                    </div>

                    <div class="render-status">
                        <h4>渲染状态</h4>
                        <div class="render-jobs" id="render-jobs">
                            ${this.renderJobs.length > 0 ? this.renderRenderJobs() :
                '<div class="empty-state"><p>暂无渲染任务</p></div>'}
                        </div>
                    </div>
                </div>

                <!-- 系统信息 -->
                <div class="system-info ${this.connectionStatus !== 'connected' ? 'disabled' : ''}">
                    <div class="section-header">
                        <h3>系统信息</h3>
                    </div>
                    <div class="info-grid" id="system-info">
                        <div class="info-item">
                            <span class="info-label">DaVinci版本</span>
                            <span class="info-value">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">产品名称</span>
                            <span class="info-value">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">项目数量</span>
                            <span class="info-value">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">时间线数量</span>
                            <span class="info-value">-</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // 文件拖拽
        const dropZone = this.element.querySelector('#media-drop-zone');
        const fileInput = this.element.querySelector('#media-file-input');

        if (dropZone && fileInput) {
            this.addEventListener(dropZone, 'click', () => fileInput.click());

            this.addEventListener(dropZone, 'dragover', (e) => {
                e.preventDefault();
                dropZone.classList.add('drag-over');
            });

            this.addEventListener(dropZone, 'dragleave', () => {
                dropZone.classList.remove('drag-over');
            });

            this.addEventListener(dropZone, 'drop', (e) => {
                e.preventDefault();
                dropZone.classList.remove('drag-over');
                this.handleFilesDrop(e.dataTransfer.files);
            });

            this.addEventListener(fileInput, 'change', (e) => {
                this.handleFilesDrop(e.target.files);
            });
        }
    }

    getStatusText() {
        const statusTexts = {
            'disconnected': '未连接',
            'connecting': '连接中...',
            'connected': '已连接',
            'error': '连接错误'
        };
        return statusTexts[this.connectionStatus] || '未知状态';
    }

    async checkConnectionStatus() {
        try {
            const response = await fetch('/api/davinci/status');
            const data = await response.json();

            if (data.success) {
                this.connectionStatus = data.data.connected ? 'connected' : 'disconnected';
                this.updateSystemInfo(data.data);
            } else {
                this.connectionStatus = 'error';
            }
        } catch (error) {
            console.error('Failed to check DaVinci status:', error);
            this.connectionStatus = 'error';
        }

        this.updateConnectionUI();
    }

    async connectToDaVinci() {
        this.connectionStatus = 'connecting';
        this.updateConnectionUI();

        try {
            const response = await fetch('/api/davinci/connect', { method: 'POST' });
            const data = await response.json();

            if (data.success) {
                this.connectionStatus = 'connected';
                this.updateSystemInfo(data.data);
                // 连接成功，更新UI状态

                if (window.app) {
                    window.app.showSuccess('成功连接到DaVinci Resolve');
                }
            } else {
                this.connectionStatus = 'error';
                if (window.app) {
                    window.app.showError('连接DaVinci Resolve失败: ' + data.message);
                }
            }
        } catch (error) {
            console.error('Failed to connect to DaVinci:', error);
            this.connectionStatus = 'error';
            if (window.app) {
                window.app.showError('连接DaVinci Resolve时发生错误');
            }
        }

        this.updateConnectionUI();
    }

    updateConnectionUI() {
        const statusIndicator = this.element.querySelector('.status-indicator');
        const statusText = this.element.querySelector('.status-text');
        const connectBtn = this.element.querySelector('button[onclick="connectToDaVinci()"]');
        const sections = this.element.querySelectorAll('.media-management, .timeline-management, .render-export, .system-info');

        if (statusIndicator) {
            statusIndicator.className = `status-indicator ${this.connectionStatus}`;
        }

        if (statusText) {
            statusText.textContent = this.getStatusText();
        }

        if (connectBtn) {
            connectBtn.disabled = this.connectionStatus === 'connected' || this.connectionStatus === 'connecting';
        }

        sections.forEach(section => {
            section.classList.toggle('disabled', this.connectionStatus !== 'connected');
        });
    }

    updateSystemInfo(info) {
        const systemInfoGrid = this.element.querySelector('#system-info');
        if (systemInfoGrid && info) {
            const items = systemInfoGrid.querySelectorAll('.info-item');

            const values = [
                info.version || '-',
                info.product_name || '-',
                info.project_count || '-',
                info.timeline_count || '-'
            ];

            items.forEach((item, index) => {
                const valueSpan = item.querySelector('.info-value');
                if (valueSpan && values[index] !== undefined) {
                    valueSpan.textContent = values[index];
                }
            });
        }
    }

    // 项目管理功能已完全移除

    renderRenderJobs() {
        return this.renderJobs.map(job => `
            <div class="render-job">
                <div class="job-info">
                    <span class="job-id">任务 ${job.job_id}</span>
                    <span class="job-status ${job.status}">${job.status}</span>
                </div>
                <div class="job-progress">
                    <div class="progress">
                        <div class="progress-bar" style="width: ${job.completion_percentage || 0}%">
                            ${job.completion_percentage || 0}%
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    // 项目列表更新功能已移除

    handleFilesDrop(files) {
        const fileArray = Array.from(files);
        const validFiles = fileArray.filter(file => {
            return file.type.startsWith('video/') ||
                file.type.startsWith('audio/') ||
                file.type.startsWith('image/');
        });

        if (validFiles.length > 0) {
            console.log('Files to import:', validFiles.map(f => f.name));
            // 这里可以添加文件预览和导入逻辑
            if (window.app) {
                window.app.showInfo(`准备导入 ${validFiles.length} 个文件`);
            }
        } else {
            if (window.app) {
                window.app.showWarning('请选择有效的媒体文件');
            }
        }
    }

    startStatusMonitoring() {
        setInterval(() => {
            this.checkConnectionStatus();
        }, this.options.statusCheckInterval);

        setInterval(() => {
            if (this.connectionStatus === 'connected' && this.renderJobs.length > 0) {
                this.checkRenderStatus();
            }
        }, this.options.renderCheckInterval);
    }

    async checkRenderStatus() {
        try {
            const response = await fetch('/api/davinci/render/status');
            const data = await response.json();

            if (data.success) {
                this.renderJobs = data.data.jobs || [];
                this.updateRenderJobsUI();
            }
        } catch (error) {
            console.error('Failed to check render status:', error);
        }
    }

    updateRenderJobsUI() {
        const renderJobsElement = this.element.querySelector('#render-jobs');
        if (renderJobsElement) {
            renderJobsElement.innerHTML = this.renderJobs.length > 0 ?
                this.renderRenderJobs() :
                '<div class="empty-state"><p>暂无渲染任务</p></div>';
        }
    }

    // ==================== 静帧联动功能 ====================

    async refreshPlayheadInfo() {
        try {
            const response = await fetch('/api/davinci/playhead/info');
            const data = await response.json();

            if (data.success) {
                this.updatePlayheadDisplay(data.data);
                if (window.app) {
                    window.app.showSuccess('播放头信息已更新');
                }
            } else {
                if (window.app) {
                    window.app.showError('获取播放头信息失败');
                }
            }
        } catch (error) {
            console.error('Failed to get playhead info:', error);
            if (window.app) {
                window.app.showError('获取播放头信息时发生错误');
            }
        }
    }

    updatePlayheadDisplay(playheadData) {
        const currentFrameEl = this.element.querySelector('#current-frame');
        const currentTimecodeEl = this.element.querySelector('#current-timecode');
        const timelineDurationEl = this.element.querySelector('#timeline-duration');

        if (currentFrameEl) currentFrameEl.textContent = playheadData.current_frame || '--';
        if (currentTimecodeEl) currentTimecodeEl.textContent = playheadData.current_timecode || '--:--:--:--';
        if (timelineDurationEl) timelineDurationEl.textContent = `${playheadData.timeline_duration || '--'} 帧`;
    }

    async captureCurrentFrame() {
        try {
            const response = await fetch('/api/davinci/frame/export', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    format: 'PNG',
                    quality: 'Best'
                })
            });

            const data = await response.json();

            if (data.success) {
                if (window.app) {
                    window.app.showSuccess(`静帧导出成功: ${data.data.file_path}`);
                }
                this.showFramePreview(data.data);
            } else {
                if (window.app) {
                    window.app.showError('静帧导出失败: ' + data.message);
                }
            }
        } catch (error) {
            console.error('Failed to capture frame:', error);
            if (window.app) {
                window.app.showError('静帧导出时发生错误');
            }
        }
    }

    async exportFrameForAI(serviceType) {
        try {
            const response = await fetch('/api/davinci/frame/export-for-ai', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    ai_service_type: serviceType
                })
            });

            const data = await response.json();

            if (data.success) {
                if (window.app) {
                    window.app.showSuccess(`AI静帧导出成功，已准备用于${serviceType}`);
                }
                this.integrateWithAIService(data.data);
            } else {
                if (window.app) {
                    window.app.showError('AI静帧导出失败: ' + data.message);
                }
            }
        } catch (error) {
            console.error('Failed to export frame for AI:', error);
            if (window.app) {
                window.app.showError('AI静帧导出时发生错误');
            }
        }
    }

    showFramePreview(frameData) {
        const modal = document.createElement('div');
        modal.className = 'frame-preview-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>静帧预览</h3>
                    <button class="close-btn" onclick="this.closest('.frame-preview-modal').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="frame-info">
                        <p><strong>帧数:</strong> ${frameData.frame_number}</p>
                        <p><strong>时间码:</strong> ${frameData.timecode}</p>
                        <p><strong>分辨率:</strong> ${frameData.width}x${frameData.height}</p>
                        <p><strong>格式:</strong> ${frameData.format}</p>
                        <p><strong>文件路径:</strong> ${frameData.file_path}</p>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    integrateWithAIService(frameData) {
        const event = new CustomEvent('frameReadyForAI', {
            detail: frameData
        });
        document.dispatchEvent(event);
    }

    // ==================== 深度集成功能 ====================

    async analyzeContent() {
        try {
            const response = await fetch('/api/davinci/deep-integration/analyze-content', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    analysis_type: 'comprehensive',
                    confidence_threshold: 0.7,
                    auto_apply: false
                })
            });

            const data = await response.json();

            if (data.success) {
                if (window.app) {
                    window.app.showSuccess('内容分析完成');
                }
                this.showAnalysisResults(data.data);
            } else {
                if (window.app) {
                    window.app.showError('内容分析失败: ' + data.message);
                }
            }
        } catch (error) {
            console.error('Failed to analyze content:', error);
            if (window.app) {
                window.app.showError('内容分析时发生错误');
            }
        }
    }

    async generateSubtitles() {
        try {
            const response = await fetch('/api/davinci/deep-integration/generate-subtitles', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    track_index: 1,
                    language: 'zh-CN',
                    ai_service: 'deepseek'
                })
            });

            const data = await response.json();

            if (data.success) {
                if (window.app) {
                    window.app.showSuccess(`字幕生成成功，共${data.data.subtitles.length}条字幕`);
                }
                this.showSubtitleResults(data.data);
            } else {
                if (window.app) {
                    window.app.showError('字幕生成失败: ' + data.message);
                }
            }
        } catch (error) {
            console.error('Failed to generate subtitles:', error);
            if (window.app) {
                window.app.showError('字幕生成时发生错误');
            }
        }
    }

    // ==================== 缺失的方法实现 ====================

    showAnalysisResults(data) {
        const modal = document.createElement('div');
        modal.className = 'analysis-results-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>内容分析结果</h3>
                    <button class="close-btn" onclick="this.closest('.analysis-results-modal').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="analysis-summary">
                        <h4>分析摘要</h4>
                        <p><strong>分析类型:</strong> ${data.analysis_type || '综合分析'}</p>
                        <p><strong>置信度阈值:</strong> ${data.confidence_threshold || 0.7}</p>
                        <p><strong>自动应用:</strong> ${data.auto_applied ? '是' : '否'}</p>
                    </div>
                    ${data.analysis ? `
                        <div class="analysis-details">
                            <h4>详细结果</h4>
                            <pre>${JSON.stringify(data.analysis, null, 2)}</pre>
                        </div>
                    ` : ''}
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary" onclick="this.closest('.analysis-results-modal').remove()">确定</button>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    showSubtitleResults(data) {
        const modal = document.createElement('div');
        modal.className = 'subtitle-results-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>字幕生成结果</h3>
                    <button class="close-btn" onclick="this.closest('.subtitle-results-modal').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="subtitle-summary">
                        <p><strong>生成数量:</strong> ${data.subtitles ? data.subtitles.length : 0} 条字幕</p>
                        <p><strong>语言:</strong> ${data.language || 'zh-CN'}</p>
                        <p><strong>音轨:</strong> ${data.track_index || 1}</p>
                    </div>
                    ${data.subtitles && data.subtitles.length > 0 ? `
                        <div class="subtitle-list">
                            <h4>字幕内容</h4>
                            <div class="subtitle-items">
                                ${data.subtitles.map(subtitle => `
                                    <div class="subtitle-item">
                                        <div class="subtitle-time">${subtitle.start_time}s - ${subtitle.end_time}s</div>
                                        <div class="subtitle-text">${subtitle.text}</div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="this.downloadSubtitles(${JSON.stringify(data).replace(/"/g, '&quot;')})">下载字幕</button>
                    <button class="btn btn-primary" onclick="this.closest('.subtitle-results-modal').remove()">确定</button>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    downloadSubtitles(data) {
        if (!data.subtitles || data.subtitles.length === 0) return;

        const srtContent = data.subtitles.map((subtitle, index) => {
            const startTime = this.formatSRTTime(subtitle.start_time);
            const endTime = this.formatSRTTime(subtitle.end_time);
            return `${index + 1}\n${startTime} --> ${endTime}\n${subtitle.text}\n`;
        }).join('\n');

        const blob = new Blob([srtContent], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'subtitles.srt';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    formatSRTTime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        const ms = Math.floor((seconds % 1) * 1000);
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${ms.toString().padStart(3, '0')}`;
    }

    async addSmartMarkers() {
        try {
            const response = await fetch('/api/davinci/deep-integration/analyze-content', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    analysis_type: 'smart_marking',
                    confidence_threshold: 0.8,
                    auto_apply: true
                })
            });

            const data = await response.json();

            if (data.success) {
                if (window.app) {
                    window.app.showSuccess(`智能标记添加成功，应用了${data.data.markers_applied || 0}个标记`);
                }
                this.showAnalysisResults(data.data);
            } else {
                if (window.app) {
                    window.app.showError('智能标记添加失败: ' + data.message);
                }
            }
        } catch (error) {
            console.error('Failed to add smart markers:', error);
            if (window.app) {
                window.app.showError('智能标记添加时发生错误');
            }
        }
    }

    async extractAudio() {
        try {
            // 显示开始提取的反馈
            if (window.app) {
                window.app.showNotification('开始提取音频...', 'info');
            }

            const response = await fetch('/api/davinci/deep-integration/extract-audio', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    track_index: 1
                })
            });

            const data = await response.json();

            if (data.success) {
                // 显示详细的成功反馈
                if (window.app) {
                    window.app.showDetailedNotification({
                        title: '音频提取成功！',
                        message: `已从轨道 ${data.data.track_index} 提取音频`,
                        details: [
                            `文件: ${data.data.filename}`,
                            `大小: ${this.formatFileSize(data.data.file_size)}`,
                            `格式: ${data.data.format.toUpperCase()}`,
                            `项目: ${data.data.project_name}`
                        ],
                        type: 'success',
                        duration: 6000
                    });
                }

                // 触发音频提取完成事件
                this.dispatchAudioExtractedEvent(data.data);

            } else {
                if (window.app) {
                    window.app.showError('音频提取失败: ' + data.message);
                }
            }
        } catch (error) {
            console.error('Failed to extract audio:', error);
            if (window.app) {
                window.app.showError('音频提取时发生错误');
            }
        }
    }

    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 触发音频提取完成事件
     */
    dispatchAudioExtractedEvent(audioData) {
        const event = new CustomEvent('audioExtracted', {
            detail: audioData
        });
        document.dispatchEvent(event);
    }
}

// ==================== 全局函数实现 ====================
// 这些函数被HTML中的onclick属性调用

window.connectToDaVinci = function () {
    const davinciModule = window.daVinciIntegration;
    if (davinciModule) {
        davinciModule.connectToDaVinci();
    } else {
        console.error('DaVinci integration module not found');
    }
};

window.refreshStatus = function () {
    const davinciModule = window.daVinciIntegration;
    if (davinciModule) {
        davinciModule.checkConnectionStatus();
    } else {
        console.error('DaVinci integration module not found');
    }
};

// 项目创建功能已移除

// 项目加载功能已移除

// 项目刷新功能已移除

window.createTimeline = async function () {
    const timelineName = document.getElementById('timeline-name')?.value;
    const timelineFps = document.getElementById('timeline-fps')?.value;

    if (!timelineName) {
        if (window.app) {
            window.app.showWarning('请输入时间线名称');
        }
        return;
    }

    try {
        const response = await fetch('/api/davinci/timeline/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                name: timelineName,
                settings: {
                    fps: parseInt(timelineFps) || 24
                }
            })
        });

        const data = await response.json();

        if (data.success) {
            if (window.app) {
                window.app.showSuccess(`时间线 "${timelineName}" 创建成功`);
            }
            // 清空输入框
            if (document.getElementById('timeline-name')) {
                document.getElementById('timeline-name').value = '';
            }
        } else {
            if (window.app) {
                window.app.showError('时间线创建失败: ' + data.message);
            }
        }
    } catch (error) {
        console.error('Failed to create timeline:', error);
        if (window.app) {
            window.app.showError('时间线创建时发生错误');
        }
    }
};

window.importMedia = async function () {
    const fileInput = document.getElementById('media-file-input');
    const targetFolder = document.getElementById('target-folder')?.value || '';

    if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
        if (window.app) {
            window.app.showWarning('请选择要导入的媒体文件');
        }
        return;
    }

    const filePaths = Array.from(fileInput.files).map(file => file.name);

    try {
        const response = await fetch('/api/davinci/media/import', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                file_paths: filePaths,
                target_folder: targetFolder
            })
        });

        const data = await response.json();

        if (data.success) {
            if (window.app) {
                window.app.showSuccess(`成功导入 ${data.data.imported_count} 个文件`);
            }
            // 清空文件选择
            fileInput.value = '';
        } else {
            if (window.app) {
                window.app.showError('媒体导入失败: ' + data.message);
            }
        }
    } catch (error) {
        console.error('Failed to import media:', error);
        if (window.app) {
            window.app.showError('媒体导入时发生错误');
        }
    }
};

window.startRender = async function () {
    const exportPath = document.getElementById('export-path')?.value;
    const exportFilename = document.getElementById('export-filename')?.value;
    const exportFormat = document.getElementById('export-format')?.value || 'mp4';
    const exportQuality = document.getElementById('export-quality')?.value || 'high';

    if (!exportPath || !exportFilename) {
        if (window.app) {
            window.app.showWarning('请填写导出路径和文件名');
        }
        return;
    }

    try {
        const response = await fetch('/api/davinci/render/start', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                export_path: exportPath,
                filename: exportFilename,
                format: exportFormat,
                render_settings: {
                    quality: exportQuality
                }
            })
        });

        const data = await response.json();

        if (data.success) {
            if (window.app) {
                window.app.showSuccess('渲染任务已开始');
            }
            // 开始监控渲染状态
            const davinciModule = window.daVinciIntegration;
            if (davinciModule) {
                davinciModule.checkRenderStatus();
            }
        } else {
            if (window.app) {
                window.app.showError('渲染启动失败: ' + data.message);
            }
        }
    } catch (error) {
        console.error('Failed to start render:', error);
        if (window.app) {
            window.app.showError('渲染启动时发生错误');
        }
    }
};

window.checkRenderStatus = function () {
    const davinciModule = window.daVinciIntegration;
    if (davinciModule) {
        davinciModule.checkRenderStatus();
    } else {
        console.error('DaVinci integration module not found');
    }
};

// 注册组件
componentManager.registerComponent('DaVinciIntegration', DaVinciIntegrationModule);

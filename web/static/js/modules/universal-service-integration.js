/**
 * 通用服务集成模块
 * 基于统一配置自动生成UI组件，消除重复代码
 */

class UniversalServiceIntegration {
    constructor() {
        this.servicesConfig = {};
        this.registeredServices = new Map();
        this.parameterPanels = new Map();
        this.isInitialized = false;
        this.dynamicConfigLoader = null;
    }

    /**
     * 初始化通用服务集成
     */
    async initialize() {
        try {
            console.log('🚀 初始化通用服务集成系统...');

            // 等待动态配置加载器初始化完成
            await this.waitForDynamicConfigLoader();

            // 加载统一服务配置
            await this.loadServicesConfig();

            // 注册所有服务
            this.registerAllServices();

            // 设置全局事件监听器
            this.setupGlobalEventListeners();

            this.isInitialized = true;
            console.log('✅ 通用服务集成系统初始化完成');

        } catch (error) {
            console.error('❌ 通用服务集成初始化失败:', error);
            this.isInitialized = false;
        }
    }

    /**
     * 等待动态配置加载器初始化完成
     */
    async waitForDynamicConfigLoader() {
        return new Promise((resolve, reject) => {
            const checkLoader = () => {
                if (window.dynamicConfigLoader && window.dynamicConfigLoader.configLoaded) {
                    this.dynamicConfigLoader = window.dynamicConfigLoader;
                    console.log('✅ 动态配置加载器已就绪');
                    resolve();
                } else if (window.dynamicConfigLoader) {
                    // 监听配置加载完成事件
                    window.addEventListener('dynamicConfigLoaded', () => {
                        this.dynamicConfigLoader = window.dynamicConfigLoader;
                        console.log('✅ 动态配置加载器已就绪');
                        resolve();
                    }, { once: true });
                } else {
                    // 继续等待
                    setTimeout(checkLoader, 100);
                }
            };

            // 设置超时
            setTimeout(() => {
                if (!this.dynamicConfigLoader) {
                    console.warn('⚠️ 动态配置加载器超时，使用回退模式');
                    resolve();
                }
            }, 5000);

            checkLoader();
        });
    }

    /**
     * 加载统一服务配置
     */
    async loadServicesConfig() {
        try {
            const response = await fetch('/config/unified_services.json');
            if (!response.ok) {
                throw new Error(`配置加载失败: ${response.statusText}`);
            }

            this.servicesConfig = await response.json();
            console.log('✅ 统一服务配置加载成功');

        } catch (error) {
            console.error('❌ 加载统一服务配置失败:', error);
            // 使用默认配置
            this.servicesConfig = { services: {} };
        }
    }

    /**
     * 注册所有服务
     */
    registerAllServices() {
        const services = this.servicesConfig.services || {};

        for (const [serviceName, serviceConfig] of Object.entries(services)) {
            this.registerService(serviceName, serviceConfig);
        }

        console.log(`✅ 已注册 ${this.registeredServices.size} 个服务`);
    }

    /**
     * 注册单个服务
     */
    registerService(serviceName, serviceConfig) {
        try {
            const metadata = serviceConfig.metadata || {};
            const capabilities = serviceConfig.capabilities || {};
            const uiConfig = serviceConfig.ui_config || {};

            // 注册服务信息
            this.registeredServices.set(serviceName, {
                metadata,
                capabilities,
                uiConfig,
                tools: serviceConfig.tools || {}
            });

            // 为每个能力添加到相应的提供商选择器
            for (const [capabilityName, capabilityConfig] of Object.entries(capabilities)) {
                if (capabilityConfig.enabled) {
                    this.addToProviderSelector(serviceName, capabilityName, metadata);
                    this.createParameterPanel(serviceName, capabilityName, uiConfig);
                }
            }

            console.log(`✅ 注册服务: ${metadata.display_name || serviceName}`);

        } catch (error) {
            console.error(`❌ 注册服务 ${serviceName} 失败:`, error);
        }
    }

    /**
     * 添加到提供商选择器
     */
    addToProviderSelector(serviceName, capabilityName, metadata) {
        // 使用动态配置加载器获取选择器映射
        let selectors = [];

        selectors.forEach(selectorId => {
            const selector = document.getElementById(selectorId);
            if (selector && !selector.querySelector(`option[value="${serviceName}"]`)) {
                const option = document.createElement('option');
                option.value = serviceName;
                option.textContent = `${metadata.icon || '🤖'} ${metadata.display_name || serviceName}`;
                option.title = metadata.description || '';

                // 按优先级排序插入
                const priority = metadata.priority || 0;
                let inserted = false;

                for (const existingOption of selector.options) {
                    if (existingOption.value === '') continue; // 跳过默认选项

                    const existingService = this.registeredServices.get(existingOption.value);
                    const existingPriority = existingService?.metadata?.priority || 0;

                    if (priority > existingPriority) {
                        selector.insertBefore(option, existingOption);
                        inserted = true;
                        break;
                    }
                }

                if (!inserted) {
                    selector.appendChild(option);
                }
            }
        });
    }

    /**
     * 创建参数控制面板
     */
    createParameterPanel(serviceName, capabilityName, uiConfig) {
        const panelConfig = uiConfig.parameter_panels?.[capabilityName];
        if (!panelConfig) return;

        const panelId = `${serviceName}-${capabilityName}-params`;

        // 检查面板是否已存在
        if (document.getElementById(panelId)) return;

        const panel = document.createElement('div');
        panel.id = panelId;
        panel.className = 'provider-params';
        panel.style.display = 'none';

        // 创建面板标题
        const title = document.createElement('h4');
        title.textContent = panelConfig.title || `${serviceName} 参数`;
        title.className = 'params-title';
        panel.appendChild(title);

        // 创建参数组
        const groups = panelConfig.groups || [];
        groups.forEach(group => {
            const groupElement = this.createParameterGroup(serviceName, group);
            panel.appendChild(groupElement);
        });

        // 将面板添加到相应的容器
        const containerMappings = {
            'speech_synthesis': '#speech-synthesis-section',
            'image_generation': '#image-generation-section',
            'video_generation': '#video-generation-section',
            'text_generation': '#text-generation-section',
            'chat': '#text-generation-section',
            'text_analysis': '#text-analysis-section',
            'translation': '#translation-section',
            'voice_cloning': '#voice-cloning-section'
        };

        const containerSelector = containerMappings[capabilityName];
        if (containerSelector) {
            const container = document.querySelector(containerSelector);
            if (container) {
                container.appendChild(panel);
                this.parameterPanels.set(panelId, panel);
            }
        }
    }

    /**
     * 创建参数组
     */
    createParameterGroup(serviceName, groupConfig) {
        const group = document.createElement('div');
        group.className = 'param-group-container';

        // 组标题
        if (groupConfig.title) {
            const groupTitle = document.createElement('h5');
            groupTitle.textContent = groupConfig.title;
            groupTitle.className = 'param-group-title';
            group.appendChild(groupTitle);
        }

        // 参数控件
        const parameters = groupConfig.parameters || [];
        parameters.forEach(paramConfig => {
            const paramElement = this.createParameterControl(serviceName, paramConfig);
            group.appendChild(paramElement);
        });

        return group;
    }

    /**
     * 创建参数控件
     */
    createParameterControl(serviceName, paramConfig) {
        const paramGroup = document.createElement('div');
        paramGroup.className = 'param-group';

        const label = document.createElement('label');
        label.textContent = paramConfig.label || paramConfig.key;
        label.setAttribute('for', `${serviceName}-${paramConfig.key}`);
        paramGroup.appendChild(label);

        let control;
        const controlId = `${serviceName}-${paramConfig.key}`;

        switch (paramConfig.type) {
            case 'range':
                control = this.createRangeControl(controlId, paramConfig);
                break;
            case 'select':
                control = this.createSelectControl(controlId, paramConfig);
                break;
            case 'checkbox':
                control = this.createCheckboxControl(controlId, paramConfig);
                break;
            case 'text':
            default:
                control = this.createTextControl(controlId, paramConfig);
                break;
        }

        paramGroup.appendChild(control);

        // 添加值显示（对于range类型）
        if (paramConfig.type === 'range') {
            const valueSpan = document.createElement('span');
            valueSpan.id = `${controlId}-value`;
            valueSpan.textContent = paramConfig.default || paramConfig.min || 0;
            paramGroup.appendChild(valueSpan);
        }

        return paramGroup;
    }

    /**
     * 创建范围控件
     */
    createRangeControl(controlId, paramConfig) {
        const input = document.createElement('input');
        input.type = 'range';
        input.id = controlId;
        input.min = paramConfig.min || 0;
        input.max = paramConfig.max || 100;
        input.step = paramConfig.step || 1;
        input.value = paramConfig.default || paramConfig.min || 0;

        // 添加值更新监听器
        input.addEventListener('input', (e) => {
            const valueSpan = document.getElementById(`${controlId}-value`);
            if (valueSpan) {
                valueSpan.textContent = e.target.value;
            }
        });

        return input;
    }

    /**
     * 创建选择控件
     */
    createSelectControl(controlId, paramConfig) {
        const select = document.createElement('select');
        select.id = controlId;

        // 添加选项
        const options = paramConfig.options || [];
        options.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option.value;
            optionElement.textContent = option.label || option.value;
            if (option.value === paramConfig.default) {
                optionElement.selected = true;
            }
            select.appendChild(optionElement);
        });

        // 如果有API选项，异步加载
        if (paramConfig.options_api) {
            this.loadSelectOptions(select, paramConfig.options_api);
        }

        return select;
    }

    /**
     * 创建复选框控件
     */
    createCheckboxControl(controlId, paramConfig) {
        const input = document.createElement('input');
        input.type = 'checkbox';
        input.id = controlId;
        input.checked = paramConfig.default || false;
        return input;
    }

    /**
     * 创建文本控件
     */
    createTextControl(controlId, paramConfig) {
        const input = document.createElement('input');
        input.type = 'text';
        input.id = controlId;
        input.value = paramConfig.default || '';
        if (paramConfig.placeholder) {
            input.placeholder = paramConfig.placeholder;
        }
        return input;
    }

    /**
     * 异步加载选择控件选项
     */
    async loadSelectOptions(selectElement, apiUrl) {
        try {
            const response = await fetch(apiUrl);
            if (!response.ok) return;

            const data = await response.json();

            // 处理不同的API响应格式
            let options = [];
            if (data && data.success && data.data) {
                // 处理标准APIResponse格式
                if (data.data.voices) {
                    // 语音API格式: {success: true, data: {voices: [...]}}
                    options = data.data.voices;
                } else if (Array.isArray(data.data)) {
                    // 直接数组格式: {success: true, data: [...]}
                    options = data.data;
                } else {
                    // 其他格式，尝试直接使用data.data
                    options = [data.data];
                }
            } else if (data && Array.isArray(data.data)) {
                // 旧格式兼容: {data: [...]}
                options = data.data;
            }

            if (options && options.length > 0) {
                // 清除现有选项
                selectElement.innerHTML = '';

                // 添加新选项
                options.forEach(option => {
                    const optionElement = document.createElement('option');
                    optionElement.value = option.id || option.voice_id || option.value;
                    optionElement.textContent = option.name || option.label || option.voice_name || option.id;
                    selectElement.appendChild(optionElement);
                });
            }
        } catch (error) {
            console.error('加载选择选项失败:', error);
        }
    }

    /**
     * 设置全局事件监听器
     */
    setupGlobalEventListeners() {
        // 监听提供商选择变化
        document.addEventListener('change', (e) => {
            if (e.target.matches('[id$="-provider-select"]')) {
                this.handleProviderChange(e.target);
            }
        });

        // 监听参数变化
        document.addEventListener('input', (e) => {
            if (e.target.matches('[id*="-params"] input, [id*="-params"] select')) {
                this.handleParameterChange(e.target);
            }
        });
    }

    /**
     * 处理提供商选择变化
     */
    handleProviderChange(selector) {
        const selectedProvider = selector.value;
        const capabilityName = this.getCapabilityFromSelector(selector.id);

        // 隐藏所有相关参数面板
        this.parameterPanels.forEach((panel, panelId) => {
            if (panelId.includes(capabilityName)) {
                panel.style.display = 'none';
            }
        });

        // 显示选中提供商的参数面板
        if (selectedProvider) {
            const targetPanelId = `${selectedProvider}-${capabilityName}-params`;
            const targetPanel = document.getElementById(targetPanelId);
            if (targetPanel) {
                targetPanel.style.display = 'block';
            }
        }
    }

    /**
     * 从选择器ID获取能力名称
     */
    getCapabilityFromSelector(selectorId) {
        const mappings = {
            'speech-provider': 'speech_synthesis',
            'image-provider': 'image_generation',
            'video-provider': 'video_generation',
            'generation-provider': 'text_generation',
            'analysis-provider': 'text_analysis',
            'enhancement-provider': 'text_analysis',
            'translation-provider': 'translation',
            'clone-provider': 'voice_cloning'
        };

        return mappings[selectorId] || 'unknown';
    }

    /**
     * 处理参数变化
     */
    handleParameterChange(input) {
        // 更新范围控件的值显示
        if (input.type === 'range') {
            const valueSpan = document.getElementById(`${input.id}-value`);
            if (valueSpan) {
                valueSpan.textContent = input.value;
            }
        }

        // 触发自定义事件
        const event = new CustomEvent('parameterChanged', {
            detail: {
                inputId: input.id,
                value: input.type === 'checkbox' ? input.checked : input.value,
                serviceName: this.getServiceNameFromInputId(input.id)
            }
        });
        document.dispatchEvent(event);
    }

    /**
     * 从输入控件ID获取服务名称
     */
    getServiceNameFromInputId(inputId) {
        const parts = inputId.split('-');
        return parts[0]; // 假设格式为 serviceName-paramName
    }

    /**
     * 获取指定服务的参数
     */
    getServiceParameters(serviceName, capabilityName) {
        const panelId = `${serviceName}-${capabilityName}-params`;
        const panel = document.getElementById(panelId);

        if (!panel) return {};

        const parameters = {};
        const inputs = panel.querySelectorAll('input, select');

        inputs.forEach(input => {
            const paramName = input.id.replace(`${serviceName}-`, '');
            if (input.type === 'checkbox') {
                parameters[paramName] = input.checked;
            } else if (input.type === 'range' || input.type === 'number') {
                parameters[paramName] = parseFloat(input.value);
            } else {
                parameters[paramName] = input.value;
            }
        });

        return parameters;
    }

    /**
     * 获取已注册的服务列表
     */
    getRegisteredServices() {
        return Array.from(this.registeredServices.keys());
    }

    /**
     * 获取服务的能力列表
     */
    getServiceCapabilities(serviceName) {
        const service = this.registeredServices.get(serviceName);
        return service ? Object.keys(service.capabilities) : [];
    }

    /**
     * 重新加载配置
     */
    async reloadConfig() {
        console.log('🔄 重新加载服务配置...');

        // 清理现有注册
        this.registeredServices.clear();
        this.parameterPanels.clear();

        // 重新初始化
        await this.initialize();

        console.log('✅ 服务配置重新加载完成');
    }
}

// 创建全局实例
window.universalServiceIntegration = new UniversalServiceIntegration();

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
    window.universalServiceIntegration.initialize();
});

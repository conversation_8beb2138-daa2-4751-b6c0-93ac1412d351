/**
 * 统一AI服务管理器
 * 实现前端动态适配器系统的核心组件
 * 提供统一的API调用接口，消除硬编码依赖
 */

class UnifiedAIServiceManager {
    constructor() {
        this.dynamicConfigLoader = null;
        this.isInitialized = false;
        this.serviceCache = new Map();
        this.requestHistory = [];
        this.maxHistorySize = 100;
        
        // 统一响应格式
        this.standardResponseFormat = {
            success: false,
            data: null,
            error: null,
            metadata: {
                provider: null,
                service_type: null,
                model: null,
                processing_time: 0,
                request_id: null
            }
        };
    }

    /**
     * 初始化统一AI服务管理器
     */
    async initialize() {
        try {
            console.log('🚀 初始化统一AI服务管理器...');

            // 等待动态配置加载器就绪
            await this.waitForDynamicConfigLoader();

            // 设置事件监听器
            this.setupEventListeners();

            this.isInitialized = true;
            console.log('✅ 统一AI服务管理器初始化完成');

            // 触发初始化完成事件
            window.dispatchEvent(new CustomEvent('unifiedAIServiceManagerReady', {
                detail: { manager: this }
            }));

        } catch (error) {
            console.error('❌ 统一AI服务管理器初始化失败:', error);
            this.isInitialized = false;
        }
    }

    /**
     * 等待动态配置加载器就绪
     */
    async waitForDynamicConfigLoader() {
        return new Promise((resolve, reject) => {
            if (window.dynamicConfigLoader && window.dynamicConfigLoader.configLoaded) {
                this.dynamicConfigLoader = window.dynamicConfigLoader;
                resolve();
                return;
            }

            const timeout = setTimeout(() => {
                reject(new Error('动态配置加载器超时'));
            }, 10000);

            const checkConfig = () => {
                if (window.dynamicConfigLoader && window.dynamicConfigLoader.configLoaded) {
                    clearTimeout(timeout);
                    this.dynamicConfigLoader = window.dynamicConfigLoader;
                    resolve();
                } else {
                    setTimeout(checkConfig, 100);
                }
            };

            checkConfig();
        });
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 监听动态配置重新加载事件
        window.addEventListener('dynamicConfigReloaded', () => {
            this.clearCache();
            console.log('🔄 动态配置重新加载，清理缓存');
        });
    }

    /**
     * 统一的AI服务调用接口
     * @param {string} serviceType - 服务类型 (text_generation, translation, text_analysis等)
     * @param {string|null} provider - 服务提供商 (可选，null表示自动选择)
     * @param {Object} params - 调用参数
     * @param {Object} options - 额外选项
     * @returns {Promise<Object>} 标准化响应
     */
    async callService(serviceType, provider = null, params = {}, options = {}) {
        try {
            console.log(`🔧 调用AI服务: ${serviceType}, 提供商: ${provider || '自动选择'}`);

            // 验证初始化状态
            if (!this.isInitialized) {
                throw new Error('统一AI服务管理器未初始化');
            }

            // 生成请求ID
            const requestId = this.generateRequestId();

            // 获取服务配置
            const serviceConfig = await this.getServiceConfig(serviceType, provider);
            if (!serviceConfig) {
                throw new Error(`未找到服务配置: ${serviceType}`);
            }

            // 准备请求参数
            const requestParams = this.prepareRequestParams(serviceType, provider, params, serviceConfig);

            // 执行API调用
            const startTime = Date.now();
            const response = await this.executeAPICall(serviceConfig.endpoint, requestParams);
            const processingTime = Date.now() - startTime;

            // 标准化响应
            const standardResponse = this.standardizeResponse(response, {
                serviceType,
                provider: provider || serviceConfig.selectedProvider,
                processingTime,
                requestId
            });

            // 记录请求历史
            this.recordRequest({
                requestId,
                serviceType,
                provider: provider || serviceConfig.selectedProvider,
                params,
                response: standardResponse,
                timestamp: new Date().toISOString(),
                processingTime
            });

            console.log(`✅ AI服务调用成功: ${serviceType}, 耗时: ${processingTime}ms`);
            return standardResponse;

        } catch (error) {
            console.error(`❌ AI服务调用失败: ${serviceType}`, error);
            return this.createErrorResponse(error.message, {
                serviceType,
                provider,
                requestId: this.generateRequestId()
            });
        }
    }

    /**
     * 获取服务配置
     */
    async getServiceConfig(serviceType, provider) {
        const cacheKey = `${serviceType}_${provider || 'auto'}`;
        
        if (this.serviceCache.has(cacheKey)) {
            return this.serviceCache.get(cacheKey);
        }

        // 从动态配置获取服务信息
        const serviceTypeConfig = this.dynamicConfigLoader.getServiceType(serviceType);
        if (!serviceTypeConfig) {
            throw new Error(`未知的服务类型: ${serviceType}`);
        }

        // 选择提供商
        let selectedProvider = provider;
        if (!selectedProvider) {
            const availableProviders = this.dynamicConfigLoader.getProvidersForCapability(serviceType);
            if (availableProviders.length === 0) {
                throw new Error(`服务类型 ${serviceType} 没有可用的提供商`);
            }
            selectedProvider = availableProviders[0]; // 选择第一个可用的提供商
        }

        // 构建服务配置
        const config = {
            serviceType,
            selectedProvider,
            endpoint: '/api/ai/generate', // 统一端点
            availableProviders: this.dynamicConfigLoader.getProvidersForCapability(serviceType)
        };

        this.serviceCache.set(cacheKey, config);
        return config;
    }

    /**
     * 准备请求参数
     */
    prepareRequestParams(serviceType, provider, params, serviceConfig) {
        return {
            service_type: serviceType,
            provider: serviceConfig.selectedProvider,
            prompt: params.prompt || params.content || params.text || '',
            parameters: {
                ...params,
                // 移除已经提取的字段
                prompt: undefined,
                content: undefined,
                text: undefined
            }
        };
    }

    /**
     * 执行API调用
     */
    async executeAPICall(endpoint, params) {
        const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(params)
        });

        if (!response.ok) {
            throw new Error(`API调用失败: ${response.status} ${response.statusText}`);
        }

        return await response.json();
    }

    /**
     * 标准化响应格式
     */
    standardizeResponse(apiResponse, metadata) {
        const standardResponse = { ...this.standardResponseFormat };
        
        standardResponse.success = apiResponse.success || false;
        standardResponse.data = apiResponse.data || null;
        standardResponse.error = apiResponse.error || null;
        
        // 合并元数据
        standardResponse.metadata = {
            ...standardResponse.metadata,
            ...metadata,
            ...apiResponse.metadata
        };

        // 确保数据格式正确
        if (standardResponse.success && standardResponse.data) {
            // 如果data是字符串，包装为对象
            if (typeof standardResponse.data === 'string') {
                standardResponse.data = {
                    text: standardResponse.data,
                    content: standardResponse.data
                };
            }
            
            // 确保元数据字段存在于data中
            if (typeof standardResponse.data === 'object') {
                standardResponse.data.provider = standardResponse.metadata.provider;
                standardResponse.data.service_type = standardResponse.metadata.service_type;
                standardResponse.data.model = standardResponse.metadata.model;
                standardResponse.data.processing_time = standardResponse.metadata.processing_time;
            }
        }

        return standardResponse;
    }

    /**
     * 创建错误响应
     */
    createErrorResponse(errorMessage, metadata) {
        return {
            ...this.standardResponseFormat,
            success: false,
            error: errorMessage,
            metadata: {
                ...this.standardResponseFormat.metadata,
                ...metadata
            }
        };
    }

    /**
     * 生成请求ID
     */
    generateRequestId() {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 记录请求历史
     */
    recordRequest(requestInfo) {
        this.requestHistory.unshift(requestInfo);
        
        // 限制历史记录大小
        if (this.requestHistory.length > this.maxHistorySize) {
            this.requestHistory = this.requestHistory.slice(0, this.maxHistorySize);
        }
    }

    /**
     * 清理缓存
     */
    clearCache() {
        this.serviceCache.clear();
    }

    /**
     * 获取请求历史
     */
    getRequestHistory(limit = 10) {
        return this.requestHistory.slice(0, limit);
    }

    /**
     * 获取服务统计
     */
    getServiceStats() {
        const stats = {
            totalRequests: this.requestHistory.length,
            successfulRequests: this.requestHistory.filter(r => r.response.success).length,
            failedRequests: this.requestHistory.filter(r => !r.response.success).length,
            averageProcessingTime: 0,
            serviceTypeStats: {},
            providerStats: {}
        };

        if (stats.totalRequests > 0) {
            stats.averageProcessingTime = this.requestHistory.reduce((sum, r) => sum + r.processingTime, 0) / stats.totalRequests;
            stats.successRate = (stats.successfulRequests / stats.totalRequests * 100).toFixed(1);
        }

        // 统计各服务类型和提供商的使用情况
        this.requestHistory.forEach(request => {
            const serviceType = request.serviceType;
            const provider = request.provider;

            if (!stats.serviceTypeStats[serviceType]) {
                stats.serviceTypeStats[serviceType] = { count: 0, success: 0 };
            }
            stats.serviceTypeStats[serviceType].count++;
            if (request.response.success) {
                stats.serviceTypeStats[serviceType].success++;
            }

            if (!stats.providerStats[provider]) {
                stats.providerStats[provider] = { count: 0, success: 0 };
            }
            stats.providerStats[provider].count++;
            if (request.response.success) {
                stats.providerStats[provider].success++;
            }
        });

        return stats;
    }
}

// 创建全局实例
window.unifiedAIServiceManager = new UnifiedAIServiceManager();

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
    window.unifiedAIServiceManager.initialize();
});

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UnifiedAIServiceManager;
}

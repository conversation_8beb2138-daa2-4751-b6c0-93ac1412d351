/**
 * DaVinci AI Co-pilot PRO 前端应用
 * 处理用户交互、WebSocket通信和API调用
 */

class DaVinciApp {
    constructor() {
        this.websocket = null;
        this.isConnected = false;
        this.currentTab = 'dashboard';
        this.serviceStats = {};
        this.capabilities = {};

        // 状态管理
        this.state = {
            referenceImages: [], // 保存参考图片数据
            videoFirstFrame: null // 保存视频首帧数据
        };

        // 事件监听器标志
        this.eventListenersSetup = {
            frameCaptureListeners: false
        };

        // 组件实例
        this.components = {
            tabs: null,
            modals: new Map(),
            tooltips: new Map(),
            progressBars: new Map(),
            notifications: []
        };

        this.init();
    }

    /**
     * 初始化应用
     */
    init() {
        this.initializeComponents();
        this.setupEventListeners();
        this.connectWebSocket();
        this.loadServiceData();
        this.startAutoRefresh();
    }

    /**
     * 初始化组件
     */
    initializeComponents() {
        // 初始化标签页组件
        const tabContainer = document.querySelector('.nav');
        if (tabContainer) {
            this.components.tabs = new TabsComponent(tabContainer, {
                activeTab: 0
            });
        }

        // 初始化工具提示
        document.querySelectorAll('[data-tooltip]').forEach(element => {
            const tooltip = new TooltipComponent(element, {
                placement: element.getAttribute('data-tooltip-placement') || 'top',
                trigger: element.getAttribute('data-tooltip-trigger') || 'hover'
            });
            this.components.tooltips.set(element, tooltip);
        });

        // 监听组件事件
        componentManager.on('tabChange', (event) => {
            const { activeTab } = event.detail;
            this.switchTab(this.getTabNameByIndex(activeTab));
        });

        // 监听状态变化
        componentManager.on('stateChange', (event) => {
            const { property, value } = event.detail;
            // 状态变化处理
        });
    }

    /**
     * 根据索引获取标签名
     */
    getTabNameByIndex(index) {
        const tabs = ['dashboard', 'text-generation', 'text-analysis', 'prompt-enhancement', 'speech-synthesis', 'video-generation', 'image-generation', 'media-library', 'davinci-integration', 'settings'];
        return tabs[index] || 'dashboard';
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 导航按钮
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tab = e.target.dataset.tab;
                this.switchTab(tab);
            });
        });

        // 文本生成
        const generateTextBtn = document.getElementById('generate-text-btn');
        if (generateTextBtn) {
            generateTextBtn.addEventListener('click', () => this.generateText());
        }

        const clearGenerationBtn = document.getElementById('clear-generation-btn');
        if (clearGenerationBtn) {
            clearGenerationBtn.addEventListener('click', () => this.clearGeneration());
        }

        // 文案分析
        const analyzeBtn = document.getElementById('analyze-btn');
        if (analyzeBtn) {
            analyzeBtn.addEventListener('click', () => this.analyzeText());
        }

        const clearAnalysisBtn = document.getElementById('clear-analysis-btn');
        if (clearAnalysisBtn) {
            clearAnalysisBtn.addEventListener('click', () => this.clearAnalysis());
        }

        // 提示词增强
        const enhancePromptBtn = document.getElementById('enhance-prompt-btn');
        if (enhancePromptBtn) {
            enhancePromptBtn.addEventListener('click', () => this.enhancePrompt());
        }

        const clearEnhancementBtn = document.getElementById('clear-enhancement-btn');
        if (clearEnhancementBtn) {
            clearEnhancementBtn.addEventListener('click', () => this.clearEnhancement());
        }

        const copyEnhancedPromptBtn = document.getElementById('copy-enhanced-prompt-btn');
        if (copyEnhancedPromptBtn) {
            copyEnhancedPromptBtn.addEventListener('click', () => this.copyEnhancedPrompt());
        }

        const useEnhancedPromptBtn = document.getElementById('use-enhanced-prompt-btn');
        if (useEnhancedPromptBtn) {
            useEnhancedPromptBtn.addEventListener('click', () => this.useEnhancedPrompt());
        }

        const regeneratePromptBtn = document.getElementById('regenerate-prompt-btn');
        if (regeneratePromptBtn) {
            regeneratePromptBtn.addEventListener('click', () => this.enhancePrompt());
        }

        // 语音合成 - 现在由 speech-synthesis.js 模块处理
        // const synthesizeBtn = document.getElementById('synthesize-btn');
        // if (synthesizeBtn) {
        //     synthesizeBtn.addEventListener('click', () => this.synthesizeSpeech());
        // }

        const clearSpeechBtn = document.getElementById('clear-speech-btn');
        if (clearSpeechBtn) {
            clearSpeechBtn.addEventListener('click', () => this.clearSpeech());
        }

        // 语音参数滑块事件监听
        this.setupSpeechParameterListeners();

        // 图像参数事件监听
        this.setupImageParameterListeners();

        // 视频参数事件监听
        this.setupVideoParameterListeners();

        // 参数验证和帮助系统
        this.setupParameterValidation();

        // 异步任务管理
        this.asyncTasks = new Map();
        this.setupAsyncTaskPolling();

        // 视频生成
        const generateVideoBtn = document.getElementById('generate-video-btn');
        if (generateVideoBtn) {
            generateVideoBtn.addEventListener('click', () => this.generateVideo());
        }

        const clearVideoBtn = document.getElementById('clear-video-btn');
        if (clearVideoBtn) {
            clearVideoBtn.addEventListener('click', () => this.clearVideo());
        }

        // 图像生成
        const generateImageBtn = document.getElementById('generate-image-btn');
        if (generateImageBtn) {
            generateImageBtn.addEventListener('click', () => this.generateImage());
        }

        const clearImageBtn = document.getElementById('clear-image-btn');
        if (clearImageBtn) {
            clearImageBtn.addEventListener('click', () => this.clearImage());
        }

        // 翻译
        const translateBtn = document.getElementById('translate-btn');
        if (translateBtn) {
            translateBtn.addEventListener('click', () => this.translateText());
        }

        const clearTranslationBtn = document.getElementById('clear-translation-btn');
        if (clearTranslationBtn) {
            clearTranslationBtn.addEventListener('click', () => this.clearTranslation());
        }

        const swapLanguagesBtn = document.getElementById('swap-languages-btn');
        if (swapLanguagesBtn) {
            swapLanguagesBtn.addEventListener('click', () => this.swapLanguages());
        }

        // DaVinci集成
        const connectDaVinciBtn = document.getElementById('connect-davinci-btn');
        if (connectDaVinciBtn) {
            connectDaVinciBtn.addEventListener('click', () => this.connectDaVinci());
        }

        const refreshDaVinciBtn = document.getElementById('refresh-davinci-btn');
        if (refreshDaVinciBtn) {
            refreshDaVinciBtn.addEventListener('click', () => this.loadDaVinciStatus());
        }

        // 项目管理相关按钮已移除

        // 静帧捕获功能
        const captureFrameBtn = document.getElementById('capture-frame-btn');
        if (captureFrameBtn) {
            captureFrameBtn.addEventListener('click', () => this.captureCurrentFrame());
        }

        const exportForAIBtn = document.getElementById('export-for-ai-btn');
        if (exportForAIBtn) {
            exportForAIBtn.addEventListener('click', () => this.exportFrameForAI());
        }

        const refreshPlayheadBtn = document.getElementById('refresh-playhead-btn');
        if (refreshPlayheadBtn) {
            refreshPlayheadBtn.addEventListener('click', () => this.refreshPlayheadInfo());
        }

        // AI深度集成功能
        const analyzeContentBtn = document.getElementById('analyze-content-btn');
        if (analyzeContentBtn) {
            analyzeContentBtn.addEventListener('click', () => this.analyzeContent());
        }

        const generateSubtitlesBtn = document.getElementById('generate-subtitles-btn');
        if (generateSubtitlesBtn) {
            generateSubtitlesBtn.addEventListener('click', () => this.generateSubtitles());
        }

        const extractAudioBtn = document.getElementById('extract-audio-btn');
        if (extractAudioBtn) {
            extractAudioBtn.addEventListener('click', () => this.extractAudio());
        }

        const addSmartMarkersBtn = document.getElementById('add-smart-markers-btn');
        if (addSmartMarkersBtn) {
            addSmartMarkersBtn.addEventListener('click', () => this.addSmartMarkers());
        }

        // AI集成标签页切换
        const aiTabBtns = document.querySelectorAll('.ai-tab-btn');
        aiTabBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tabName = e.target.dataset.aiTab;
                this.switchAITab(tabName);
            });
        });

        // 滑块值更新
        const confidenceSlider = document.getElementById('confidence-threshold');
        const confidenceValue = document.getElementById('confidence-value');
        if (confidenceSlider && confidenceValue) {
            confidenceSlider.addEventListener('input', (e) => {
                confidenceValue.textContent = parseFloat(e.target.value).toFixed(1);
            });
        }

        const markerConfidenceSlider = document.getElementById('marker-confidence');
        const markerConfidenceValue = document.getElementById('marker-confidence-value');
        if (markerConfidenceSlider && markerConfidenceValue) {
            markerConfidenceSlider.addEventListener('input', (e) => {
                markerConfidenceValue.textContent = parseFloat(e.target.value).toFixed(1);
            });
        }

        // 设置
        const saveSettingsBtn = document.getElementById('save-settings-btn');
        if (saveSettingsBtn) {
            saveSettingsBtn.addEventListener('click', () => this.saveSettings());
        }

        const resetSettingsBtn = document.getElementById('reset-settings-btn');
        if (resetSettingsBtn) {
            resetSettingsBtn.addEventListener('click', () => this.resetSettings());
        }
    }

    /**
     * 连接WebSocket
     */
    connectWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws`;

        try {
            this.websocket = new WebSocket(wsUrl);

            this.websocket.onopen = () => {
                this.isConnected = true;
                this.updateConnectionStatus('connected', 'WebSocket: 已连接');
            };

            this.websocket.onmessage = (event) => {
                this.handleWebSocketMessage(event.data);
            };

            this.websocket.onclose = () => {
                this.isConnected = false;
                this.updateConnectionStatus('error', 'WebSocket: 连接断开');

                // 5秒后重连
                setTimeout(() => this.connectWebSocket(), 5000);
            };

            this.websocket.onerror = (error) => {
                console.error('WebSocket error:', error);
                this.updateConnectionStatus('error', 'WebSocket: 连接错误');
            };

        } catch (error) {
            console.error('Failed to connect WebSocket:', error);
            this.updateConnectionStatus('error', 'WebSocket: 连接失败');
        }
    }

    /**
     * 处理WebSocket消息
     */
    handleWebSocketMessage(data) {
        try {
            const message = JSON.parse(data);

            switch (message.type) {
                case 'status_update':
                    this.handleStatusUpdate(message.data);
                    break;
                case 'progress_update':
                    this.handleProgressUpdate(message.data);
                    break;
                case 'result':
                    this.handleResult(message.data);
                    break;
                default:
                // 未知消息类型
            }
        } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
        }
    }

    /**
     * 更新连接状态
     */
    updateConnectionStatus(status, text) {
        const indicator = document.getElementById('status-indicator');
        const statusText = document.getElementById('status-text');
        const connectionStatus = document.getElementById('connection-status');

        if (indicator) {
            indicator.className = `status-indicator ${status}`;
        }

        if (statusText) {
            statusText.textContent = text;
        }

        if (connectionStatus) {
            connectionStatus.textContent = text;
        }
    }

    /**
     * 切换标签页
     */
    switchTab(tabName) {
        // 更新导航按钮状态
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // 切换内容
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');

        this.currentTab = tabName;

        // 根据标签页加载相应数据
        if (tabName === 'dashboard') {
            this.loadServiceData();
        } else if (tabName === 'davinci-integration') {
            this.loadDaVinciStatus();
            // 初始化DaVinci高级功能
            setTimeout(() => {
                this.initDaVinciAdvancedFeatures();
            }, 500);
        } else if (tabName === 'image-generation') {
            // 恢复图像参考状态
            setTimeout(() => {
                this.updateImageReferencePreview();
            }, 100);
        } else if (tabName === 'video-generation') {
            // 恢复视频首帧状态
            setTimeout(() => {
                this.updateVideoFirstFramePreview();
            }, 100);
        } else if (tabName === 'media-library') {
            // 初始化媒体库界面
            setTimeout(() => {
                if (window.mediaLibraryUI) {
                    window.mediaLibraryUI.render('media-library');
                }
            }, 100);
        } else if (tabName === 'speech-synthesis') {
            // 初始化语音合成模块
            setTimeout(() => {
                if (window.speechSynthesisModule) {
                    console.log('🎤 Initializing Speech Synthesis Module on tab switch...');
                    window.speechSynthesisModule.initialize();
                }
            }, 300); // 增加延迟确保DOM完全可见
        }
    }

    /**
     * 加载服务数据
     */
    async loadServiceData() {
        try {
            // 加载服务统计
            const statsResponse = await fetch('/api/services/stats');
            if (statsResponse.ok) {
                const statsData = await statsResponse.json();
                this.serviceStats = statsData.success ? statsData.data : {};
                this.updateServiceStatus();
            }

            // 加载服务能力
            const capabilitiesResponse = await fetch('/api/services/capabilities');
            if (capabilitiesResponse.ok) {
                const capabilitiesData = await capabilitiesResponse.json();
                this.capabilities = capabilitiesData.success ? capabilitiesData.data : {};
                this.updateServiceCapabilities();
            }

        } catch (error) {
            console.error('Failed to load service data:', error);
        }
    }

    /**
     * 更新服务状态显示
     */
    updateServiceStatus() {
        const container = document.getElementById('service-status');
        if (!container) return;

        let html = '';

        for (const [provider, stats] of Object.entries(this.serviceStats)) {
            const statusClass = stats.is_available ? 'success' : 'error';
            const statusText = stats.is_available ? '可用' : '不可用';
            const successRate = (stats.success_rate * 100).toFixed(1);

            // 检查是否为MCP服务
            const isMCPService = this.isMCPService(provider);
            const mcpIndicator = isMCPService ? '<span class="mcp-indicator">MCP</span>' : '';

            // 添加服务状态指示器
            const statusIndicator = `<span class="service-status ${stats.is_available ? 'available' : 'unavailable'}"></span>`;

            html += `
                <div class="service-item">
                    <div class="service-header">
                        <span class="service-name">
                            ${statusIndicator}${provider}${mcpIndicator}
                        </span>
                        <span class="service-status ${statusClass}">${statusText}</span>
                    </div>
                    <div class="service-details">
                        <span>请求: ${stats.total_requests}</span>
                        <span>成功率: ${successRate}%</span>
                        ${isMCPService ? '<span class="mcp-label">🔗 MCP协议</span>' : ''}
                    </div>
                </div>
            `;
        }

        container.innerHTML = html || '<div class="loading">暂无服务数据</div>';
    }

    /**
     * 检查是否为MCP服务
     */
    isMCPService(provider) {
        // 基于提供商名称或元数据判断是否为MCP服务
        const mcpServices = ['minimax', 'enhance-prompt', 'openai', 'anthropic'];
        return mcpServices.includes(provider.toLowerCase()) ||
            (this.serviceStats[provider] && this.serviceStats[provider].provider &&
                this.serviceStats[provider].provider.includes('mcp'));
    }

    /**
     * 更新服务能力显示
     */
    updateServiceCapabilities() {
        const container = document.getElementById('service-capabilities');
        if (!container) return;

        let html = '';

        for (const [capability, providers] of Object.entries(this.capabilities)) {
            const providerList = providers.length > 0 ? providers.join(', ') : '暂无';
            const capabilityName = this.getCapabilityName(capability);

            html += `
                <div class="capability-item">
                    <span class="capability-name">${capabilityName}:</span>
                    <span class="capability-providers">${providerList}</span>
                </div>
            `;
        }

        container.innerHTML = html || '<div class="loading">暂无能力数据</div>';

        // 更新服务选择器
        this.updateServiceSelectors();
    }

    /**
     * 更新所有服务选择器
     */
    updateServiceSelectors() {
        const selectors = [
            'generation-provider',
            'analysis-provider',
            'enhancement-provider',
            'speech-provider',
            'video-provider',
            'image-provider',
            'translation-provider'
        ];

        selectors.forEach(selectorId => {
            const select = document.getElementById(selectorId);
            if (select) {
                this.updateServiceSelector(select, selectorId);
            }
        });
    }

    /**
     * 更新单个服务选择器
     */
    updateServiceSelector(select, selectorId) {
        // 保存当前选择的值
        const currentValue = select.value;

        // 获取当前选择器对应的服务类型
        const serviceType = this.getServiceTypeFromSelector(selectorId);
        const availableProviders = this.capabilities[serviceType] || [];

        // 清空现有选项（保留"自动选择"）
        const autoOption = select.querySelector('option[value=""]');
        select.innerHTML = '';
        if (autoOption) {
            select.appendChild(autoOption);
        } else {
            const option = document.createElement('option');
            option.value = '';
            option.textContent = '自动选择';
            select.appendChild(option);
        }

        // 添加可用的服务提供商
        availableProviders.forEach(provider => {
            const option = document.createElement('option');
            option.value = provider;

            // 检查是否为MCP服务并添加标识
            const isMCP = this.isMCPService(provider);
            option.textContent = isMCP ? `🔗 ${provider} (MCP)` : provider;

            // 如果服务不可用，禁用选项
            const stats = this.serviceStats[provider];
            if (stats && !stats.is_available) {
                option.disabled = true;
                option.textContent += ' (不可用)';
            }

            select.appendChild(option);
        });

        // 恢复之前的选择（如果仍然可用）
        if (currentValue && availableProviders.includes(currentValue)) {
            select.value = currentValue;
        }
    }

    /**
     * 根据选择器ID获取对应的服务类型
     */
    getServiceTypeFromSelector(selectorId) {
        const mapping = {
            'generation-provider': 'text_generation',
            'analysis-provider': 'text_analysis',
            'enhancement-provider': 'text_analysis', // 提示词增强使用text_analysis类型
            'speech-provider': 'speech_synthesis',
            'video-provider': 'video_generation',
            'image-provider': 'image_generation',
            'translation-provider': 'translation'
        };
        return mapping[selectorId] || 'text_generation';
    }

    /**
     * 设置异步任务轮询
     */
    setupAsyncTaskPolling() {
        // 每5秒检查一次异步任务状态
        setInterval(() => {
            this.checkAsyncTasks();
        }, 5000);
    }

    /**
     * 检查异步任务状态
     */
    async checkAsyncTasks() {
        for (const [taskId, taskInfo] of this.asyncTasks) {
            try {
                const response = await fetch(`/api/tasks/${taskId}`);
                const result = await response.json();

                if (result.success) {
                    const taskData = result.data;

                    // 更新任务状态
                    if (taskData.status === 'completed') {
                        this.handleAsyncTaskCompleted(taskId, taskData);
                    } else if (taskData.status === 'failed') {
                        this.handleAsyncTaskFailed(taskId, taskData);
                    } else if (taskData.status === 'cancelled') {
                        this.handleAsyncTaskCancelled(taskId, taskData);
                    }

                    // 更新进度显示
                    this.updateTaskProgress(taskId, taskData);
                }
            } catch (error) {
                console.error(`Error checking task ${taskId}:`, error);
            }
        }
    }

    /**
     * 处理异步任务完成
     */
    handleAsyncTaskCompleted(taskId, taskData) {
        const taskInfo = this.asyncTasks.get(taskId);
        if (!taskInfo) return;

        // 显示结果
        this.displayAsyncResult(taskInfo, taskData.result);

        // 恢复按钮状态
        this.restoreButtonState(taskInfo.button);

        // 移除任务
        this.asyncTasks.delete(taskId);

        // 显示通知
        this.showNotification('生成完成！', 'success');
    }

    /**
     * 处理异步任务失败
     */
    handleAsyncTaskFailed(taskId, taskData) {
        const taskInfo = this.asyncTasks.get(taskId);
        if (!taskInfo) return;

        // 恢复按钮状态
        this.restoreButtonState(taskInfo.button);

        // 移除任务
        this.asyncTasks.delete(taskId);

        // 显示错误
        this.showNotification(`生成失败: ${taskData.error}`, 'error');
    }

    /**
     * 处理异步任务取消
     */
    handleAsyncTaskCancelled(taskId, taskData) {
        const taskInfo = this.asyncTasks.get(taskId);
        if (!taskInfo) return;

        // 恢复按钮状态
        this.restoreButtonState(taskInfo.button);

        // 移除任务
        this.asyncTasks.delete(taskId);

        // 显示通知
        this.showNotification('任务已取消', 'warning');
    }

    /**
     * 更新任务进度
     */
    updateTaskProgress(taskId, taskData) {
        const taskInfo = this.asyncTasks.get(taskId);
        if (!taskInfo || !taskInfo.button) return;

        const button = taskInfo.button;
        const loadingSpan = button.querySelector('.btn-loading');

        if (loadingSpan && taskData.progress !== undefined) {
            const progressText = `${Math.round(taskData.progress * 100)}%`;
            loadingSpan.textContent = `处理中... ${progressText}`;
        }
    }

    /**
     * 启动异步生成
     */
    async startAsyncGeneration(serviceType, provider, prompt, parameters, button, resultContainer) {
        try {
            const response = await fetch('/api/ai/generate/async', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    service_type: serviceType,
                    provider: provider,
                    prompt: prompt,
                    parameters: parameters
                })
            });

            const result = await response.json();

            if (result.success) {
                const taskId = result.data.task_id;

                // 记录任务信息
                this.asyncTasks.set(taskId, {
                    serviceType,
                    provider,
                    prompt,
                    parameters,
                    button,
                    resultContainer,
                    startTime: Date.now()
                });

                // 更新按钮状态
                this.setAsyncButtonState(button, taskId);

                this.showNotification('任务已启动，正在后台处理...', 'info');
                return true;
            } else {
                this.showNotification(result.error || '启动异步任务失败', 'error');
                return false;
            }
        } catch (error) {
            console.error('Error starting async generation:', error);
            this.showNotification('启动异步任务时发生错误', 'error');
            return false;
        }
    }

    /**
     * 设置异步按钮状态
     */
    setAsyncButtonState(button, taskId) {
        button.disabled = true;
        button.querySelector('.btn-text').style.display = 'none';

        const loadingSpan = button.querySelector('.btn-loading');
        loadingSpan.style.display = 'inline';
        loadingSpan.textContent = '处理中...';

        // 添加取消按钮
        const cancelBtn = document.createElement('button');
        cancelBtn.className = 'cancel-btn';
        cancelBtn.textContent = '取消';
        cancelBtn.onclick = () => this.cancelAsyncTask(taskId);

        button.parentNode.insertBefore(cancelBtn, button.nextSibling);
    }

    /**
     * 恢复按钮状态
     */
    restoreButtonState(button) {
        if (!button) return;

        button.disabled = false;
        button.querySelector('.btn-text').style.display = 'inline';
        button.querySelector('.btn-loading').style.display = 'none';

        // 移除取消按钮
        const cancelBtn = button.parentNode.querySelector('.cancel-btn');
        if (cancelBtn) {
            cancelBtn.remove();
        }
    }

    /**
     * 取消异步任务
     */
    async cancelAsyncTask(taskId) {
        try {
            const response = await fetch(`/api/tasks/${taskId}`, {
                method: 'DELETE'
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification('任务已取消', 'info');
            } else {
                this.showNotification('取消任务失败', 'error');
            }
        } catch (error) {
            console.error('Error cancelling task:', error);
            this.showNotification('取消任务时发生错误', 'error');
        }
    }

    /**
     * 显示异步结果
     */
    displayAsyncResult(taskInfo, result) {
        // 这里可以根据不同的服务类型显示不同的结果
        // 复用现有的结果显示逻辑
        if (taskInfo.resultContainer) {
            // 显示结果容器
            taskInfo.resultContainer.style.display = 'block';

            // 根据服务类型处理结果
            this.handleServiceResult(taskInfo.serviceType, result, taskInfo.resultContainer);
        }
    }

    /**
     * 处理不同服务类型的结果
     */
    handleServiceResult(serviceType, result, container) {
        // 这里可以根据服务类型调用相应的结果处理方法
        // 例如：图像生成、视频生成、语音合成等

        // 简单的通用处理
        const contentDiv = container.querySelector('[id$="-content"]');
        if (contentDiv && result.data) {
            if (result.data.file_path) {
                // 文件结果
                const link = document.createElement('a');
                link.href = result.data.file_path;
                link.textContent = '下载生成的文件';
                link.target = '_blank';
                contentDiv.innerHTML = '';
                contentDiv.appendChild(link);
            } else if (result.data.content) {
                // 文本结果
                contentDiv.textContent = result.data.content;
            }
        }
    }

    /**
     * 设置参数验证和帮助系统
     */
    setupParameterValidation() {
        // 为所有带有帮助文本的元素添加工具提示
        this.setupTooltips();

        // 设置参数范围验证
        this.setupRangeValidation();

        // 设置模型相关的参数联动
        this.setupModelDependencies();
    }

    /**
     * 设置工具提示
     */
    setupTooltips() {
        const helpTexts = document.querySelectorAll('.help-text');
        helpTexts.forEach(helpText => {
            const parentGroup = helpText.closest('.form-group');
            if (parentGroup) {
                const input = parentGroup.querySelector('input, select, textarea');
                if (input) {
                    input.title = helpText.textContent;
                }
            }
        });
    }

    /**
     * 设置范围验证
     */
    setupRangeValidation() {
        // 语音参数验证
        this.setupRangeInput('speech-speed', 0.5, 2.0, '语速');
        this.setupRangeInput('speech-volume', 0.1, 10.0, '音量');
        this.setupRangeInput('speech-pitch', -12, 12, '音高');

        // 图像参数验证
        this.setupRangeInput('image-reference-strength', 0.1, 1.0, '参考强度');
        this.setupNumberInput('image-seed', 0, 4294967295, '随机种子');

        // 视频参数验证
        this.setupRangeInput('video-frame-strength', 0.1, 1.0, '首帧影响强度');
        this.setupRangeInput('video-motion-intensity', 0.1, 1.0, '运动强度');
        this.setupNumberInput('video-seed', 0, 4294967295, '随机种子');
    }

    /**
     * 设置范围输入验证
     */
    setupRangeInput(elementId, min, max, name) {
        const input = document.getElementById(elementId);
        if (!input) return;

        input.addEventListener('input', (e) => {
            const value = parseFloat(e.target.value);
            if (value < min || value > max) {
                this.showNotification(`${name}必须在${min}到${max}之间`, 'warning');
                e.target.value = Math.max(min, Math.min(max, value));
            }
        });
    }

    /**
     * 设置数字输入验证
     */
    setupNumberInput(elementId, min, max, name) {
        const input = document.getElementById(elementId);
        if (!input) return;

        input.addEventListener('blur', (e) => {
            if (e.target.value === '') return; // 允许空值

            const value = parseInt(e.target.value);
            if (isNaN(value) || value < min || value > max) {
                this.showNotification(`${name}必须是${min}到${max}之间的整数`, 'warning');
                e.target.value = '';
            }
        });
    }

    /**
     * 设置模型相关的参数联动
     */
    setupModelDependencies() {
        // 视频模型联动
        const videoModelSelect = document.getElementById('video-model');
        const videoDurationSelect = document.getElementById('video-duration');
        const videoResolutionSelect = document.getElementById('video-resolution');

        if (videoModelSelect && videoDurationSelect && videoResolutionSelect) {
            videoModelSelect.addEventListener('change', (e) => {
                this.updateVideoModelDependencies(e.target.value, videoDurationSelect, videoResolutionSelect);
            });
        }

        // 移除旧的语音模型联动逻辑，现在统一使用语音选择器
    }

    /**
     * 更新视频模型相关参数
     */
    updateVideoModelDependencies(model, durationSelect, resolutionSelect) {
        // 清空现有选项
        durationSelect.innerHTML = '';
        resolutionSelect.innerHTML = '';

        if (model === 'MiniMax-Hailuo-02') {
            // Hailuo-02 模型的特殊参数
            durationSelect.innerHTML = `
                <option value="6">6秒</option>
                <option value="10" selected>10秒</option>
            `;
            resolutionSelect.innerHTML = `
                <option value="768P">768P</option>
                <option value="1080P" selected>1080P</option>
            `;
        } else {
            // 其他模型的通用参数
            durationSelect.innerHTML = `
                <option value="5">5秒</option>
                <option value="10" selected>10秒</option>
                <option value="15">15秒</option>
                <option value="30">30秒</option>
            `;
            resolutionSelect.innerHTML = `
                <option value="720P">720P</option>
                <option value="1080P" selected>1080P</option>
                <option value="1440P">1440P</option>
            `;
        }
    }

    // 移除旧的语音模型依赖更新方法，现在统一使用语音选择器

    /**
     * 设置语音参数滑块监听器
     */
    setupSpeechParameterListeners() {
        // 语速滑块
        const speedRange = document.getElementById('speech-speed');
        const speedValue = document.getElementById('speech-speed-value');
        if (speedRange && speedValue) {
            speedRange.addEventListener('input', (e) => {
                speedValue.textContent = parseFloat(e.target.value).toFixed(1);
            });
        }

        // 音量滑块
        const volumeRange = document.getElementById('speech-volume');
        const volumeValue = document.getElementById('speech-volume-value');
        if (volumeRange && volumeValue) {
            volumeRange.addEventListener('input', (e) => {
                volumeValue.textContent = parseFloat(e.target.value).toFixed(1);
            });
        }

        // 音高滑块
        const pitchRange = document.getElementById('speech-pitch');
        const pitchValue = document.getElementById('speech-pitch-value');
        if (pitchRange && pitchValue) {
            pitchRange.addEventListener('input', (e) => {
                const value = parseInt(e.target.value);
                pitchValue.textContent = value > 0 ? `+${value}` : value.toString();
            });
        }
    }

    /**
     * 获取语音合成参数
     */
    getSpeechParameters() {
        // 简化的参数获取，只返回ElevenLabs需要的参数
        return {
            model_id: 'eleven_multilingual_v2',
            output_format: 'mp3_44100_128'
        };
    }

    // 旧的语音合成方法已移除，现在由 speech-synthesis.js 模块处理

    /**
     * 显示语音合成结果
     */
    displaySpeechResult(result) {
        const resultContent = document.getElementById('speech-content');

        // 直接处理result数据，不需要检查result.success
        if (result) {
            // 尝试多种可能的音频URL字段
            const audioUrl = result.audio_url ||
                result.audio_file ||
                result.url ||
                result.file_path ||
                result.audio_path;

            const duration = result.duration || '未知';
            const format = result.format || result.output_format || 'MP3';
            const provider = result.provider || 'ElevenLabs';

            if (audioUrl) {
                resultContent.innerHTML = `
                    <div class="speech-result">
                        <div class="success-message">
                            <span class="success-icon">✅</span>
                            <span>语音合成成功！</span>
                        </div>
                        <div class="audio-player">
                            <audio controls preload="metadata">
                                <source src="${audioUrl}" type="audio/mpeg">
                                <source src="${audioUrl}" type="audio/wav">
                                您的浏览器不支持音频播放。
                            </audio>
                        </div>
                        <div class="speech-meta">
                            <span>时长: ${duration}秒</span>
                            <span>格式: ${format.toUpperCase()}</span>
                            <span>提供商: ${provider}</span>
                        </div>
                        <div class="speech-actions">
                            <a href="${audioUrl}" download="speech.${format.toLowerCase()}" class="download-btn">下载音频</a>
                            <button onclick="navigator.clipboard.writeText('${audioUrl}')" class="copy-btn">复制链接</button>
                        </div>
                        <div class="audio-url">
                            <small>音频链接: <a href="${audioUrl}" target="_blank">${audioUrl}</a></small>
                        </div>
                    </div>
                `;
            } else {
                resultContent.innerHTML = `
                    <div class="error">
                        <span class="error-icon">❌</span>
                        <span>语音合成成功，但未找到音频文件链接</span>
                        <details>
                            <summary>调试信息</summary>
                            <pre>${JSON.stringify(result.data, null, 2)}</pre>
                        </details>
                    </div>
                `;
            }
        } else {
            resultContent.innerHTML = `
                <div class="error">
                    <span class="error-icon">❌</span>
                    <span>语音合成失败，请重试</span>
                    <details>
                        <summary>错误详情</summary>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    </details>
                </div>
            `;
        }
    }

    /**
     * 设置视频参数监听器
     */
    setupVideoParameterListeners() {
        // 首帧影响强度滑块
        const frameStrengthRange = document.getElementById('video-frame-strength');
        const frameStrengthValue = document.getElementById('video-frame-strength-value');
        if (frameStrengthRange && frameStrengthValue) {
            frameStrengthRange.addEventListener('input', (e) => {
                frameStrengthValue.textContent = parseFloat(e.target.value).toFixed(1);
            });
        }

        // 运动强度滑块
        const motionIntensityRange = document.getElementById('video-motion-intensity');
        const motionIntensityValue = document.getElementById('video-motion-intensity-value');
        if (motionIntensityRange && motionIntensityValue) {
            motionIntensityRange.addEventListener('input', (e) => {
                motionIntensityValue.textContent = parseFloat(e.target.value).toFixed(1);
            });
        }

        // 首帧图片上传处理
        const uploadInput = document.getElementById('video-first-frame-upload');
        if (uploadInput) {
            uploadInput.addEventListener('change', (e) => this.handleVideoFrameUpload(e));
        }

        // 清除首帧图片
        const clearBtn = document.getElementById('clear-video-first-frame');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => this.clearVideoFirstFrame());
        }
    }

    /**
     * 处理视频首帧图片上传
     */
    handleVideoFrameUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        if (!file.type.startsWith('image/')) {
            this.showNotification('请选择有效的图片文件', 'warning');
            return;
        }

        const previewContainer = document.getElementById('video-first-frame-preview');
        const previewDiv = document.getElementById('video-frame-preview');

        if (!previewContainer || !previewDiv) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            const previewItem = document.createElement('div');
            previewItem.className = 'preview-item';

            const img = document.createElement('img');
            img.src = e.target.result;
            img.dataset.base64 = e.target.result;
            img.dataset.filename = file.name;

            const removeBtn = document.createElement('button');
            removeBtn.className = 'remove-btn';
            removeBtn.innerHTML = '×';
            removeBtn.onclick = () => this.clearVideoFirstFrame();

            previewItem.appendChild(img);
            previewItem.appendChild(removeBtn);

            previewDiv.innerHTML = '';
            previewDiv.appendChild(previewItem);
            previewContainer.style.display = 'block';
        };

        reader.readAsDataURL(file);
        event.target.value = '';
    }

    /**
     * 清除视频首帧图片
     */
    clearVideoFirstFrame() {
        const previewContainer = document.getElementById('video-first-frame-preview');
        const previewDiv = document.getElementById('video-frame-preview');

        if (previewDiv) {
            previewDiv.innerHTML = '';
        }

        if (previewContainer) {
            previewContainer.style.display = 'none';
        }
    }

    /**
     * 获取视频生成参数
     */
    getVideoParameters() {
        const modelSelect = document.getElementById('video-model');
        const durationSelect = document.getElementById('video-duration');
        const resolutionSelect = document.getElementById('video-resolution');
        const aspectRatioSelect = document.getElementById('video-aspect-ratio');
        const asyncModeCheckbox = document.getElementById('video-async-mode');
        const frameStrengthRange = document.getElementById('video-frame-strength');
        const motionIntensityRange = document.getElementById('video-motion-intensity');
        const seedInput = document.getElementById('video-seed');
        const styleSelect = document.getElementById('video-style');

        // 获取首帧图片
        const firstFrame = this.getVideoFirstFrame();

        return {
            // 基础参数
            model: modelSelect?.value || 'MiniMax-Hailuo-02',
            duration: parseInt(durationSelect?.value || 10),
            resolution: resolutionSelect?.value || '1080P',
            aspect_ratio: aspectRatioSelect?.value || '16:9',

            // 异步模式
            async_mode: asyncModeCheckbox?.checked !== false,

            // 首帧参数
            first_frame: firstFrame,
            frame_strength: parseFloat(frameStrengthRange?.value || 0.8),

            // 运动参数
            motion_intensity: parseFloat(motionIntensityRange?.value || 0.5),

            // 风格和种子
            style: styleSelect?.value || '',
            seed: seedInput?.value ? parseInt(seedInput.value) : null,

            // 兼容性参数
            fps: 30
        };
    }

    /**
     * 获取视频首帧图片数据
     */
    getVideoFirstFrame() {
        const previewDiv = document.getElementById('video-frame-preview');
        if (!previewDiv) return null;

        const img = previewDiv.querySelector('img');
        if (img && img.dataset.base64) {
            return {
                data: img.dataset.base64,
                filename: img.dataset.filename || 'first_frame.jpg'
            };
        }

        return null;
    }

    /**
     * 视频生成
     */
    async generateVideo() {
        const promptArea = document.getElementById('video-prompt');
        const providerSelect = document.getElementById('video-provider');
        const generateBtn = document.getElementById('generate-video-btn');
        const resultContainer = document.getElementById('video-result');
        const resultContent = document.getElementById('video-content');

        const prompt = promptArea.value.trim();
        if (!prompt) {
            this.showNotification('请输入视频描述', 'warning');
            return;
        }

        // 获取所有视频参数
        const videoParams = this.getVideoParameters();

        // 检查是否启用异步模式
        if (videoParams.async_mode) {
            // 使用异步生成
            const success = await this.startAsyncGeneration(
                'video_generation',
                providerSelect.value || null,
                prompt,
                videoParams,
                generateBtn,
                resultContainer
            );

            if (!success) {
                // 如果异步启动失败，恢复按钮状态
                generateBtn.disabled = false;
                generateBtn.querySelector('.btn-text').style.display = 'inline';
                generateBtn.querySelector('.btn-loading').style.display = 'none';
            }
            return;
        }

        // 同步模式：更新按钮状态
        generateBtn.disabled = true;
        generateBtn.querySelector('.btn-text').style.display = 'none';
        generateBtn.querySelector('.btn-loading').style.display = 'inline';

        try {
            const response = await fetch('/api/ai/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    service_type: 'video_generation',
                    provider: providerSelect.value || null,
                    prompt: prompt,
                    parameters: videoParams
                })
            });

            if (response.ok) {
                try {
                    const result = await response.json();
                    this.displayVideoResult(result);
                    resultContainer.style.display = 'block';
                } catch (parseError) {
                    console.error('Failed to parse video generation response as JSON:', parseError);
                    this.showNotification('视频生成响应格式错误，请联系管理员', 'error');
                }
            } else {
                let errorMessage = '未知错误';
                try {
                    const error = await response.json();
                    errorMessage = error.message || errorMessage;
                } catch (parseError) {
                    // 如果错误响应不是JSON格式，使用状态文本
                    errorMessage = response.statusText || `HTTP ${response.status}`;
                    console.error('Failed to parse error response as JSON:', parseError);
                }
                this.showNotification(`视频生成失败: ${errorMessage}`, 'error');
            }

        } catch (error) {
            console.error('Video generation error:', error);
            this.showNotification('视频生成请求失败，请检查网络连接', 'error');
        } finally {
            // 恢复按钮状态
            generateBtn.disabled = false;
            generateBtn.querySelector('.btn-text').style.display = 'inline';
            generateBtn.querySelector('.btn-loading').style.display = 'none';
        }
    }

    /**
     * 显示视频生成结果
     */
    displayVideoResult(result) {
        const resultContent = document.getElementById('video-content');

        if (result.success && result.data) {
            const videoUrl = result.data.video_url || result.data.url;
            const duration = result.data.duration || '未知';
            const resolution = result.data.resolution || '未知';

            resultContent.innerHTML = `
                <div class="video-result">
                    <div class="video-player">
                        <video controls width="100%" height="auto">
                            <source src="${videoUrl}" type="video/mp4">
                            您的浏览器不支持视频播放。
                        </video>
                    </div>
                    <div class="video-meta">
                        <span>时长: ${duration}秒</span>
                        <span>分辨率: ${resolution}</span>
                        <span>提供商: ${result.metadata?.provider || '未知'}</span>
                    </div>
                    <div class="video-actions">
                        <a href="${videoUrl}" download="generated_video.mp4" class="download-btn">下载视频</a>
                        <button onclick="window.app.saveVideoToMediaLibrary('${videoUrl}', '${document.getElementById('video-prompt').value}', '${duration}', '${resolution}')" class="save-btn">保存到媒体库</button>
                    </div>
                </div>
            `;
        } else {
            resultContent.innerHTML = '<div class="error">视频生成失败，请重试</div>';
        }
    }

    /**
     * 设置图像参数监听器
     */
    setupImageParameterListeners() {
        // 参考强度滑块
        const strengthRange = document.getElementById('image-reference-strength');
        const strengthValue = document.getElementById('image-reference-strength-value');
        if (strengthRange && strengthValue) {
            strengthRange.addEventListener('input', (e) => {
                strengthValue.textContent = parseFloat(e.target.value).toFixed(1);
            });
        }

        // 图片上传处理
        const uploadInput = document.getElementById('image-reference-upload');
        if (uploadInput) {
            uploadInput.addEventListener('change', (e) => this.handleImageUpload(e));
        }

        // 清除参考图片
        const clearBtn = document.getElementById('clear-image-references');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => this.clearReferenceImages());
        }
    }

    /**
     * 处理图片上传
     */
    handleImageUpload(event) {
        const files = event.target.files;
        if (!files || files.length === 0) return;

        const previewContainer = document.getElementById('image-reference-preview');
        const previewGrid = document.getElementById('image-preview-grid');

        if (!previewContainer || !previewGrid) return;

        Array.from(files).forEach(file => {
            if (!file.type.startsWith('image/')) {
                this.showNotification(`文件 ${file.name} 不是有效的图片格式`, 'warning');
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                const previewItem = document.createElement('div');
                previewItem.className = 'preview-item';

                const img = document.createElement('img');
                img.src = e.target.result;
                img.dataset.base64 = e.target.result;
                img.dataset.filename = file.name;

                const removeBtn = document.createElement('button');
                removeBtn.className = 'remove-btn';
                removeBtn.innerHTML = '×';
                removeBtn.onclick = () => {
                    previewItem.remove();
                    if (previewGrid.children.length === 0) {
                        previewContainer.style.display = 'none';
                    }
                };

                previewItem.appendChild(img);
                previewItem.appendChild(removeBtn);
                previewGrid.appendChild(previewItem);

                previewContainer.style.display = 'block';
            };

            reader.readAsDataURL(file);
        });

        // 清空input以允许重复选择相同文件
        event.target.value = '';
    }

    /**
     * 清除所有参考图片
     */
    clearReferenceImages() {
        const previewContainer = document.getElementById('image-reference-preview');
        const previewGrid = document.getElementById('image-preview-grid');

        if (previewGrid) {
            previewGrid.innerHTML = '';
        }

        if (previewContainer) {
            previewContainer.style.display = 'none';
        }
    }

    /**
     * 获取图像生成参数
     */
    getImageParameters() {
        const modelSelect = document.getElementById('image-model');
        const aspectRatioSelect = document.getElementById('image-aspect-ratio');
        const countSelect = document.getElementById('image-count');
        const optimizerCheckbox = document.getElementById('image-prompt-optimizer');
        const styleSelect = document.getElementById('image-style');
        const strengthRange = document.getElementById('image-reference-strength');
        const seedInput = document.getElementById('image-seed');

        // 获取参考图片
        const referenceImages = this.getReferenceImages();

        return {
            // 基础参数
            model: modelSelect?.value || 'image-01',
            aspect_ratio: aspectRatioSelect?.value || '1:1',
            n: parseInt(countSelect?.value || 1),

            // 提示词优化
            prompt_optimizer: optimizerCheckbox?.checked !== false,

            // 风格参数
            style: styleSelect?.value || '',

            // 参考图片参数
            reference_images: referenceImages,
            reference_strength: parseFloat(strengthRange?.value || 0.7),

            // 随机种子
            seed: seedInput?.value ? parseInt(seedInput.value) : null,

            // 兼容性参数（保持向后兼容）
            size: aspectRatioSelect?.value === '1:1' ? '1024x1024' :
                aspectRatioSelect?.value === '16:9' ? '1920x1080' :
                    aspectRatioSelect?.value === '9:16' ? '1080x1920' : '1024x1024',
            quality: 'high'
        };
    }

    /**
     * 获取参考图片数据
     */
    getReferenceImages() {
        const images = [];

        // 从状态中获取参考图片
        this.state.referenceImages.forEach(img => {
            if (img.type === 'davinci_frame') {
                // DaVinci静帧使用文件路径
                images.push({
                    file_path: img.filePath,
                    filename: 'davinci_frame.png',
                    type: 'file_path'
                });
            } else if (img.data) {
                // 上传的图片使用base64数据
                images.push({
                    data: img.data,
                    filename: img.filename || 'reference.jpg',
                    type: 'base64'
                });
            }
        });

        // 兼容性：也从DOM中获取（用于向后兼容）
        const previewGrid = document.getElementById('image-preview-grid');
        if (previewGrid) {
            const imgElements = previewGrid.querySelectorAll('img');
            imgElements.forEach(img => {
                if (img.dataset.base64 && !this.state.referenceImages.some(ref => ref.data === img.dataset.base64)) {
                    images.push({
                        data: img.dataset.base64,
                        filename: img.dataset.filename || 'reference.jpg',
                        type: 'base64'
                    });
                }
            });
        }

        return images;
    }

    /**
     * 图像生成
     */
    async generateImage() {
        const promptArea = document.getElementById('image-prompt');
        const providerSelect = document.getElementById('image-provider');
        const generateBtn = document.getElementById('generate-image-btn');
        const resultContainer = document.getElementById('image-result');
        const resultContent = document.getElementById('image-content');

        const prompt = promptArea.value.trim();
        if (!prompt) {
            this.showNotification('请输入图像描述', 'warning');
            return;
        }

        // 获取所有图像参数
        const imageParams = this.getImageParameters();

        // 更新按钮状态
        generateBtn.disabled = true;
        generateBtn.querySelector('.btn-text').style.display = 'none';
        generateBtn.querySelector('.btn-loading').style.display = 'inline';

        // 显示进度提示
        this.showNotification('正在生成图像，预计需要30-60秒，请耐心等待...', 'info');

        try {
            // 创建AbortController用于超时控制 - 图像生成需要更长时间
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 180000); // 3分钟超时

            const response = await fetch('/api/ai/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    service_type: 'image_generation',
                    provider: providerSelect.value || null,
                    prompt: prompt,
                    parameters: imageParams
                }),
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (response.ok) {
                try {
                    const result = await response.json();
                    this.displayImageResult(result);
                    resultContainer.style.display = 'block';
                    this.showNotification('图像生成成功！', 'success');
                } catch (parseError) {
                    console.error('Failed to parse image generation response as JSON:', parseError);
                    this.showNotification('图像生成响应格式错误，请联系管理员', 'error');
                }
            } else {
                let errorMessage = '未知错误';
                try {
                    const error = await response.json();
                    errorMessage = error.message || errorMessage;
                } catch (parseError) {
                    // 如果错误响应不是JSON格式，使用状态文本
                    errorMessage = response.statusText || `HTTP ${response.status}`;
                    console.error('Failed to parse error response as JSON:', parseError);
                }
                this.showNotification(`图像生成失败: ${errorMessage}`, 'error');
            }

        } catch (error) {
            console.error('Image generation error:', error);
            if (error.name === 'AbortError') {
                this.showNotification('图像生成请求超时，请重试', 'error');
            } else {
                this.showNotification('图像生成请求失败，请检查网络连接', 'error');
            }
        } finally {
            // 恢复按钮状态
            generateBtn.disabled = false;
            generateBtn.querySelector('.btn-text').style.display = 'inline';
            generateBtn.querySelector('.btn-loading').style.display = 'none';
        }
    }

    /**
     * 显示图像生成结果
     */
    displayImageResult(result) {
        const resultContent = document.getElementById('image-content');

        if (result.success && result.data) {
            // 处理多张图片的情况
            const imageUrls = result.data.image_urls || result.data.images ||
                (result.data.image_url ? [result.data.image_url] : []) ||
                (result.data.url ? [result.data.url] : []);

            const count = result.data.count || imageUrls.length || 1;
            const aspectRatio = result.data.aspect_ratio || '未知';

            if (imageUrls.length > 0) {
                const imagesHtml = imageUrls.map((url, index) => `
                    <div class="image-item">
                        <img src="${url}" alt="Generated Image ${index + 1}"
                             style="max-width: 100%; height: auto; border-radius: 8px; margin-bottom: 10px;">
                        <div class="image-item-actions">
                            <a href="${url}" download="generated_image_${index + 1}.jpg" class="download-btn">下载图片 ${index + 1}</a>
                            <button onclick="navigator.clipboard.writeText('${url}')" class="copy-btn">复制链接</button>
                            <button onclick="window.app.saveToMediaLibrary('${url}', 'ai_image', '${document.getElementById('image-prompt').value}', ${index + 1})" class="save-btn">保存到媒体库</button>
                        </div>
                    </div>
                `).join('');

                resultContent.innerHTML = `
                    <div class="image-result">
                        <div class="success-message">
                            <span class="success-icon">✅</span>
                            <span>图像生成成功！生成了 ${count} 张图片</span>
                        </div>
                        <div class="image-gallery">
                            ${imagesHtml}
                        </div>
                        <div class="image-meta">
                            <span>数量: ${count}张</span>
                            <span>宽高比: ${aspectRatio}</span>
                            <span>提供商: ${result.metadata?.provider || 'MiniMax'}</span>
                        </div>
                        <div class="batch-actions">
                            <button onclick="this.downloadAllImages(${JSON.stringify(imageUrls)})" class="download-all-btn">下载全部</button>
                        </div>
                    </div>
                `;
            } else {
                resultContent.innerHTML = `
                    <div class="error">
                        <span class="error-icon">❌</span>
                        <span>图像生成成功，但未找到图片链接</span>
                        <details>
                            <summary>调试信息</summary>
                            <pre>${JSON.stringify(result.data, null, 2)}</pre>
                        </details>
                    </div>
                `;
            }
        } else {
            resultContent.innerHTML = `
                <div class="error">
                    <span class="error-icon">❌</span>
                    <span>图像生成失败，请重试</span>
                    <details>
                        <summary>错误详情</summary>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    </details>
                </div>
            `;
        }
    }

    /**
     * 翻译
     */
    async translateText() {
        const textArea = document.getElementById('translation-text');
        const sourceLangSelect = document.getElementById('source-language');
        const targetLangSelect = document.getElementById('target-language');
        const providerSelect = document.getElementById('translation-provider');
        const translateBtn = document.getElementById('translate-btn');
        const resultContainer = document.getElementById('translation-result');
        const resultContent = document.getElementById('translation-content');

        const text = textArea.value.trim();
        if (!text) {
            this.showNotification('请输入要翻译的文本', "warning");
            return;
        }

        // 更新按钮状态
        translateBtn.disabled = true;
        translateBtn.querySelector('.btn-text').style.display = 'none';
        translateBtn.querySelector('.btn-loading').style.display = 'inline';

        try {
            // 使用统一AI服务管理器
            if (!window.unifiedAIServiceManager || !window.unifiedAIServiceManager.isInitialized) {
                throw new Error('统一AI服务管理器未就绪');
            }

            const result = await window.unifiedAIServiceManager.callService(
                'translation',
                providerSelect.value || null,
                {
                    prompt: text,
                    source_language: sourceLangSelect.value,
                    target_language: targetLangSelect.value,
                    original_text: text
                }
            );

            if (result.success) {
                this.displayTranslationResult(result);
                resultContainer.style.display = 'block';
            } else {
                this.showNotification(`翻译失败: ${result.error}`, 'error');
            }

        } catch (error) {
            console.error('Translation error:', error);
            this.showNotification('翻译请求失败，请检查网络连接', "error");
        } finally {
            // 恢复按钮状态
            translateBtn.disabled = false;
            translateBtn.querySelector('.btn-text').style.display = 'inline';
            translateBtn.querySelector('.btn-loading').style.display = 'none';
        }
    }

    /**
     * 显示翻译结果
     */
    displayTranslationResult(result) {
        const resultContent = document.getElementById('translation-content');

        if (result.success && result.data) {
            // 统一响应格式处理
            const translatedText = result.data.translated_text || result.data.text || result.data.content || '翻译失败';
            const sourceLang = result.data.source_language || '未知';
            const targetLang = result.data.target_language || '未知';
            const originalText = result.data.original_text || '原文';

            // 从统一格式中获取元数据
            const provider = result.data.provider || result.metadata?.provider || '未知';
            const model = result.data.model || result.metadata?.model || '未知';
            const processingTime = result.data.processing_time || result.metadata?.processing_time || 0;

            if (translatedText && translatedText !== '翻译失败') {
                resultContent.innerHTML = `
                    <div class="translation-result">
                        <div class="translation-pair">
                            <div class="source-text">
                                <h4>原文 (${sourceLang}):</h4>
                                <p>${originalText}</p>
                            </div>
                            <div class="target-text">
                                <h4>译文 (${targetLang}):</h4>
                                <p>${translatedText}</p>
                            </div>
                        </div>
                        <div class="translation-meta">
                            <span>字符数: ${translatedText.length}</span>
                            <span>提供商: ${provider}</span>
                            <span>模型: ${model}</span>
                            <span>耗时: ${processingTime}ms</span>
                        </div>
                    </div>
                `;
                this.showNotification('✓ 已翻译为中文', 'success');
            } else {
                resultContent.innerHTML = '<div class="error">翻译结果为空</div>';
                this.showNotification('翻译结果为空', 'error');
            }
        } else {
            resultContent.innerHTML = '<div class="error">翻译失败，请重试</div>';
            this.showNotification(`翻译失败: ${result.error || '未知错误'}`, 'error');
        }
    }

    /**
     * 自动翻译文本框内容
     */
    async autoTranslateTextarea(textareaId) {
        const textarea = document.getElementById(textareaId);
        if (!textarea) {
            this.showNotification('找不到文本框', 'error');
            return;
        }

        const text = textarea.value.trim();
        if (!text) {
            this.showNotification('请输入要翻译的文本', 'warning');
            return;
        }

        // 检测语言并确定翻译方向
        const isChineseText = /[\u4e00-\u9fff]/.test(text);
        const sourceLang = isChineseText ? 'zh' : 'auto';
        const targetLang = isChineseText ? 'en' : 'zh';

        // 找到对应的翻译按钮并显示加载状态
        const translateBtn = textarea.parentElement.querySelector('.translate-btn');
        if (translateBtn) {
            translateBtn.disabled = true;
            const iconSpan = translateBtn.querySelector('.translate-icon');
            const textSpan = translateBtn.querySelector('.translate-text');
            if (iconSpan) iconSpan.textContent = '🔄';
            if (textSpan) textSpan.textContent = '翻译中...';
        }

        try {
            const response = await fetch('/api/ai/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    service_type: 'translation',
                    provider: null, // 自动选择
                    prompt: text,
                    parameters: {
                        source_language: sourceLang,
                        target_language: targetLang
                    }
                })
            });

            if (response.ok) {
                try {
                    const result = await response.json();
                    if (result.success && result.data) {
                        const translatedText = result.data.translated_text || result.data.text;
                        if (translatedText) {
                            // 替换文本框内容
                            textarea.value = translatedText;
                            this.showNotification(`已翻译为${targetLang === 'zh' ? '中文' : '英文'}`, 'success');
                        } else {
                            this.showNotification('翻译结果为空', 'warning');
                        }
                    } else {
                        this.showNotification(result.message || '翻译失败', 'error');
                    }
                } catch (parseError) {
                    console.error('Failed to parse translation response:', parseError);
                    this.showNotification('翻译响应格式错误', 'error');
                }
            } else {
                let errorMessage = '翻译失败';
                try {
                    const error = await response.json();
                    errorMessage = error.message || errorMessage;
                } catch (parseError) {
                    errorMessage = response.statusText || `HTTP ${response.status}`;
                }
                this.showNotification(errorMessage, 'error');
            }
        } catch (error) {
            console.error('Translation error:', error);
            this.showNotification('翻译请求失败，请检查网络连接', 'error');
        } finally {
            // 恢复按钮状态
            if (translateBtn) {
                translateBtn.disabled = false;
                const iconSpan = translateBtn.querySelector('.translate-icon');
                const textSpan = translateBtn.querySelector('.translate-text');
                if (iconSpan) iconSpan.textContent = '🌐';
                if (textSpan) textSpan.textContent = '翻译';
            }
        }
    }

    /**
     * DaVinci集成 - 获取状态
     */
    async loadDaVinciStatus() {
        const statusContainer = document.getElementById('davinci-status');

        try {
            const response = await fetch('/api/davinci/status');
            const result = await response.json();

            if (result.success) {
                const status = result.data;
                statusContainer.innerHTML = `
                    <div class="davinci-status">
                        <div class="status-item">
                            <span class="status-label">API可用:</span>
                            <span class="status-value ${status.api_available ? 'success' : 'error'}">
                                ${status.api_available ? '✓ 可用' : '✗ 不可用'}
                            </span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">连接状态:</span>
                            <span class="status-value ${status.connected ? 'success' : 'error'}">
                                ${status.connected ? '✓ 已连接' : '✗ 未连接'}
                            </span>
                        </div>
                        <!-- 项目状态显示已移除 -->
                    </div>
                `;
            } else {
                statusContainer.innerHTML = `
                    <div class="error">
                        <h4>❌ 连接失败</h4>
                        <p><strong>错误信息:</strong> ${result.message || '未知错误'}</p>
                        <div class="error-help">
                            <p><strong>可能的解决方案:</strong></p>
                            <ul>
                                <li>确保 DaVinci Resolve 已启动并运行</li>
                                <li>检查 DaVinci Resolve 的脚本API是否已启用</li>
                                <li>确认 Python API 模块已正确安装</li>
                                <li>重启 DaVinci Resolve 并重新尝试连接</li>
                            </ul>
                        </div>
                    </div>
                `;
            }
        } catch (error) {
            console.error('DaVinci status error:', error);
            statusContainer.innerHTML = `
                <div class="error">
                    <h4>❌ 网络错误</h4>
                    <p><strong>错误信息:</strong> ${error.message || '网络请求失败'}</p>
                    <div class="error-help">
                        <p><strong>可能的解决方案:</strong></p>
                        <ul>
                            <li>检查网络连接是否正常</li>
                            <li>确认服务器正在运行</li>
                            <li>刷新页面重新尝试</li>
                        </ul>
                    </div>
                </div>
            `;
        }
    }

    /**
     * 连接DaVinci
     */
    async connectDaVinci() {
        const connectBtn = document.getElementById('connect-davinci-btn');

        connectBtn.disabled = true;
        connectBtn.textContent = '连接中...';

        try {
            const response = await fetch('/api/davinci/connect', {
                method: 'POST'
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification('DaVinci连接成功！', "success");
                this.loadDaVinciStatus();
            } else {
                this.showNotification(`连接失败: ${result.message}`, "error");
            }
        } catch (error) {
            console.error('DaVinci connect error:', error);
            this.showNotification('连接请求失败，请检查DaVinci Resolve是否运行', "error");
        } finally {
            connectBtn.disabled = false;
            connectBtn.textContent = '连接DaVinci';
        }
    }

    // 项目列表加载功能已移除

    // 项目加载功能已移除

    /**
     * 获取能力名称的中文翻译
     */
    getCapabilityName(capability) {
        const names = {
            'text_generation': '文本生成',
            'text_analysis': '文案分析',
            'speech_synthesis': '语音合成',
            'video_generation': '视频生成',
            'image_generation': '图像生成',
            'translation': '翻译',
            'prompt_enhancement': '✨ 提示词增强'
        };
        return names[capability] || capability;
    }

    /**
     * 文本生成
     */
    async generateText() {
        const promptArea = document.getElementById('generation-prompt');
        const providerSelect = document.getElementById('generation-provider');
        const lengthSelect = document.getElementById('generation-length');
        const generateBtn = document.getElementById('generate-text-btn');
        const resultContainer = document.getElementById('generation-result');
        const resultContent = document.getElementById('generation-content');

        const prompt = promptArea.value.trim();
        if (!prompt) {
            this.showNotification('请输入提示词', "warning");
            return;
        }

        // 更新按钮状态
        generateBtn.disabled = true;
        generateBtn.querySelector('.btn-text').style.display = 'none';
        generateBtn.querySelector('.btn-loading').style.display = 'inline';

        try {
            // 使用统一AI服务管理器
            if (!window.unifiedAIServiceManager || !window.unifiedAIServiceManager.isInitialized) {
                throw new Error('统一AI服务管理器未就绪');
            }

            // 根据长度设置max_tokens
            const lengthMap = {
                'short': 200,
                'medium': 500,
                'long': 800
            };

            const result = await window.unifiedAIServiceManager.callService(
                'text_generation',
                providerSelect.value || null,
                {
                    prompt: prompt,
                    max_tokens: lengthMap[lengthSelect.value] || 500,
                    temperature: 0.7
                }
            );

            if (result.success) {
                this.displayGenerationResult(result);
                resultContainer.style.display = 'block';
            } else {
                this.showNotification(`生成失败: ${result.error}`, 'error');
            }

        } catch (error) {
            console.error('Generation error:', error);
            this.showNotification('生成请求失败，请检查网络连接', 'error');
        } finally {
            // 恢复按钮状态
            generateBtn.disabled = false;
            generateBtn.querySelector('.btn-text').style.display = 'inline';
            generateBtn.querySelector('.btn-loading').style.display = 'none';
        }
    }

    /**
     * 显示文本生成结果
     */
    displayGenerationResult(result) {
        const resultContent = document.getElementById('generation-content');

        if (result.success && result.data) {
            const generatedText = result.data.text || result.data.content || '生成失败';

            resultContent.innerHTML = `
                <div class="generated-text">
                    <div class="text-content">${generatedText.replace(/\n/g, '<br>')}</div>
                    <div class="text-meta">
                        <span>字数: ${generatedText.length}</span>
                        <span>模型: ${result.data.model || '未知'}</span>
                        <span>提供商: ${result.data.provider || result.metadata?.provider || '未知'}</span>
                    </div>
                </div>
            `;
        } else {
            resultContent.innerHTML = '<div class="error">生成失败，请重试</div>';
        }
    }

    /**
     * 文案分析
     */
    async analyzeText() {
        const textArea = document.getElementById('analysis-text');
        const providerSelect = document.getElementById('analysis-provider');
        const analyzeBtn = document.getElementById('analyze-btn');
        const resultContainer = document.getElementById('analysis-result');
        const resultContent = document.getElementById('analysis-content');

        const text = textArea.value.trim();
        if (!text) {
            this.showNotification('请输入要分析的文案内容', "warning");
            return;
        }

        // 更新按钮状态
        analyzeBtn.disabled = true;
        analyzeBtn.querySelector('.btn-text').style.display = 'none';
        analyzeBtn.querySelector('.btn-loading').style.display = 'inline';

        try {
            const response = await fetch('/api/ai/analyze', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    service_type: 'text_analysis',
                    provider: providerSelect.value || null,
                    prompt: text,
                    parameters: {}
                })
            });

            if (response.ok) {
                try {
                    const result = await response.json();
                    this.displayAnalysisResult(result);
                    resultContainer.style.display = 'block';
                } catch (parseError) {
                    console.error('Failed to parse text analysis response as JSON:', parseError);
                    this.showNotification('文案分析响应格式错误，请联系管理员', 'error');
                }
            } else {
                let errorMessage = '未知错误';
                try {
                    const error = await response.json();
                    errorMessage = error.message || errorMessage;
                } catch (parseError) {
                    // 如果错误响应不是JSON格式，使用状态文本
                    errorMessage = response.statusText || `HTTP ${response.status}`;
                    console.error('Failed to parse error response as JSON:', parseError);
                }
                this.showNotification(`分析失败: ${errorMessage}`, 'error');
            }

        } catch (error) {
            console.error('Analysis error:', error);
            this.showNotification('分析请求失败，请检查网络连接', "error");
        } finally {
            // 恢复按钮状态
            analyzeBtn.disabled = false;
            analyzeBtn.querySelector('.btn-text').style.display = 'inline';
            analyzeBtn.querySelector('.btn-loading').style.display = 'none';
        }
    }

    /**
     * 显示分析结果
     */
    displayAnalysisResult(result) {
        const container = document.getElementById('analysis-content');
        if (!container) return;

        let html = '';

        if (result.success && result.data) {
            const data = result.data;

            if (data.scenes && data.scenes.length > 0) {
                html += '<h4>场景拆解:</h4>';
                data.scenes.forEach((scene, index) => {
                    html += `
                        <div class="scene-item">
                            <h5>场景 ${scene.id || index + 1}</h5>
                            <p><strong>描述:</strong> ${scene.description}</p>
                            <p><strong>关键词:</strong> ${scene.keywords ? scene.keywords.join(', ') : '无'}</p>
                            <p><strong>视觉元素:</strong> ${scene.visual_elements ? scene.visual_elements.join(', ') : '无'}</p>
                            <p><strong>时长:</strong> ${scene.duration || 0}秒</p>
                            <p><strong>情感:</strong> ${scene.emotion || '中性'}</p>
                        </div>
                    `;
                });
            }

            if (data.summary) {
                html += '<h4>总结:</h4>';
                html += `<p>总场景数: ${data.summary.total_scenes || 0}</p>`;
                html += `<p>总时长: ${data.summary.total_duration || 0}秒</p>`;
                if (data.summary.main_theme) {
                    html += `<p>主要主题: ${data.summary.main_theme}</p>`;
                }
            }

            if (data.raw_analysis) {
                html += '<h4>原始分析:</h4>';
                html += `<pre>${data.raw_analysis}</pre>`;
            }
        } else {
            html = `<p class="error">分析失败: ${result.error || '未知错误'}</p>`;
        }

        container.innerHTML = html;
    }

    /**
     * 清空文本生成
     */
    clearGeneration() {
        document.getElementById('generation-prompt').value = '';
        document.getElementById('generation-provider').value = '';
        document.getElementById('generation-length').value = 'medium';
        document.getElementById('generation-result').style.display = 'none';
    }

    /**
     * 清空文案分析
     */
    clearAnalysis() {
        document.getElementById('analysis-text').value = '';
        document.getElementById('analysis-provider').value = '';
        document.getElementById('analysis-result').style.display = 'none';
    }

    /**
     * 清空语音合成
     */
    clearSpeech() {
        document.getElementById('speech-text').value = '';
        document.getElementById('speech-provider').value = '';
        document.getElementById('speech-voice').value = 'female';
        document.getElementById('speech-result').style.display = 'none';
    }

    /**
     * 清空视频生成
     */
    clearVideo() {
        document.getElementById('video-prompt').value = '';
        document.getElementById('video-provider').value = '';
        document.getElementById('video-duration').value = '10';
        document.getElementById('video-result').style.display = 'none';
    }

    /**
     * 清空图像生成
     */
    clearImage() {
        document.getElementById('image-prompt').value = '';
        document.getElementById('image-provider').value = '';
        document.getElementById('image-size').value = '512x512';
        document.getElementById('image-result').style.display = 'none';
    }

    /**
     * 清空翻译
     */
    clearTranslation() {
        document.getElementById('translation-text').value = '';
        document.getElementById('translation-provider').value = '';
        document.getElementById('source-language').value = 'auto';
        document.getElementById('target-language').value = 'en';
        document.getElementById('translation-result').style.display = 'none';
    }

    /**
     * 交换翻译语言
     */
    swapLanguages() {
        const sourceLang = document.getElementById('source-language');
        const targetLang = document.getElementById('target-language');

        const temp = sourceLang.value;
        sourceLang.value = targetLang.value;
        targetLang.value = temp;
    }

    // 项目创建功能已移除

    /**
     * 保存设置
     */
    async saveSettings() {
        try {
            const settings = {
                deepseek_key: document.getElementById('deepseek-key').value,
                minimax_key: document.getElementById('minimax-key').value,
                volcano_key: document.getElementById('volcano-key').value,
                theme: document.getElementById('theme-select').value,
                auto_refresh: document.getElementById('auto-refresh').checked
            };

            // 这里应该发送到后端保存设置
            // 暂时使用localStorage保存
            localStorage.setItem('davinci_ai_settings', JSON.stringify(settings));

            // 显示成功消息
            this.showNotification('设置保存成功！', 'success');

        } catch (error) {
            console.error('Save settings error:', error);
            this.showNotification('设置保存失败', 'error');
        }
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        // 确保通知容器存在
        let container = document.querySelector('.notification-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'notification-container';
            document.body.appendChild(container);
        }

        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;

        // 获取图标
        const icons = {
            success: '✓',
            error: '✕',
            warning: '⚠',
            info: 'ℹ'
        };

        notification.innerHTML = `
            <div class="notification-icon">${icons[type] || icons.info}</div>
            <div class="notification-content">
                <div class="notification-message">${message}</div>
            </div>
            <button class="notification-close" onclick="this.parentElement.remove()">×</button>
        `;

        // 添加到容器
        container.appendChild(notification);

        // 显示动画
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // 自动隐藏
        setTimeout(() => {
            notification.classList.remove('show');
            notification.classList.add('hide');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 4000);
    }

    /**
     * 显示确认对话框
     */
    showConfirmDialog(message) {
        // 创建模态对话框
        const modal = document.createElement('div');
        modal.className = 'confirm-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10001;
        `;

        const dialog = document.createElement('div');
        dialog.className = 'confirm-dialog';
        dialog.style.cssText = `
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: 24px;
            max-width: 400px;
            width: 90%;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            color: var(--text-primary);
        `;

        dialog.innerHTML = `
            <h3 style="margin: 0 0 16px 0; color: var(--text-primary);">确认操作</h3>
            <p style="margin: 0 0 24px 0; color: var(--text-secondary);">${message}</p>
            <div style="display: flex; gap: 12px; justify-content: flex-end;">
                <button class="cancel-btn" style="
                    padding: 8px 16px;
                    border: 1px solid var(--border-color);
                    background: transparent;
                    color: var(--text-secondary);
                    border-radius: 4px;
                    cursor: pointer;
                ">取消</button>
                <button class="confirm-btn" style="
                    padding: 8px 16px;
                    border: none;
                    background: var(--accent-color);
                    color: white;
                    border-radius: 4px;
                    cursor: pointer;
                ">确认</button>
            </div>
        `;

        modal.appendChild(dialog);
        document.body.appendChild(modal);

        // 返回Promise以支持异步操作
        return new Promise((resolve) => {
            const confirmBtn = dialog.querySelector('.confirm-btn');
            const cancelBtn = dialog.querySelector('.cancel-btn');

            const cleanup = () => {
                document.body.removeChild(modal);
            };

            confirmBtn.addEventListener('click', () => {
                cleanup();
                resolve(true);
            });

            cancelBtn.addEventListener('click', () => {
                cleanup();
                resolve(false);
            });

            // 点击背景关闭
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    cleanup();
                    resolve(false);
                }
            });

            // ESC键关闭
            const handleEsc = (e) => {
                if (e.key === 'Escape') {
                    cleanup();
                    resolve(false);
                    document.removeEventListener('keydown', handleEsc);
                }
            };
            document.addEventListener('keydown', handleEsc);
        });
    }

    /**
     * 显示输入对话框
     */
    showInputDialog(title, message, defaultValue = '') {
        // 创建模态对话框
        const modal = document.createElement('div');
        modal.className = 'input-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10001;
        `;

        const dialog = document.createElement('div');
        dialog.className = 'input-dialog';
        dialog.style.cssText = `
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: 24px;
            max-width: 400px;
            width: 90%;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            color: var(--text-primary);
        `;

        dialog.innerHTML = `
            <h3 style="margin: 0 0 16px 0; color: var(--text-primary);">${title}</h3>
            <p style="margin: 0 0 16px 0; color: var(--text-secondary);">${message}</p>
            <input type="text" class="input-field" value="${defaultValue}" style="
                width: 100%;
                padding: 8px 12px;
                border: 1px solid var(--border-color);
                background: var(--bg-primary);
                color: var(--text-primary);
                border-radius: 4px;
                margin-bottom: 24px;
                box-sizing: border-box;
            ">
            <div style="display: flex; gap: 12px; justify-content: flex-end;">
                <button class="cancel-btn" style="
                    padding: 8px 16px;
                    border: 1px solid var(--border-color);
                    background: transparent;
                    color: var(--text-secondary);
                    border-radius: 4px;
                    cursor: pointer;
                ">取消</button>
                <button class="confirm-btn" style="
                    padding: 8px 16px;
                    border: none;
                    background: var(--accent-color);
                    color: white;
                    border-radius: 4px;
                    cursor: pointer;
                ">确认</button>
            </div>
        `;

        modal.appendChild(dialog);
        document.body.appendChild(modal);

        const inputField = dialog.querySelector('.input-field');
        inputField.focus();
        inputField.select();

        // 返回Promise以支持异步操作
        return new Promise((resolve) => {
            const confirmBtn = dialog.querySelector('.confirm-btn');
            const cancelBtn = dialog.querySelector('.cancel-btn');

            const cleanup = () => {
                document.body.removeChild(modal);
            };

            const handleConfirm = () => {
                const value = inputField.value.trim();
                cleanup();
                resolve(value || null);
            };

            const handleCancel = () => {
                cleanup();
                resolve(null);
            };

            confirmBtn.addEventListener('click', handleConfirm);
            cancelBtn.addEventListener('click', handleCancel);

            // Enter键确认
            inputField.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    handleConfirm();
                }
            });

            // 点击背景关闭
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    handleCancel();
                }
            });

            // ESC键关闭
            const handleEsc = (e) => {
                if (e.key === 'Escape') {
                    handleCancel();
                    document.removeEventListener('keydown', handleEsc);
                }
            };
            document.addEventListener('keydown', handleEsc);
        });
    }

    /**
     * 重置设置
     */
    async resetSettings() {
        // 创建确认对话框
        const confirmed = await this.showConfirmDialog('确定要重置所有设置吗？');
        if (confirmed) {
            document.getElementById('deepseek-key').value = '';
            document.getElementById('minimax-key').value = '';
            document.getElementById('volcano-key').value = '';
            document.getElementById('theme-select').value = 'dark';
            document.getElementById('auto-refresh').checked = true;
            this.showNotification('设置已重置', 'success');
        }
    }

    /**
     * 开始自动刷新
     */
    startAutoRefresh() {
        setInterval(() => {
            if (this.currentTab === 'dashboard') {
                this.loadServiceData();
            }
        }, 30000); // 30秒刷新一次
    }

    /**
     * 处理状态更新
     */
    handleStatusUpdate(data) {
        // 这里可以处理实时状态更新
    }

    /**
     * 处理进度更新
     */
    handleProgressUpdate(data) {
        // 这里可以处理进度更新
    }

    /**
     * 保存到媒体库
     */
    async saveToMediaLibrary(url, category, prompt = '', index = 1) {
        try {
            // 获取图片信息
            const img = new Image();
            img.crossOrigin = 'anonymous';

            img.onload = async () => {
                try {
                    const mediaData = {
                        name: `AI生成图像_${new Date().toLocaleString('zh-CN').replace(/[\/\s:]/g, '_')}_${index}`,
                        type: 'image',
                        category: category,
                        url: url,
                        file_path: url, // 对于AI生成的图片，使用URL作为路径
                        size: 0, // 无法直接获取文件大小
                        dimensions: {
                            width: img.width,
                            height: img.height
                        },
                        metadata: {
                            source: 'ai_generation',
                            prompt: prompt,
                            model: 'MiniMax',
                            generated_at: new Date().toISOString()
                        }
                    };

                    const response = await fetch('/api/media-library/items', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(mediaData)
                    });

                    if (response.ok) {
                        const result = await response.json();
                        if (result.success) {
                            this.showNotification(`图片已保存到媒体库`, 'success');

                            // 通知媒体库更新
                            if (window.mediaLibrary) {
                                window.mediaLibrary.addMediaItem(mediaData);
                            }
                        } else {
                            this.showNotification('保存到媒体库失败', 'error');
                        }
                    } else {
                        this.showNotification('保存到媒体库失败', 'error');
                    }
                } catch (error) {
                    console.error('Save to media library error:', error);
                    this.showNotification('保存到媒体库失败', 'error');
                }
            };

            img.onerror = () => {
                this.showNotification('无法加载图片信息', 'error');
            };

            img.src = url;

        } catch (error) {
            console.error('Save to media library error:', error);
            this.showNotification('保存到媒体库失败', 'error');
        }
    }

    /**
     * 保存视频到媒体库
     */
    async saveVideoToMediaLibrary(url, prompt = '', duration = '未知', resolution = '未知') {
        try {
            const mediaData = {
                name: `AI生成视频_${new Date().toLocaleString('zh-CN').replace(/[\/\s:]/g, '_')}`,
                type: 'video',
                category: 'ai_video',
                url: url,
                file_path: url, // 对于AI生成的视频，使用URL作为路径
                size: 0, // 无法直接获取文件大小
                duration: parseFloat(duration) || 0,
                metadata: {
                    source: 'ai_generation',
                    prompt: prompt,
                    model: 'MiniMax',
                    resolution: resolution,
                    generated_at: new Date().toISOString()
                }
            };

            const response = await fetch('/api/media-library/items', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(mediaData)
            });

            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    this.showNotification(`视频已保存到媒体库`, 'success');

                    // 通知媒体库更新
                    if (window.mediaLibrary) {
                        window.mediaLibrary.addMediaItem(mediaData);
                    }
                } else {
                    this.showNotification('保存到媒体库失败', 'error');
                }
            } else {
                this.showNotification('保存到媒体库失败', 'error');
            }

        } catch (error) {
            console.error('Save video to media library error:', error);
            this.showNotification('保存视频到媒体库失败', 'error');
        }
    }

    /**
     * 保存DaVinci静帧到媒体库
     */
    async saveDaVinciFrameToMediaLibrary(frameData, serviceType) {
        try {
            const mediaData = {
                name: `DaVinci静帧_${new Date().toLocaleString('zh-CN').replace(/[\/\s:]/g, '_')}`,
                type: 'image',
                category: 'davinci_frame',
                url: this.convertFilePathToUrl(frameData.file_path),
                file_path: frameData.file_path,
                size: 0, // 文件大小将由后端获取
                metadata: {
                    source: 'davinci_capture',
                    service_type: serviceType,
                    frame_number: frameData.frame_number,
                    timecode: frameData.timecode || '00:00:00:00',
                    captured_at: new Date().toISOString()
                }
            };

            const response = await fetch('/api/media-library/items', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(mediaData)
            });

            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    console.log('DaVinci frame saved to media library:', result.data);

                    // 通知媒体库更新
                    if (window.mediaLibrary) {
                        window.mediaLibrary.addMediaItem(mediaData);
                    }
                } else {
                    console.error('Failed to save DaVinci frame to media library:', result);
                }
            } else {
                console.error('Failed to save DaVinci frame to media library:', response.status);
            }

        } catch (error) {
            console.error('Save DaVinci frame to media library error:', error);
        }
    }

    /**
     * 处理结果
     */
    handleResult(data) {
        // 这里可以处理异步结果
    }



    /**
     * 显示成功通知
     */
    showSuccess(message, title = '成功') {
        return this.showNotification(message, 'success');
    }

    /**
     * 显示错误通知
     */
    showError(message, title = '错误') {
        return this.showNotification(message, 'error');
    }

    /**
     * 显示警告通知
     */
    showWarning(message, title = '警告') {
        return this.showNotification(message, 'warning');
    }

    /**
     * 显示信息通知
     */
    showInfo(message, title = '信息') {
        return this.showNotification(message, 'info');
    }

    /**
     * 显示模态框
     */
    showModal(id, options) {
        let modal = this.components.modals.get(id);

        if (!modal) {
            const modalElement = document.getElementById(id) || document.createElement('div');
            modalElement.id = id;

            modal = new ModalComponent(modalElement, {
                title: options.title || '',
                content: options.content || '',
                footer: options.footer || '',
                size: options.size || 'medium',
                closable: options.closable !== false,
                backdrop: options.backdrop !== false,
                keyboard: options.keyboard !== false
            });

            this.components.modals.set(id, modal);
        }

        modal.open();
        return modal;
    }

    /**
     * 关闭模态框
     */
    closeModal(id) {
        const modal = this.components.modals.get(id);
        if (modal) {
            modal.close();
        }
    }

    /**
     * 创建进度条
     */
    createProgressBar(element, options = {}) {
        const progressBar = new ProgressComponent(element, {
            value: options.value || 0,
            max: options.max || 100,
            showText: options.showText !== false,
            animated: options.animated !== false,
            striped: options.striped || false,
            color: options.color || 'primary'
        });

        if (options.id) {
            this.components.progressBars.set(options.id, progressBar);
        }

        return progressBar;
    }

    /**
     * 更新进度条
     */
    updateProgress(id, value) {
        const progressBar = this.components.progressBars.get(id);
        if (progressBar) {
            progressBar.setValue(value);
        }
    }

    /**
     * 显示加载状态
     */
    showLoading(message = '加载中...') {
        return this.showModal('loading-modal', {
            title: '请稍候',
            content: `
                <div class="text-center">
                    <div class="loading-spinner"></div>
                    <p class="mt-3">${message}</p>
                </div>
            `,
            closable: false,
            backdrop: false,
            size: 'small'
        });
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        this.closeModal('loading-modal');
    }

    /**
     * 提示词增强
     */
    async enhancePrompt() {
        const enhanceBtn = document.getElementById('enhance-prompt-btn');
        const originalPrompt = document.getElementById('enhancement-input').value.trim();
        const providerSelect = document.getElementById('enhancement-provider');
        const styleSelect = document.getElementById('enhancement-style');
        const resultContainer = document.getElementById('enhancement-result');

        if (!originalPrompt) {
            this.showNotification('请输入要增强的提示词', 'warning');
            return;
        }

        // 更新按钮状态
        enhanceBtn.disabled = true;
        enhanceBtn.querySelector('.btn-text').style.display = 'none';
        enhanceBtn.querySelector('.btn-loading').style.display = 'inline';

        try {
            // 使用统一AI服务管理器
            if (!window.unifiedAIServiceManager || !window.unifiedAIServiceManager.isInitialized) {
                throw new Error('统一AI服务管理器未就绪');
            }

            const result = await window.unifiedAIServiceManager.callService(
                'text_analysis',
                providerSelect.value || null,
                {
                    prompt: originalPrompt,
                    enhancement_style: styleSelect.value,
                    operation: 'enhance_prompt'
                }
            );

            if (result.success) {
                this.displayEnhancementResult(originalPrompt, result);
                resultContainer.style.display = 'block';
            } else {
                this.showNotification(`提示词增强失败: ${result.error}`, 'error');
            }

        } catch (error) {
            console.error('Prompt enhancement error:', error);
            this.showNotification('提示词增强请求失败，请检查网络连接', 'error');
        } finally {
            // 恢复按钮状态
            enhanceBtn.disabled = false;
            enhanceBtn.querySelector('.btn-text').style.display = 'inline';
            enhanceBtn.querySelector('.btn-loading').style.display = 'none';
        }
    }

    /**
     * 显示提示词增强结果
     */
    displayEnhancementResult(originalPrompt, result) {
        const originalContent = document.getElementById('original-prompt-content');
        const enhancedContent = document.getElementById('enhanced-prompt-content');

        // 显示原始提示词
        originalContent.textContent = originalPrompt;

        // 显示增强后的提示词
        if (result.success && result.data) {
            // 统一响应格式处理
            const enhancedPrompt = result.data.enhanced_prompt || result.data.content || result.data.text || '增强失败';

            if (enhancedPrompt && enhancedPrompt !== '增强失败') {
                enhancedContent.textContent = enhancedPrompt;

                // 存储增强后的提示词供其他功能使用
                this.lastEnhancedPrompt = enhancedPrompt;

                // 显示成功通知
                this.showNotification('✓ 提示词优化完成', 'success');
            } else {
                enhancedContent.innerHTML = '<div class="error">提示词增强结果为空</div>';
                this.showNotification('✕ 提示词优化失败', 'error');
            }
        } else {
            enhancedContent.innerHTML = '<div class="error">提示词增强失败，请重试</div>';
            this.showNotification(`✕ 提示词优化失败: ${result.error || '未知错误'}`, 'error');
        }
    }

    /**
     * 复制增强后的提示词
     */
    async copyEnhancedPrompt() {
        const enhancedContent = document.getElementById('enhanced-prompt-content');
        const text = enhancedContent.textContent;

        if (!text || text.includes('增强失败')) {
            this.showNotification('没有可复制的增强提示词', 'warning');
            return;
        }

        try {
            await navigator.clipboard.writeText(text);
            this.showNotification('增强提示词已复制到剪贴板', 'success');
        } catch (error) {
            console.error('Copy failed:', error);
            this.showNotification('复制失败，请手动选择文本复制', 'error');
        }
    }

    /**
     * 使用增强后的提示词
     */
    useEnhancedPrompt() {
        const enhancedContent = document.getElementById('enhanced-prompt-content');
        const enhancedPrompt = enhancedContent.textContent;

        if (!enhancedPrompt || enhancedPrompt.includes('增强失败')) {
            this.showNotification('没有可用的增强提示词', 'warning');
            return;
        }

        // 显示选择目标功能的模态框
        this.showModal('use-prompt-modal', {
            title: '选择目标功能',
            content: `
                <div class="use-prompt-options">
                    <p>将增强后的提示词应用到：</p>
                    <div class="option-buttons">
                        <button class="option-btn" onclick="app.applyPromptTo('text-generation')">文本生成</button>
                        <button class="option-btn" onclick="app.applyPromptTo('image-generation')">图像生成</button>
                        <button class="option-btn" onclick="app.applyPromptTo('video-generation')">视频生成</button>
                        <button class="option-btn" onclick="app.applyPromptTo('text-analysis')">文案分析</button>
                    </div>
                </div>
            `,
            actions: [
                {
                    text: '取消',
                    class: 'secondary-btn',
                    action: () => this.hideModal('use-prompt-modal')
                }
            ]
        });
    }

    /**
     * 将增强提示词应用到指定功能
     */
    applyPromptTo(targetTab) {
        const enhancedPrompt = this.lastEnhancedPrompt;
        if (!enhancedPrompt) {
            this.showNotification('没有可用的增强提示词', 'warning');
            return;
        }

        // 关闭模态框
        this.hideModal('use-prompt-modal');

        // 切换到目标标签页
        this.switchTab(targetTab);

        // 根据目标功能填充相应的输入框
        let targetInput;
        switch (targetTab) {
            case 'text-generation':
                targetInput = document.getElementById('generation-prompt');
                break;
            case 'image-generation':
                targetInput = document.getElementById('image-prompt');
                break;
            case 'video-generation':
                targetInput = document.getElementById('video-prompt');
                break;
            case 'text-analysis':
                targetInput = document.getElementById('analysis-text');
                break;
        }

        if (targetInput) {
            targetInput.value = enhancedPrompt;
            targetInput.focus();
            this.showNotification(`增强提示词已应用到${this.getTabDisplayName(targetTab)}`, 'success');
        }
    }

    /**
     * 获取标签页显示名称
     */
    getTabDisplayName(tabName) {
        const names = {
            'text-generation': '文本生成',
            'image-generation': '图像生成',
            'video-generation': '视频生成',
            'text-analysis': '文案分析'
        };
        return names[tabName] || tabName;
    }

    /**
     * 清空提示词增强
     */
    clearEnhancement() {
        document.getElementById('enhancement-input').value = '';
        document.getElementById('enhancement-result').style.display = 'none';
        document.getElementById('original-prompt-content').textContent = '';
        document.getElementById('enhanced-prompt-content').textContent = '';
        this.lastEnhancedPrompt = null;
    }
}

// 全局函数，供HTML调用
function switchTab(tabName) {
    if (window.app) {
        window.app.switchTab(tabName);
    }
}

// 提示词优化功能
class PromptEnhancer {
    constructor() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        // 为每个功能区域设置提示词优化按钮
        const enhanceButtons = [
            { id: 'enhance-generation-prompt-btn', targetType: 'text', inputId: 'generation-prompt', previewId: 'generation-prompt-preview', enhancedId: 'generation-prompt-enhanced', applyId: 'apply-generation-prompt-btn', cancelId: 'cancel-generation-prompt-btn' },
            { id: 'enhance-image-prompt-btn', targetType: 'image', inputId: 'image-prompt', previewId: 'image-prompt-preview', enhancedId: 'image-prompt-enhanced', applyId: 'apply-image-prompt-btn', cancelId: 'cancel-image-prompt-btn' },
            { id: 'enhance-video-prompt-btn', targetType: 'video', inputId: 'video-prompt', previewId: 'video-prompt-preview', enhancedId: 'video-prompt-enhanced', applyId: 'apply-video-prompt-btn', cancelId: 'cancel-video-prompt-btn' },
            { id: 'enhance-speech-text-btn', targetType: 'speech', inputId: 'speech-text', previewId: 'speech-text-preview', enhancedId: 'speech-text-enhanced', applyId: 'apply-speech-text-btn', cancelId: 'cancel-speech-text-btn' }
        ];

        enhanceButtons.forEach(config => {
            const button = document.getElementById(config.id);
            if (button) {
                button.addEventListener('click', () => this.enhancePrompt(config));
            }

            const applyButton = document.getElementById(config.applyId);
            if (applyButton) {
                applyButton.addEventListener('click', () => this.applyEnhancedPrompt(config));
            }

            const cancelButton = document.getElementById(config.cancelId);
            if (cancelButton) {
                cancelButton.addEventListener('click', () => this.cancelEnhancement(config));
            }
        });
    }

    async enhancePrompt(config) {
        const inputElement = document.getElementById(config.inputId);
        const button = document.getElementById(config.id);
        const previewElement = document.getElementById(config.previewId);
        const enhancedElement = document.getElementById(config.enhancedId);

        if (!inputElement || !button || !previewElement || !enhancedElement) {
            console.error('Required elements not found for prompt enhancement');
            return;
        }

        const originalPrompt = inputElement.value.trim();
        if (!originalPrompt) {
            window.app.showNotification('请先输入提示词内容', 'warning');
            return;
        }

        // 设置加载状态
        button.disabled = true;
        button.classList.add('loading');
        const enhanceText = button.querySelector('.enhance-text');
        const originalText = enhanceText.textContent;
        enhanceText.textContent = '优化中';

        try {
            const response = await fetch('/api/ai/enhance-prompt', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    prompt: originalPrompt,
                    target_type: config.targetType,
                    enhancement_style: 'detailed'
                })
            });

            const result = await response.json();

            if (result.success && result.data && result.data.content) {
                const enhancedPrompt = result.data.content;
                const originalLength = originalPrompt.length;
                const enhancedLength = enhancedPrompt.length;

                // 显示增强后的提示词
                enhancedElement.textContent = enhancedPrompt;
                previewElement.style.display = 'block';

                // 更新预览标题，显示字符数信息
                const previewTitle = previewElement.querySelector('.preview-title');
                if (previewTitle) {
                    previewTitle.textContent = `优化后的${this.getTypeDisplayName(config.targetType)}描述 (${originalLength} → ${enhancedLength} 字符)`;
                }

                // 滚动到预览区域
                previewElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });

                // 根据长度显示不同的提示
                if (enhancedLength > 1000) {
                    window.app.showNotification('提示词优化成功！注意：生成的提示词较长，可能需要根据具体模型调整。', 'success');
                } else {
                    window.app.showNotification('提示词优化成功！', 'success');
                }
            } else {
                window.app.showNotification(result.error || '提示词优化失败', 'error');
            }

        } catch (error) {
            console.error('Prompt enhancement error:', error);
            window.app.showNotification('提示词优化请求失败，请检查网络连接', 'error');
        } finally {
            // 恢复按钮状态
            button.disabled = false;
            button.classList.remove('loading');
            enhanceText.textContent = originalText;
        }
    }

    applyEnhancedPrompt(config) {
        const inputElement = document.getElementById(config.inputId);
        const enhancedElement = document.getElementById(config.enhancedId);
        const previewElement = document.getElementById(config.previewId);

        if (!inputElement || !enhancedElement || !previewElement) {
            return;
        }

        const enhancedPrompt = enhancedElement.textContent;
        if (enhancedPrompt) {
            inputElement.value = enhancedPrompt;
            previewElement.style.display = 'none';
            window.app.showNotification('已应用优化后的提示词', 'success');

            // 触发输入事件以更新其他相关UI
            inputElement.dispatchEvent(new Event('input', { bubbles: true }));
        }
    }

    cancelEnhancement(config) {
        const previewElement = document.getElementById(config.previewId);
        if (previewElement) {
            previewElement.style.display = 'none';
        }
    }

    getTypeDisplayName(targetType) {
        const typeNames = {
            'text': '文本',
            'image': '图像',
            'video': '视频',
            'speech': '语音'
        };
        return typeNames[targetType] || '提示词';
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.app = new DaVinciApp();

    // 延迟初始化PromptEnhancer，确保所有DOM元素都已加载
    setTimeout(() => {
        window.promptEnhancer = new PromptEnhancer();
    }, 1000);
});

// 全局函数，供HTML调用
function autoTranslateTextarea(textareaId) {
    if (window.app) {
        window.app.autoTranslateTextarea(textareaId);
    } else {
        console.error('App not initialized');
    }
}

// ==================== 达芬奇高级功能扩展 ====================

// 扩展DaVinciApp类的原型，添加新功能
Object.assign(DaVinciApp.prototype, {
    // 检查静帧捕获状态
    async checkFrameCaptureStatus() {
        const statusContainer = document.getElementById('frame-capture-status');
        if (!statusContainer) return;

        try {
            const response = await fetch('/api/davinci/frame/status');
            const result = await response.json();

            if (result.success && result.data) {
                const data = result.data;
                statusContainer.className = data.frame_export_available ? 'ready' : 'error';
                statusContainer.innerHTML = `
                    <div class="status-info">
                        <h4>${data.frame_export_available ? '✅ 静帧功能可用' : '❌ 静帧功能不可用'}</h4>
                        <p><strong>时间线状态:</strong> ${data.timeline_loaded ? '已加载' : '未加载'}</p>
                        <p><strong>导出格式:</strong> ${data.export_format || 'PNG'}</p>
                        <p><strong>导出质量:</strong> ${data.export_quality || 'high'}</p>
                    </div>
                `;

                // 更新播放头信息
                if (data.frame_export_available) {
                    this.updatePlayheadInfo({
                        current_frame: data.current_frame,
                        current_timecode: data.current_timecode,
                        timeline_duration: data.timeline_duration || 0
                    });
                }
            } else {
                statusContainer.className = 'error';
                statusContainer.innerHTML = `
                    <div class="error">
                        <h4>❌ 无法获取静帧状态</h4>
                        <p>${result.message || '未知错误'}</p>
                    </div>
                `;
            }
        } catch (error) {
            console.error('Frame capture status error:', error);
            statusContainer.className = 'error';
            statusContainer.innerHTML = `
                <div class="error">
                    <h4>❌ 网络错误</h4>
                    <p>${error.message || '无法连接到服务器'}</p>
                </div>
            `;
        }
    },

    // 刷新播放头信息
    async refreshPlayheadInfo() {
        try {
            const response = await fetch('/api/davinci/playhead/info');
            const result = await response.json();

            if (result.success && result.data) {
                this.updatePlayheadInfo(result.data);
            } else {
                this.showNotification('获取播放头信息失败: ' + result.message, 'error');
            }
        } catch (error) {
            console.error('Playhead info error:', error);
            this.showNotification('获取播放头信息时发生错误', 'error');
        }
    },

    // 更新播放头信息显示
    updatePlayheadInfo(data) {
        const playheadInfo = document.getElementById('playhead-info');
        if (!playheadInfo) return;

        const currentFrameEl = document.getElementById('current-frame');
        const currentTimecodeEl = document.getElementById('current-timecode');
        const timelineDurationEl = document.getElementById('timeline-duration');

        if (currentFrameEl) currentFrameEl.textContent = data.current_frame || '-';
        if (currentTimecodeEl) currentTimecodeEl.textContent = data.current_timecode || '-';
        if (timelineDurationEl) timelineDurationEl.textContent = data.timeline_duration || '-';

        playheadInfo.style.display = 'block';
    },

    // 捕获当前帧
    async captureCurrentFrame() {
        const formatSelect = document.getElementById('frame-export-format');
        const qualitySelect = document.getElementById('frame-export-quality');
        const outputDirInput = document.getElementById('frame-output-dir');

        try {
            const response = await fetch('/api/davinci/frame/export', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    format: formatSelect?.value || 'PNG',
                    quality: qualitySelect?.value || 'high',
                    output_dir: outputDirInput?.value || '',
                    filename: `frame_${Date.now()}.${(formatSelect?.value || 'PNG').toLowerCase()}`
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification(`静帧导出成功: ${result.data.file_path}`, 'success');
                // 刷新播放头信息
                this.refreshPlayheadInfo();
            } else {
                this.showNotification('静帧导出失败: ' + result.message, 'error');
            }
        } catch (error) {
            console.error('Frame capture error:', error);
            this.showNotification('静帧导出时发生错误', 'error');
        }
    },

    // 导出帧给AI使用
    async exportFrameForAI() {
        // 显示选择对话框让用户选择AI服务类型
        const serviceType = await this.showAIServiceSelectionDialog();
        if (!serviceType) return;

        try {
            const response = await fetch('/api/davinci/frame/export-for-ai', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    ai_service_type: serviceType
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification(`AI静帧导出成功: ${result.data.file_path}`, 'success');

                // 触发AI集成事件
                const event = new CustomEvent('aiFrameReady', {
                    detail: result.data
                });
                document.dispatchEvent(event);

            } else {
                this.showNotification('AI静帧导出失败: ' + result.message, 'error');
            }
        } catch (error) {
            console.error('AI frame export error:', error);
            this.showNotification('AI静帧导出时发生错误', 'error');
        }
    },

    /**
     * 显示AI服务选择对话框
     */
    async showAIServiceSelectionDialog() {
        return new Promise((resolve) => {
            // 创建模态框内容
            const modalContent = `
                <div class="ai-service-selection">
                    <p>请选择要使用静帧的AI服务：</p>
                    <div class="service-options">
                        <button class="service-option-btn" data-service="image_generation">
                            <span class="service-icon">🎨</span>
                            <div class="service-content">
                                <span class="service-name">图像生成</span>
                                <span class="service-desc">用作参考图片生成新图像</span>
                            </div>
                        </button>
                        <button class="service-option-btn" data-service="video_generation">
                            <span class="service-icon">🎥</span>
                            <div class="service-content">
                                <span class="service-name">视频生成</span>
                                <span class="service-desc">用作首帧生成视频</span>
                            </div>
                        </button>
                    </div>
                    <div class="modal-actions">
                        <button class="btn btn-secondary">取消</button>
                    </div>
                </div>
            `;

            // 创建模态框元素
            const modal = document.createElement('div');
            modal.className = 'modal ai-service-modal modal-small active';
            modal.innerHTML = `
                <div class="modal-backdrop active"></div>
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3 class="modal-title">选择AI服务类型</h3>
                            <button class="modal-close">&times;</button>
                        </div>
                        <div class="modal-body">
                            ${modalContent}
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // 为服务选项按钮添加事件监听器
            modal.querySelectorAll('.service-option-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    const serviceType = btn.dataset.service;
                    modal.remove();
                    resolve(serviceType);
                });
            });

            // 为关闭按钮添加事件监听器
            modal.querySelector('.modal-close').addEventListener('click', () => {
                modal.remove();
                resolve(null);
            });

            // 为取消按钮添加事件监听器
            modal.querySelector('.btn-secondary').addEventListener('click', () => {
                modal.remove();
                resolve(null);
            });

            // 点击背景关闭
            const backdrop = modal.querySelector('.modal-backdrop');
            backdrop.addEventListener('click', () => {
                modal.remove();
                resolve(null);
            });

            // 模态框已经是active状态，直接显示
        });
    },

    /**
     * 显示详细通知
     */
    showDetailedNotification(options) {
        const { title, message, details = [], type = 'info', duration = 4000 } = options;

        // 确保通知容器存在
        let container = document.querySelector('.notification-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'notification-container';
            document.body.appendChild(container);
        }

        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;

        // 获取图标
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };

        // 构建详细信息HTML
        const detailsHtml = details.length > 0 ? `
            <div class="notification-details" style="margin-top: 0.5rem; font-size: 0.85rem; color: var(--text-secondary);">
                ${details.map(detail => `<div style="margin: 0.25rem 0;">${detail}</div>`).join('')}
            </div>
        ` : '';

        notification.innerHTML = `
            <div class="notification-icon">${icons[type] || icons.info}</div>
            <div class="notification-content">
                <div class="notification-title" style="font-weight: 600; margin-bottom: 0.25rem;">${title}</div>
                <div class="notification-message">${message}</div>
                ${detailsHtml}
            </div>
            <button class="notification-close" onclick="this.parentElement.remove()">×</button>
        `;

        // 添加到容器
        container.appendChild(notification);

        // 显示动画
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // 自动隐藏
        setTimeout(() => {
            notification.classList.remove('show');
            notification.classList.add('hide');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, duration);
    },

    /**
     * 获取通知图标
     */
    getNotificationIcon(type) {
        const icons = {
            'success': '✅',
            'error': '❌',
            'warning': '⚠️',
            'info': 'ℹ️'
        };
        return icons[type] || icons.info;
    },

    // AI内容分析
    async analyzeContent() {
        const analysisType = document.getElementById('analysis-type')?.value || 'comprehensive';
        const confidenceThreshold = document.getElementById('confidence-threshold')?.value || 0.7;
        const autoApply = document.getElementById('auto-apply-analysis')?.checked || false;

        try {
            const response = await fetch('/api/davinci/deep-integration/analyze-content', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    analysis_type: analysisType,
                    confidence_threshold: parseFloat(confidenceThreshold),
                    auto_apply: autoApply
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification(`内容分析完成: ${analysisType}`, 'success');
                this.showAnalysisResults(result.data);
            } else {
                this.showNotification('内容分析失败: ' + result.message, 'error');
            }
        } catch (error) {
            console.error('Content analysis error:', error);
            this.showNotification('内容分析时发生错误', 'error');
        }
    },

    // 生成字幕
    async generateSubtitles() {
        const trackIndex = document.getElementById('subtitle-track')?.value || 1;
        const language = document.getElementById('subtitle-language')?.value || 'zh-CN';
        const aiService = document.getElementById('subtitle-ai-service')?.value || 'deepseek';

        try {
            const response = await fetch('/api/davinci/deep-integration/generate-subtitles', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    track_index: parseInt(trackIndex),
                    language: language,
                    ai_service: aiService
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification(`字幕生成成功，共${result.data.subtitles?.length || 0}条字幕`, 'success');
                this.showSubtitleResults(result.data);
            } else {
                this.showNotification('字幕生成失败: ' + result.message, 'error');
            }
        } catch (error) {
            console.error('Subtitle generation error:', error);
            this.showNotification('字幕生成时发生错误', 'error');
        }
    },

    // 提取音频
    async extractAudio() {
        const trackIndex = document.getElementById('audio-track-index')?.value || 1;

        try {
            const response = await fetch('/api/davinci/deep-integration/extract-audio', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    track_index: parseInt(trackIndex)
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification(`音频提取成功: ${result.data.audio_path}`, 'success');
            } else {
                this.showNotification('音频提取失败: ' + result.message, 'error');
            }
        } catch (error) {
            console.error('Audio extraction error:', error);
            this.showNotification('音频提取时发生错误', 'error');
        }
    },

    // 添加智能标记
    async addSmartMarkers() {
        const markerType = document.getElementById('marker-type')?.value || 'content';
        const confidence = document.getElementById('marker-confidence')?.value || 0.8;

        try {
            const response = await fetch('/api/davinci/deep-integration/analyze-content', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    analysis_type: 'smart_marking',
                    confidence_threshold: parseFloat(confidence),
                    auto_apply: true,
                    marker_type: markerType
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification(`智能标记添加成功，应用了${result.data.markers_applied || 0}个标记`, 'success');
                this.showAnalysisResults(result.data);
            } else {
                this.showNotification('智能标记添加失败: ' + result.message, 'error');
            }
        } catch (error) {
            console.error('Smart markers error:', error);
            this.showNotification('智能标记添加时发生错误', 'error');
        }
    },

    // 显示分析结果
    showAnalysisResults(data) {
        const modal = document.createElement('div');
        modal.className = 'analysis-results-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>内容分析结果</h3>
                    <button class="close-btn" onclick="this.closest('.analysis-results-modal').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="analysis-summary">
                        <h4>分析摘要</h4>
                        <p><strong>分析类型:</strong> ${data.analysis_type || '综合分析'}</p>
                        <p><strong>置信度阈值:</strong> ${data.confidence_threshold || 0.7}</p>
                        <p><strong>自动应用:</strong> ${data.auto_applied ? '是' : '否'}</p>
                    </div>
                    ${data.analysis ? `
                        <div class="analysis-details">
                            <h4>详细结果</h4>
                            <pre>${JSON.stringify(data.analysis, null, 2)}</pre>
                        </div>
                    ` : ''}
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary" onclick="this.closest('.analysis-results-modal').remove()">确定</button>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    },

    // 显示字幕结果
    showSubtitleResults(data) {
        const modal = document.createElement('div');
        modal.className = 'subtitle-results-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>字幕生成结果</h3>
                    <button class="close-btn" onclick="this.closest('.subtitle-results-modal').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="subtitle-summary">
                        <p><strong>生成数量:</strong> ${data.subtitles ? data.subtitles.length : 0} 条字幕</p>
                        <p><strong>语言:</strong> ${data.language || 'zh-CN'}</p>
                        <p><strong>音轨:</strong> ${data.track_index || 1}</p>
                    </div>
                    ${data.subtitles && data.subtitles.length > 0 ? `
                        <div class="subtitle-list">
                            <h4>字幕内容</h4>
                            <div class="subtitle-items">
                                ${data.subtitles.map(subtitle => `
                                    <div class="subtitle-item">
                                        <div class="subtitle-time">${subtitle.start_time}s - ${subtitle.end_time}s</div>
                                        <div class="subtitle-text">${subtitle.text}</div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="app.downloadSubtitles(${JSON.stringify(data).replace(/"/g, '&quot;')})">下载字幕</button>
                    <button class="btn btn-primary" onclick="this.closest('.subtitle-results-modal').remove()">确定</button>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    },

    // 下载字幕文件
    downloadSubtitles(data) {
        if (!data.subtitles || data.subtitles.length === 0) return;

        const srtContent = data.subtitles.map((subtitle, index) => {
            const startTime = this.formatSRTTime(subtitle.start_time);
            const endTime = this.formatSRTTime(subtitle.end_time);
            return `${index + 1}\n${startTime} --> ${endTime}\n${subtitle.text}\n`;
        }).join('\n');

        const blob = new Blob([srtContent], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'subtitles.srt';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    },

    // 格式化SRT时间
    formatSRTTime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        const ms = Math.floor((seconds % 1) * 1000);
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${ms.toString().padStart(3, '0')}`;
    },

    // AI标签页切换
    switchAITab(tabName) {
        // 移除所有活动状态
        document.querySelectorAll('.ai-tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelectorAll('.ai-tab-content').forEach(content => {
            content.classList.remove('active');
        });

        // 激活选中的标签页
        const activeBtn = document.querySelector(`[data-ai-tab="${tabName}"]`);
        const activeContent = document.getElementById(tabName);

        if (activeBtn) activeBtn.classList.add('active');
        if (activeContent) activeContent.classList.add('active');
    },

    // 初始化DaVinci高级功能
    initDaVinciAdvancedFeatures() {
        // 检查静帧捕获状态
        this.checkFrameCaptureStatus();

        // 初始化AI标签页
        this.switchAITab('content-analysis');

        // 设置静帧捕获事件监听器
        this.setupFrameCaptureListeners();
    },

    /**
     * 设置静帧捕获事件监听器
     */
    setupFrameCaptureListeners() {
        // 防止重复添加事件监听器
        if (this.eventListenersSetup.frameCaptureListeners) {
            return;
        }

        // 监听AI静帧准备就绪事件
        document.addEventListener('aiFrameReady', (event) => {
            this.handleAIFrameReady(event.detail);
        });

        // 监听静帧捕获完成事件
        document.addEventListener('frameCaptured', (event) => {
            this.handleFrameCaptured(event.detail);
        });

        // 监听AI服务集成事件
        document.addEventListener('frameReadyForAI', (event) => {
            this.handleFrameReadyForAI(event.detail);
        });

        // 监听音频提取完成事件
        document.addEventListener('audioExtracted', (event) => {
            this.handleAudioExtracted(event.detail);
        });

        // 标记事件监听器已设置
        this.eventListenersSetup.frameCaptureListeners = true;
    },

    /**
     * 处理AI静帧准备就绪事件
     */
    handleAIFrameReady(frameData) {
        console.log('AI Frame Ready:', frameData);

        const serviceNames = {
            'image_generation': '图像生成',
            'video_generation': '视频生成'
        };

        const serviceName = serviceNames[frameData.ai_service_type] || frameData.ai_service_type;

        // 执行集成操作
        if (frameData.ai_service_type === 'image_generation') {
            this.integrateFrameWithImageGeneration(frameData);
        } else if (frameData.ai_service_type === 'video_generation') {
            this.integrateFrameWithVideoGeneration(frameData);
        }

        // 自动保存到媒体库
        this.saveDaVinciFrameToMediaLibrary(frameData, frameData.ai_service_type);

        // 显示合并的成功通知
        this.showDetailedNotification({
            title: '🎬 静帧处理完成！',
            message: `已成功捕获静帧并自动设置为${serviceName}的参考图片`,
            details: [
                `📁 文件路径: ${frameData.file_path}`,
                `🎯 应用到: ${serviceName}`,
                `⏰ 时间码: ${frameData.timecode || '00:00:00:00'}`,
                `💾 已保存到媒体库`
            ],
            type: 'success',
            duration: 4000
        });
    },

    /**
     * 处理静帧捕获完成事件
     */
    handleFrameCaptured(frameData) {
        console.log('Frame Captured:', frameData);
        // 通知已在handleAIFrameReady中统一处理，这里不再重复显示
    },

    /**
     * 处理AI服务集成事件
     */
    handleFrameReadyForAI(frameData) {
        console.log('Frame Ready for AI:', frameData);

        if (frameData.serviceType === 'image_generation') {
            this.integrateFrameWithImageGeneration(frameData);
        } else if (frameData.serviceType === 'video_generation') {
            this.integrateFrameWithVideoGeneration(frameData);
        }
    },

    /**
     * 处理音频提取完成事件
     */
    handleAudioExtracted(audioData) {
        console.log('Audio Extracted:', audioData);

        // 将音频信息存储到全局状态
        if (!window.extractedAudio) {
            window.extractedAudio = [];
        }
        window.extractedAudio.push(audioData);

        // 显示音频可用于AI服务的通知
        this.showNotification('音频已提取完成，可用于语音合成和分析服务', 'success');

        // 可以在这里添加自动集成到语音相关服务的逻辑
        this.integrateAudioWithAIServices(audioData);
    },

    /**
     * 将音频集成到AI服务
     */
    integrateAudioWithAIServices(audioData) {
        // 这里可以添加将音频自动应用到语音合成或其他AI服务的逻辑
        // 例如：自动填充到语音合成的参考音频字段
        console.log('Audio available for AI services:', audioData);
    },

    /**
     * 将静帧集成到图像生成模块
     */
    integrateFrameWithImageGeneration(frameData) {
        // 切换到图像生成标签页
        this.switchTab('image-generation');

        // 创建文件对象并设置到参考图片上传区域
        const filePath = frameData.filePath || frameData.file_path;
        if (filePath) {
            this.setImageReferenceFromPath(filePath);
            // 通知已在handleAIFrameReady中统一处理，这里不再重复显示
        }
    },

    /**
     * 将静帧集成到视频生成模块
     */
    integrateFrameWithVideoGeneration(frameData) {
        // 切换到视频生成标签页
        this.switchTab('video-generation');

        // 设置首帧图片
        const filePath = frameData.filePath || frameData.file_path;
        if (filePath) {
            this.setVideoFirstFrameFromPath(filePath);
            // 通知已在handleAIFrameReady中统一处理，这里不再重复显示
        }
    },

    /**
     * 将绝对文件路径转换为web服务器URL
     */
    convertFilePathToUrl(filePath) {
        // 如果已经是相对URL，直接返回
        if (filePath.startsWith('/output/') || filePath.startsWith('http://') || filePath.startsWith('https://')) {
            return filePath;
        }

        // 将绝对路径转换为相对于output目录的路径
        // 例如: /path/to/project/output/ai_frames/image.png -> /output/ai_frames/image.png
        const outputIndex = filePath.indexOf('/output/');
        if (outputIndex !== -1) {
            return filePath.substring(outputIndex);
        }

        // 如果路径包含项目根目录，尝试提取output部分
        const projectRootPattern = /.*DaVinci AI Co-pilot Pro[\/\\](.*)$/;
        const match = filePath.match(projectRootPattern);
        if (match && match[1].startsWith('output')) {
            return '/' + match[1].replace(/\\/g, '/');
        }

        // 如果路径包含ai_frames目录，直接构建URL
        if (filePath.includes('ai_frames')) {
            const fileName = filePath.split(/[\/\\]/).pop();
            return `/output/ai_frames/${fileName}`;
        }

        // 默认情况下，假设是相对于output目录的路径
        const fileName = filePath.split(/[\/\\]/).pop();
        return `/output/${fileName}`;
    },

    /**
     * 从路径设置图像参考
     */
    setImageReferenceFromPath(filePath) {
        // 转换文件路径为URL
        const imageUrl = this.convertFilePathToUrl(filePath);

        // 保存到状态中
        const referenceImage = {
            id: Date.now(), // 使用时间戳作为唯一ID
            filePath: filePath,
            imageUrl: imageUrl,
            name: 'DaVinci静帧',
            type: 'davinci_frame'
        };

        // 清除现有的DaVinci静帧（避免重复）
        this.state.referenceImages = this.state.referenceImages.filter(img => img.type !== 'davinci_frame');

        // 添加新的参考图片
        this.state.referenceImages.push(referenceImage);

        // 更新UI显示
        this.updateImageReferencePreview();
    },

    /**
     * 更新图像参考预览UI
     */
    updateImageReferencePreview() {
        const previewContainer = document.getElementById('image-reference-preview');
        const previewGrid = document.getElementById('image-preview-grid');

        if (!previewContainer || !previewGrid) return;

        if (this.state.referenceImages.length === 0) {
            previewContainer.style.display = 'none';
            previewGrid.innerHTML = '';
            return;
        }

        // 生成预览HTML
        const previewHTML = this.state.referenceImages.map(img => `
            <div class="preview-item" data-image-id="${img.id}">
                <img src="${img.imageUrl}" alt="参考图片" style="max-width: 150px; max-height: 150px;"
                     onerror="this.style.display='none'; this.nextElementSibling.innerHTML='<span style=&quot;color: red;&quot;>图片加载失败</span>';">
                <div class="preview-info">
                    <span class="preview-name">${img.name}</span>
                    <button class="remove-btn" onclick="app.removeReferenceImage(${img.id})">×</button>
                </div>
            </div>
        `).join('');

        previewGrid.innerHTML = previewHTML;
        previewContainer.style.display = 'block';
    },

    /**
     * 移除参考图片
     */
    removeReferenceImage(imageId) {
        this.state.referenceImages = this.state.referenceImages.filter(img => img.id !== imageId);
        this.updateImageReferencePreview();
    },

    /**
     * 从路径设置视频首帧
     */
    setVideoFirstFrameFromPath(filePath) {
        // 转换文件路径为URL
        const imageUrl = this.convertFilePathToUrl(filePath);

        // 保存到状态中
        this.state.videoFirstFrame = {
            filePath: filePath,
            imageUrl: imageUrl,
            name: 'DaVinci静帧',
            type: 'davinci_frame'
        };

        // 更新UI显示
        this.updateVideoFirstFramePreview();
    },

    /**
     * 更新视频首帧预览UI
     */
    updateVideoFirstFramePreview() {
        const previewContainer = document.getElementById('video-first-frame-preview');
        const previewElement = document.getElementById('video-frame-preview');

        if (!previewContainer || !previewElement) return;

        if (!this.state.videoFirstFrame) {
            previewContainer.style.display = 'none';
            previewElement.innerHTML = '';
            return;
        }

        const frame = this.state.videoFirstFrame;
        previewElement.innerHTML = `
            <div class="preview-item">
                <img src="${frame.imageUrl}" alt="首帧图片" style="max-width: 200px; max-height: 200px;"
                     onerror="this.style.display='none'; this.nextElementSibling.innerHTML='<span style=&quot;color: red;&quot;>图片加载失败</span>';">
                <div class="preview-info">
                    <span class="preview-name">${frame.name}</span>
                    <button class="remove-btn" onclick="app.clearVideoFirstFrame()">×</button>
                </div>
            </div>
        `;
        previewContainer.style.display = 'block';
    },

    /**
     * 清除视频首帧
     */
    clearVideoFirstFrame() {
        this.state.videoFirstFrame = null;
        this.updateVideoFirstFramePreview();
    }
});

// 初始化媒体库
document.addEventListener('DOMContentLoaded', function () {
    // 初始化媒体库实例
    if (typeof MediaLibrary !== 'undefined') {
        window.mediaLibrary = new MediaLibrary();
    }

    // 初始化媒体库UI
    if (typeof MediaLibraryUI !== 'undefined') {
        window.mediaLibraryUI = new MediaLibraryUI(window.mediaLibrary);
    }
});

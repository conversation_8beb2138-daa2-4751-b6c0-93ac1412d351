/**
 * DaVinci AI Co-pilot PRO - 模块化组件系统
 * 现代化的Web组件架构
 */

class ComponentManager {
    constructor() {
        this.components = new Map();
        this.eventBus = new EventTarget();
        this.state = new Proxy({}, {
            set: (target, property, value) => {
                const oldValue = target[property];
                target[property] = value;
                this.eventBus.dispatchEvent(new CustomEvent('stateChange', {
                    detail: { property, value, oldValue }
                }));
                return true;
            }
        });
    }

    registerComponent(name, component) {
        this.components.set(name, component);
        console.log(`Component registered: ${name}`);
    }

    getComponent(name) {
        return this.components.get(name);
    }

    setState(updates) {
        Object.assign(this.state, updates);
    }

    getState(key) {
        return key ? this.state[key] : this.state;
    }

    on(event, handler) {
        this.eventBus.addEventListener(event, handler);
    }

    emit(event, data) {
        this.eventBus.dispatchEvent(new CustomEvent(event, { detail: data }));
    }
}

// 全局组件管理器
window.componentManager = new ComponentManager();

/**
 * 基础组件类
 */
class BaseComponent {
    constructor(element, options = {}) {
        this.element = typeof element === 'string' ? document.querySelector(element) : element;
        this.options = { ...this.defaultOptions, ...options };
        this.state = {};
        this.eventListeners = [];
        
        if (this.element) {
            this.init();
        }
    }

    get defaultOptions() {
        return {};
    }

    init() {
        this.render();
        this.bindEvents();
    }

    render() {
        // 子类实现
    }

    bindEvents() {
        // 子类实现
    }

    setState(newState) {
        this.state = { ...this.state, ...newState };
        this.render();
    }

    addEventListener(element, event, handler) {
        element.addEventListener(event, handler);
        this.eventListeners.push({ element, event, handler });
    }

    destroy() {
        this.eventListeners.forEach(({ element, event, handler }) => {
            element.removeEventListener(event, handler);
        });
        this.eventListeners = [];
    }

    show() {
        if (this.element) {
            this.element.style.display = 'block';
            this.element.classList.add('active');
        }
    }

    hide() {
        if (this.element) {
            this.element.style.display = 'none';
            this.element.classList.remove('active');
        }
    }

    toggle() {
        if (this.element) {
            if (this.element.style.display === 'none') {
                this.show();
            } else {
                this.hide();
            }
        }
    }
}

/**
 * 模态框组件
 */
class ModalComponent extends BaseComponent {
    get defaultOptions() {
        return {
            closable: true,
            backdrop: true,
            keyboard: true,
            size: 'medium' // small, medium, large, fullscreen
        };
    }

    render() {
        if (!this.element) return;

        this.element.className = `modal modal-${this.options.size}`;
        this.element.innerHTML = `
            <div class="modal-backdrop ${this.options.backdrop ? 'active' : ''}"></div>
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 class="modal-title">${this.options.title || ''}</h3>
                        ${this.options.closable ? '<button class="modal-close" aria-label="Close">&times;</button>' : ''}
                    </div>
                    <div class="modal-body">
                        ${this.options.content || ''}
                    </div>
                    <div class="modal-footer">
                        ${this.options.footer || ''}
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        if (this.options.closable) {
            const closeBtn = this.element.querySelector('.modal-close');
            if (closeBtn) {
                this.addEventListener(closeBtn, 'click', () => this.close());
            }
        }

        if (this.options.backdrop) {
            const backdrop = this.element.querySelector('.modal-backdrop');
            if (backdrop) {
                this.addEventListener(backdrop, 'click', () => this.close());
            }
        }

        if (this.options.keyboard) {
            this.addEventListener(document, 'keydown', (e) => {
                if (e.key === 'Escape' && this.isVisible()) {
                    this.close();
                }
            });
        }
    }

    open() {
        this.show();
        document.body.classList.add('modal-open');
        componentManager.emit('modalOpen', { modal: this });
    }

    close() {
        this.hide();
        document.body.classList.remove('modal-open');
        componentManager.emit('modalClose', { modal: this });
    }

    isVisible() {
        return this.element && this.element.classList.contains('active');
    }
}

/**
 * 通知组件
 */
class NotificationComponent extends BaseComponent {
    get defaultOptions() {
        return {
            type: 'info', // success, error, warning, info
            duration: 5000,
            closable: true,
            position: 'top-right' // top-left, top-right, bottom-left, bottom-right
        };
    }

    render() {
        if (!this.element) {
            this.createElement();
        }

        this.element.className = `notification notification-${this.options.type} notification-${this.options.position}`;
        this.element.innerHTML = `
            <div class="notification-icon">
                ${this.getIcon()}
            </div>
            <div class="notification-content">
                <div class="notification-title">${this.options.title || ''}</div>
                <div class="notification-message">${this.options.message || ''}</div>
            </div>
            ${this.options.closable ? '<button class="notification-close">&times;</button>' : ''}
        `;
    }

    createElement() {
        this.element = document.createElement('div');
        const container = this.getContainer();
        container.appendChild(this.element);
    }

    getContainer() {
        let container = document.querySelector('.notification-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'notification-container';
            document.body.appendChild(container);
        }
        return container;
    }

    getIcon() {
        const icons = {
            success: '✓',
            error: '✕',
            warning: '⚠',
            info: 'ℹ'
        };
        return icons[this.options.type] || icons.info;
    }

    bindEvents() {
        if (this.options.closable) {
            const closeBtn = this.element.querySelector('.notification-close');
            if (closeBtn) {
                this.addEventListener(closeBtn, 'click', () => this.close());
            }
        }

        if (this.options.duration > 0) {
            setTimeout(() => this.close(), this.options.duration);
        }
    }

    show() {
        super.show();
        // 添加动画
        requestAnimationFrame(() => {
            this.element.classList.add('notification-enter');
        });
    }

    close() {
        this.element.classList.add('notification-exit');
        setTimeout(() => {
            if (this.element && this.element.parentNode) {
                this.element.parentNode.removeChild(this.element);
            }
            this.destroy();
        }, 300);
    }
}

/**
 * 进度条组件
 */
class ProgressComponent extends BaseComponent {
    get defaultOptions() {
        return {
            value: 0,
            max: 100,
            showText: true,
            animated: true,
            striped: false,
            color: 'primary' // primary, success, warning, error
        };
    }

    render() {
        if (!this.element) return;

        const percentage = Math.round((this.state.value || this.options.value) / this.options.max * 100);
        
        this.element.className = `progress ${this.options.animated ? 'progress-animated' : ''} ${this.options.striped ? 'progress-striped' : ''}`;
        this.element.innerHTML = `
            <div class="progress-bar progress-bar-${this.options.color}" 
                 style="width: ${percentage}%" 
                 role="progressbar" 
                 aria-valuenow="${this.state.value || this.options.value}" 
                 aria-valuemin="0" 
                 aria-valuemax="${this.options.max}">
                ${this.options.showText ? `${percentage}%` : ''}
            </div>
        `;
    }

    setValue(value) {
        this.setState({ value: Math.max(0, Math.min(value, this.options.max)) });
    }

    increment(amount = 1) {
        this.setValue((this.state.value || this.options.value) + amount);
    }

    reset() {
        this.setValue(0);
    }

    complete() {
        this.setValue(this.options.max);
    }
}

/**
 * 标签页组件
 */
class TabsComponent extends BaseComponent {
    get defaultOptions() {
        return {
            activeTab: 0,
            orientation: 'horizontal' // horizontal, vertical
        };
    }

    init() {
        this.tabs = [];
        this.panels = [];
        this.collectTabsAndPanels();
        super.init();
    }

    collectTabsAndPanels() {
        if (!this.element) return;

        const tabList = this.element.querySelector('.tab-list');
        const tabPanels = this.element.querySelector('.tab-panels');

        if (tabList) {
            this.tabs = Array.from(tabList.querySelectorAll('.tab'));
        }

        if (tabPanels) {
            this.panels = Array.from(tabPanels.querySelectorAll('.tab-panel'));
        }
    }

    render() {
        this.updateActiveStates();
    }

    bindEvents() {
        this.tabs.forEach((tab, index) => {
            this.addEventListener(tab, 'click', (e) => {
                e.preventDefault();
                this.setActiveTab(index);
            });

            this.addEventListener(tab, 'keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.setActiveTab(index);
                }
            });
        });
    }

    setActiveTab(index) {
        if (index >= 0 && index < this.tabs.length) {
            this.setState({ activeTab: index });
            componentManager.emit('tabChange', { 
                activeTab: index, 
                tab: this.tabs[index],
                panel: this.panels[index]
            });
        }
    }

    updateActiveStates() {
        const activeIndex = this.state.activeTab !== undefined ? this.state.activeTab : this.options.activeTab;

        this.tabs.forEach((tab, index) => {
            tab.classList.toggle('active', index === activeIndex);
            tab.setAttribute('aria-selected', index === activeIndex);
        });

        this.panels.forEach((panel, index) => {
            panel.classList.toggle('active', index === activeIndex);
            panel.setAttribute('aria-hidden', index !== activeIndex);
        });
    }

    getActiveTab() {
        return this.state.activeTab !== undefined ? this.state.activeTab : this.options.activeTab;
    }

    next() {
        const current = this.getActiveTab();
        const next = (current + 1) % this.tabs.length;
        this.setActiveTab(next);
    }

    previous() {
        const current = this.getActiveTab();
        const prev = current === 0 ? this.tabs.length - 1 : current - 1;
        this.setActiveTab(prev);
    }
}

/**
 * 工具提示组件
 */
class TooltipComponent extends BaseComponent {
    get defaultOptions() {
        return {
            placement: 'top', // top, bottom, left, right
            trigger: 'hover', // hover, click, focus
            delay: 0,
            html: false
        };
    }

    render() {
        if (!this.tooltip) {
            this.createTooltip();
        }
        this.updateTooltipContent();
    }

    createTooltip() {
        this.tooltip = document.createElement('div');
        this.tooltip.className = 'tooltip';
        this.tooltip.innerHTML = `
            <div class="tooltip-arrow"></div>
            <div class="tooltip-content"></div>
        `;
        document.body.appendChild(this.tooltip);
    }

    updateTooltipContent() {
        const content = this.tooltip.querySelector('.tooltip-content');
        const text = this.element.getAttribute('data-tooltip') || this.options.content || '';
        
        if (this.options.html) {
            content.innerHTML = text;
        } else {
            content.textContent = text;
        }
    }

    bindEvents() {
        if (this.options.trigger === 'hover') {
            this.addEventListener(this.element, 'mouseenter', () => this.show());
            this.addEventListener(this.element, 'mouseleave', () => this.hide());
        } else if (this.options.trigger === 'click') {
            this.addEventListener(this.element, 'click', () => this.toggle());
        } else if (this.options.trigger === 'focus') {
            this.addEventListener(this.element, 'focus', () => this.show());
            this.addEventListener(this.element, 'blur', () => this.hide());
        }
    }

    show() {
        if (this.showTimeout) clearTimeout(this.showTimeout);
        
        this.showTimeout = setTimeout(() => {
            this.updatePosition();
            this.tooltip.classList.add('active');
        }, this.options.delay);
    }

    hide() {
        if (this.showTimeout) {
            clearTimeout(this.showTimeout);
            this.showTimeout = null;
        }
        this.tooltip.classList.remove('active');
    }

    updatePosition() {
        const rect = this.element.getBoundingClientRect();
        const tooltipRect = this.tooltip.getBoundingClientRect();
        
        let top, left;
        
        switch (this.options.placement) {
            case 'top':
                top = rect.top - tooltipRect.height - 8;
                left = rect.left + (rect.width - tooltipRect.width) / 2;
                break;
            case 'bottom':
                top = rect.bottom + 8;
                left = rect.left + (rect.width - tooltipRect.width) / 2;
                break;
            case 'left':
                top = rect.top + (rect.height - tooltipRect.height) / 2;
                left = rect.left - tooltipRect.width - 8;
                break;
            case 'right':
                top = rect.top + (rect.height - tooltipRect.height) / 2;
                left = rect.right + 8;
                break;
        }
        
        this.tooltip.style.top = `${top}px`;
        this.tooltip.style.left = `${left}px`;
        this.tooltip.setAttribute('data-placement', this.options.placement);
    }

    destroy() {
        super.destroy();
        if (this.tooltip && this.tooltip.parentNode) {
            this.tooltip.parentNode.removeChild(this.tooltip);
        }
    }
}

// 注册组件
componentManager.registerComponent('Modal', ModalComponent);
componentManager.registerComponent('Notification', NotificationComponent);
componentManager.registerComponent('Progress', ProgressComponent);
componentManager.registerComponent('Tabs', TabsComponent);
componentManager.registerComponent('Tooltip', TooltipComponent);

// 导出组件类
window.BaseComponent = BaseComponent;
window.ModalComponent = ModalComponent;
window.NotificationComponent = NotificationComponent;
window.ProgressComponent = ProgressComponent;
window.TabsComponent = TabsComponent;
window.TooltipComponent = TooltipComponent;

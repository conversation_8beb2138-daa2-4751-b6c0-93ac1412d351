#!/usr/bin/env python3
"""
遗留模块评估脚本
评估哪些旧模块可以安全移除，哪些需要保留
"""

import os
import re
import json
from pathlib import Path
from typing import Dict, List, Set

def analyze_file_dependencies(file_path: str) -> Dict[str, List[str]]:
    """分析文件的导入依赖"""
    dependencies = {
        'imports': [],
        'from_imports': [],
        'references': []
    }
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找import语句
        import_pattern = r'^import\s+([^\s#]+)'
        from_import_pattern = r'^from\s+([^\s]+)\s+import\s+([^#\n]+)'
        
        for line in content.split('\n'):
            line = line.strip()
            
            # 普通import
            import_match = re.match(import_pattern, line)
            if import_match:
                dependencies['imports'].append(import_match.group(1))
            
            # from import
            from_match = re.match(from_import_pattern, line)
            if from_match:
                module = from_match.group(1)
                items = from_match.group(2)
                dependencies['from_imports'].append(f"{module}: {items}")
        
        return dependencies
    except Exception as e:
        print(f"  ❌ 分析文件失败 {file_path}: {e}")
        return dependencies

def find_references_to_module(module_name: str, search_dirs: List[str]) -> List[str]:
    """查找对模块的引用"""
    references = []
    
    for search_dir in search_dirs:
        for root, dirs, files in os.walk(search_dir):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        
                        # 查找模块引用
                        patterns = [
                            rf'from\s+.*{re.escape(module_name)}',
                            rf'import\s+.*{re.escape(module_name)}',
                            rf'{re.escape(module_name)}\.',
                        ]
                        
                        for pattern in patterns:
                            if re.search(pattern, content):
                                references.append(file_path)
                                break
                    except:
                        continue
    
    return references

def evaluate_legacy_modules():
    """评估遗留模块"""
    print('🔍 遗留模块评估')
    print('=' * 60)
    
    # 定义遗留模块
    legacy_modules = {
        'src/services/mcp_result_parser.py': {
            'description': 'MCP结果解析器',
            'replacement': 'src/services/universal_result_parser.py',
            'status': 'unknown'
        },
        'src/services/deepseek.py': {
            'description': 'DeepSeek服务模块',
            'replacement': 'MCP服务适配器',
            'status': 'unknown'
        },
        'src/services/volcano.py': {
            'description': 'Volcano服务模块',
            'replacement': 'MCP服务适配器',
            'status': 'unknown'
        },
        'config/config.json': {
            'description': '旧配置文件',
            'replacement': 'config/unified_services.json',
            'status': 'unknown'
        }
    }
    
    search_dirs = ['src', 'web', 'config']
    
    print('\n📋 遗留模块分析:')
    
    for module_path, info in legacy_modules.items():
        print(f'\n🔧 {module_path}')
        print(f'   📝 描述: {info["description"]}')
        print(f'   🔄 替代: {info["replacement"]}')
        
        # 检查文件是否存在
        if os.path.exists(module_path):
            print('   ✅ 文件存在')
            
            # 分析依赖
            if module_path.endswith('.py'):
                deps = analyze_file_dependencies(module_path)
                if deps['imports'] or deps['from_imports']:
                    print('   📦 依赖:')
                    for imp in deps['imports'][:3]:  # 只显示前3个
                        print(f'     - import {imp}')
                    for imp in deps['from_imports'][:3]:  # 只显示前3个
                        print(f'     - from {imp}')
            
            # 查找引用
            module_name = os.path.basename(module_path).replace('.py', '')
            references = find_references_to_module(module_name, search_dirs)
            
            if references:
                print(f'   ⚠️ 发现 {len(references)} 个引用:')
                for ref in references[:5]:  # 只显示前5个
                    print(f'     - {ref}')
                if len(references) > 5:
                    print(f'     - ... 还有 {len(references) - 5} 个')
                info['status'] = 'in_use'
            else:
                print('   ✅ 未发现活跃引用')
                info['status'] = 'safe_to_remove'
        else:
            print('   ❌ 文件不存在')
            info['status'] = 'already_removed'
    
    # 特殊检查：查看新架构是否完全替代了旧功能
    print('\n🔄 新架构替代检查:')
    
    # 检查统一解析器是否完全替代了MCP解析器
    print('\n  📊 解析器替代检查:')
    try:
        # 检查是否还有对旧解析器的引用
        old_parser_refs = find_references_to_module('MCPResultParser', search_dirs)
        if old_parser_refs:
            print(f'    ⚠️ 发现 {len(old_parser_refs)} 个对旧解析器的引用')
            for ref in old_parser_refs[:3]:
                print(f'      - {ref}')
        else:
            print('    ✅ 旧解析器已完全被替代')
        
        # 检查新解析器的使用情况
        new_parser_refs = find_references_to_module('universal_result_parser', search_dirs)
        print(f'    📈 新解析器被 {len(new_parser_refs)} 个文件使用')
        
    except Exception as e:
        print(f'    ❌ 解析器检查失败: {e}')
    
    # 检查配置文件迁移状态
    print('\n  ⚙️ 配置文件迁移检查:')
    try:
        old_config_path = 'config/config.json'
        new_config_path = 'config/unified_services.json'
        
        if os.path.exists(old_config_path):
            with open(old_config_path, 'r', encoding='utf-8') as f:
                old_config = json.load(f)
            print(f'    📄 旧配置文件包含 {len(old_config)} 个顶级键')
        
        if os.path.exists(new_config_path):
            with open(new_config_path, 'r', encoding='utf-8') as f:
                new_config = json.load(f)
            services_count = len(new_config.get('services', {}))
            print(f'    📄 新配置文件包含 {services_count} 个服务')
            
            # 检查功能覆盖度
            if services_count >= 4:
                print('    ✅ 新配置文件功能覆盖充分')
            else:
                print('    ⚠️ 新配置文件功能覆盖可能不足')
        
    except Exception as e:
        print(f'    ❌ 配置文件检查失败: {e}')
    
    # 生成建议
    print('\n' + '=' * 60)
    print('💡 遗留模块处理建议:')
    
    safe_to_remove = []
    needs_evaluation = []
    in_use = []
    
    for module_path, info in legacy_modules.items():
        if info['status'] == 'safe_to_remove':
            safe_to_remove.append(module_path)
        elif info['status'] == 'in_use':
            in_use.append(module_path)
        else:
            needs_evaluation.append(module_path)
    
    if safe_to_remove:
        print(f'\n✅ 可以安全移除 ({len(safe_to_remove)} 个):')
        for module in safe_to_remove:
            print(f'  - {module}')
    
    if in_use:
        print(f'\n⚠️ 仍在使用中 ({len(in_use)} 个):')
        for module in in_use:
            print(f'  - {module}')
    
    if needs_evaluation:
        print(f'\n🔍 需要进一步评估 ({len(needs_evaluation)} 个):')
        for module in needs_evaluation:
            print(f'  - {module}')
    
    # 总体建议
    print(f'\n🎯 总体建议:')
    if len(safe_to_remove) > len(in_use):
        print('  ✅ 大部分遗留模块可以安全移除，架构迁移成功')
    elif len(in_use) > 0:
        print('  ⚠️ 仍有模块在使用中，需要逐步迁移')
    else:
        print('  🔍 需要更详细的分析来确定处理策略')

def main():
    evaluate_legacy_modules()

if __name__ == '__main__':
    main()

#!/usr/bin/env python3
"""
调试克隆声音问题的脚本
"""
import asyncio
import aiohttp
import json
from pathlib import Path

async def test_clone_workflow():
    """测试完整的克隆工作流程"""
    print("🔍 开始调试克隆声音问题...")
    
    async with aiohttp.ClientSession() as session:
        # 1. 测试克隆声音列表API
        print("\n1️⃣ 测试克隆声音列表...")
        try:
            response = await session.get("http://localhost:8000/api/speech/voices/clones")
            if response.status == 200:
                data = await response.json()
                print(f"✅ 克隆列表API正常: {data}")
                clone_count = len(data.get('data', {}).get('voices', []))
                print(f"📊 当前克隆声音数量: {clone_count}")
            else:
                print(f"❌ 克隆列表API失败: {response.status}")
        except Exception as e:
            print(f"❌ 克隆列表API异常: {e}")
        
        # 2. 测试MiniMax语音列表API
        print("\n2️⃣ 测试MiniMax语音列表...")
        try:
            response = await session.get("http://localhost:8000/api/speech/voices?provider=minimax")
            if response.status == 200:
                data = await response.json()
                print(f"✅ MiniMax语音列表API正常")
                voices = data.get('data', {}).get('voices', [])
                print(f"📊 MiniMax语音总数: {len(voices)}")
                
                # 查找可能的克隆声音
                clone_voices = []
                for voice in voices:
                    voice_id = voice.get('id', '')
                    voice_name = voice.get('name', '')
                    
                    # 检查是否是克隆声音的特征
                    if any(keyword in voice_id.lower() for keyword in ['zy', 'clone', 'custom']):
                        clone_voices.append({
                            'id': voice_id,
                            'name': voice_name,
                            'category': voice.get('category', 'unknown')
                        })
                
                if clone_voices:
                    print(f"🎯 找到可能的克隆声音 ({len(clone_voices)} 个):")
                    for voice in clone_voices:
                        print(f"  - {voice['id']}: {voice['name']} ({voice['category']})")
                else:
                    print("❌ 在MiniMax语音列表中没有找到克隆声音")
                    
            else:
                print(f"❌ MiniMax语音列表API失败: {response.status}")
        except Exception as e:
            print(f"❌ MiniMax语音列表API异常: {e}")
        
        # 3. 测试服务器健康状态
        print("\n3️⃣ 测试服务器健康状态...")
        try:
            response = await session.get("http://localhost:8000/health")
            if response.status == 200:
                data = await response.json()
                print(f"✅ 服务器健康状态正常")
                
                # 检查MCP服务器状态
                services = data.get('services', {})
                if 'minimax' in services:
                    minimax_status = services['minimax']
                    print(f"📊 MiniMax MCP状态: {minimax_status}")
                else:
                    print("❌ MiniMax MCP服务器未找到")
                    
            else:
                print(f"❌ 服务器健康检查失败: {response.status}")
        except Exception as e:
            print(f"❌ 服务器健康检查异常: {e}")
        
        # 4. 检查最近的API调用日志
        print("\n4️⃣ 检查最近的API调用...")
        try:
            # 检查是否有最近的克隆操作
            response = await session.get("http://localhost:8000/api/debug/recent-operations")
            if response.status == 200:
                data = await response.json()
                print(f"✅ 最近操作记录: {data}")
            else:
                print(f"⚠️ 调试API不可用: {response.status}")
        except Exception as e:
            print(f"⚠️ 调试API异常: {e}")

async def test_direct_minimax_mcp():
    """直接测试MiniMax MCP连接"""
    print("\n🔧 直接测试MiniMax MCP连接...")
    
    try:
        # 这里我们需要直接调用MCP管理器
        # 但由于我们在外部脚本中，需要通过API间接测试
        
        async with aiohttp.ClientSession() as session:
            # 尝试调用一个简单的MiniMax API
            test_data = {
                'provider': 'minimax',
                'action': 'list_voices'
            }
            
            response = await session.post(
                "http://localhost:8000/api/debug/test-mcp",
                json=test_data
            )
            
            if response.status == 200:
                data = await response.json()
                print(f"✅ MiniMax MCP连接测试成功: {data}")
            else:
                print(f"❌ MiniMax MCP连接测试失败: {response.status}")
                error_text = await response.text()
                print(f"📄 错误详情: {error_text}")
                
    except Exception as e:
        print(f"❌ MiniMax MCP连接测试异常: {e}")

async def main():
    """主函数"""
    print("🎯 克隆声音问题调试工具")
    print("=" * 50)
    
    await test_clone_workflow()
    await test_direct_minimax_mcp()
    
    print("\n📋 调试总结:")
    print("1. 检查克隆声音列表是否为空")
    print("2. 检查MiniMax语音列表中是否包含您的克隆声音")
    print("3. 检查MCP服务器连接状态")
    print("4. 如果以上都正常，问题可能在前端显示逻辑")

if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
测试语音合成API功能
"""

import asyncio
import aiohttp
import json
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_speech_synthesis_api():
    """测试语音合成API"""
    base_url = "http://127.0.0.1:8000"
    
    async with aiohttp.ClientSession() as session:
        # 1. 测试获取语音列表
        logger.info("🔍 测试获取语音列表...")
        try:
            async with session.get(f"{base_url}/api/speech/voices") as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('success'):
                        voices = data.get('data', {}).get('voices', [])
                        logger.info(f"✅ 成功获取 {len(voices)} 个语音选项")
                        for voice in voices[:3]:  # 显示前3个
                            logger.info(f"   - {voice.get('name')} ({voice.get('id')})")
                    else:
                        logger.error(f"❌ 获取语音列表失败: {data.get('error')}")
                else:
                    logger.error(f"❌ API请求失败，状态码: {response.status}")
        except Exception as e:
            logger.error(f"❌ 获取语音列表异常: {e}")
        
        # 2. 测试语音合成（使用Minimax，因为ElevenLabs MCP可能有问题）
        logger.info("\n🎤 测试语音合成...")
        try:
            synthesis_data = {
                "text": "Hello, this is a test of speech synthesis functionality.",
                "voice_id": "JBFqnCBsd6RMkjVDRZzb",
                "model_id": "eleven_multilingual_v2",
                "output_format": "mp3_44100_128",
                "provider": "minimax"  # 先用Minimax测试
            }
            
            async with session.post(
                f"{base_url}/api/speech/synthesize",
                json=synthesis_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('success'):
                        logger.info("✅ 语音合成成功")
                        if 'audio_url' in data.get('data', {}):
                            logger.info(f"   音频URL: {data['data']['audio_url']}")
                    else:
                        logger.error(f"❌ 语音合成失败: {data.get('error')}")
                else:
                    logger.error(f"❌ 语音合成API请求失败，状态码: {response.status}")
                    error_text = await response.text()
                    logger.error(f"   错误详情: {error_text}")
        except Exception as e:
            logger.error(f"❌ 语音合成异常: {e}")
        
        # 3. 测试批量语音合成
        logger.info("\n📦 测试批量语音合成...")
        try:
            batch_data = {
                "name": "API测试批量任务",
                "texts": [
                    "This is the first test sentence.",
                    "This is the second test sentence."
                ],
                "voice_config": {
                    "voice_id": "JBFqnCBsd6RMkjVDRZzb",
                    "model_id": "eleven_multilingual_v2",
                    "output_format": "mp3_44100_128",
                    "provider": "minimax"
                },
                "auto_start": True
            }
            
            async with session.post(
                f"{base_url}/api/speech/batch/create",
                json=batch_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('success'):
                        job_id = data.get('data', {}).get('job_id')
                        logger.info(f"✅ 批量任务创建成功，任务ID: {job_id}")
                        
                        # 检查任务状态
                        if job_id:
                            await asyncio.sleep(2)  # 等待一下
                            async with session.get(f"{base_url}/api/speech/batch/status/{job_id}") as status_response:
                                if status_response.status == 200:
                                    status_data = await status_response.json()
                                    if status_data.get('success'):
                                        status_info = status_data.get('data', {})
                                        logger.info(f"   任务状态: {status_info.get('status')}")
                                        logger.info(f"   进度: {status_info.get('completed_items')}/{status_info.get('total_items')}")
                    else:
                        logger.error(f"❌ 批量任务创建失败: {data.get('error')}")
                else:
                    logger.error(f"❌ 批量任务API请求失败，状态码: {response.status}")
        except Exception as e:
            logger.error(f"❌ 批量语音合成异常: {e}")
        
        # 4. 测试媒体库API
        logger.info("\n📚 测试媒体库API...")
        try:
            async with session.get(f"{base_url}/api/media-library/items?type_filter=audio") as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('success'):
                        audio_items = data.get('data', [])
                        logger.info(f"✅ 媒体库中有 {len(audio_items)} 个音频文件")
                        for item in audio_items[:3]:  # 显示前3个
                            logger.info(f"   - {item.get('name')} ({item.get('category')})")
                    else:
                        logger.error(f"❌ 获取媒体库失败: {data.get('error')}")
                else:
                    logger.error(f"❌ 媒体库API请求失败，状态码: {response.status}")
        except Exception as e:
            logger.error(f"❌ 媒体库API异常: {e}")

async def main():
    """主函数"""
    logger.info("🚀 开始测试语音合成API功能...")
    logger.info("="*60)
    
    await test_speech_synthesis_api()
    
    logger.info("\n" + "="*60)
    logger.info("📊 API测试完成")
    logger.info("💡 请在浏览器中访问 http://127.0.0.1:8000 测试完整功能")

if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
测试错误处理改进
"""
import asyncio
import aiohttp
import json

async def test_clone_voice_synthesis():
    """测试使用克隆声音进行语音合成（可能遇到余额不足）"""
    print("🎵 测试克隆声音语音合成（错误处理）")
    print("-" * 50)
    
    async with aiohttp.ClientSession() as session:
        try:
            # 使用之前创建的克隆声音ID
            synthesis_data = {
                "text": "这是使用克隆声音进行的语音合成测试。",
                "voice_id": "StorageTest_1753512943",  # 使用之前创建的克隆声音
                "provider": "minimax"
            }
            
            print("🔄 发送语音合成请求...")
            print(f"📝 使用克隆声音ID: {synthesis_data['voice_id']}")
            
            async with session.post(
                "http://localhost:8000/api/speech/synthesize",
                json=synthesis_data
            ) as response:
                print(f"📊 响应状态码: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    print(f"📄 响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                    
                    if data.get('success'):
                        print("🎉 语音合成成功！")
                        return True
                    else:
                        error_msg = data.get('error', 'Unknown error')
                        print(f"❌ 语音合成失败: {error_msg}")
                        
                        # 分析错误类型
                        if "余额不足" in error_msg or "insufficient balance" in error_msg:
                            print("💰 错误类型: 账户余额不足")
                            print("💡 解决方案: 请前往MiniMax平台充值")
                        elif "参数错误" in error_msg or "invalid params" in error_msg:
                            print("⚙️ 错误类型: API参数错误")
                            print("💡 解决方案: 检查语音ID是否有效")
                        else:
                            print("🔍 错误类型: 其他API错误")
                        
                        return False
                else:
                    error_text = await response.text()
                    print(f"❌ 请求失败: {response.status}")
                    print(f"📄 错误响应: {error_text}")
                    return False
                    
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False

async def test_regular_voice_synthesis():
    """测试使用常规语音进行语音合成"""
    print("\n🎤 测试常规语音合成（对比测试）")
    print("-" * 50)
    
    async with aiohttp.ClientSession() as session:
        try:
            # 使用MiniMax的预设语音
            synthesis_data = {
                "text": "这是使用预设语音进行的语音合成测试。",
                "voice_id": "male-qn-qingse",  # MiniMax预设语音
                "provider": "minimax"
            }
            
            print("🔄 发送语音合成请求...")
            print(f"📝 使用预设语音ID: {synthesis_data['voice_id']}")
            
            async with session.post(
                "http://localhost:8000/api/speech/synthesize",
                json=synthesis_data
            ) as response:
                print(f"📊 响应状态码: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    
                    if data.get('success'):
                        print("🎉 预设语音合成成功！")
                        return True
                    else:
                        error_msg = data.get('error', 'Unknown error')
                        print(f"❌ 预设语音合成失败: {error_msg}")
                        return False
                else:
                    error_text = await response.text()
                    print(f"❌ 请求失败: {response.status}")
                    print(f"📄 错误响应: {error_text}")
                    return False
                    
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False

async def main():
    """主测试函数"""
    print("🚀 测试错误处理改进")
    print("=" * 60)
    
    # 1. 测试克隆声音合成（可能遇到余额不足）
    clone_success = await test_clone_voice_synthesis()
    
    # 2. 测试预设语音合成（对比）
    regular_success = await test_regular_voice_synthesis()
    
    # 总结
    print("\n📋 测试总结:")
    print(f"  - 克隆声音合成: {'✅ 成功' if clone_success else '❌ 失败（可能是余额不足）'}")
    print(f"  - 预设语音合成: {'✅ 成功' if regular_success else '❌ 失败'}")
    
    if not clone_success and not regular_success:
        print("\n💰 如果两个测试都失败，很可能是账户余额不足")
        print("💡 解决方案:")
        print("  1. 前往 https://www.minimax.io/platform 充值")
        print("  2. 检查API密钥是否正确配置")
        print("  3. 确认API主机地址是否匹配")
    elif not clone_success and regular_success:
        print("\n🔍 克隆声音合成失败，但预设语音成功")
        print("💡 可能原因:")
        print("  1. 克隆声音ID无效或已过期")
        print("  2. 克隆声音需要额外的API权限")
    elif clone_success:
        print("\n🎉 所有测试成功！克隆声音功能正常工作")

if __name__ == "__main__":
    asyncio.run(main())

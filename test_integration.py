#!/usr/bin/env python3
"""
ElevenLabs集成功能快速测试脚本
用于验证集成是否正常工作
"""

import asyncio
import json
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_configuration():
    """检查配置文件"""
    logger.info("🔍 检查配置文件...")
    
    # 检查主配置文件
    config_file = Path('config/config.json')
    if not config_file.exists():
        logger.error("❌ 主配置文件不存在: config/config.json")
        return False
    
    try:
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        # 检查ElevenLabs配置
        if 'elevenlabs' not in config.get('ai_services', {}):
            logger.error("❌ 主配置文件中缺少ElevenLabs配置")
            return False
        
        elevenlabs_config = config['ai_services']['elevenlabs']
        if not elevenlabs_config.get('api_key'):
            logger.error("❌ ElevenLabs API密钥未配置")
            return False
        
        logger.info("✅ 主配置文件检查通过")
        
    except Exception as e:
        logger.error(f"❌ 读取主配置文件失败: {e}")
        return False
    
    # 检查MCP配置文件
    mcp_config_file = Path('config/mcp.json')
    if not mcp_config_file.exists():
        logger.error("❌ MCP配置文件不存在: config/mcp.json")
        return False
    
    try:
        with open(mcp_config_file, 'r') as f:
            mcp_config = json.load(f)
        
        if 'elevenlabs' not in mcp_config:
            logger.error("❌ MCP配置文件中缺少ElevenLabs配置")
            return False
        
        logger.info("✅ MCP配置文件检查通过")
        
    except Exception as e:
        logger.error(f"❌ 读取MCP配置文件失败: {e}")
        return False
    
    return True

def check_dependencies():
    """检查依赖项"""
    logger.info("🔍 检查依赖项...")
    
    required_modules = [
        'fastapi',
        'uvicorn',
        'pydantic',
        'pathlib',
        'asyncio'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        logger.error(f"❌ 缺少依赖模块: {missing_modules}")
        return False
    
    logger.info("✅ 依赖项检查通过")
    return True

def check_file_structure():
    """检查文件结构"""
    logger.info("🔍 检查文件结构...")
    
    required_files = [
        'src/main.py',
        'src/api/routes.py',
        'src/services/mcp_service.py',
        'src/services/batch_speech_service.py',
        'web/static/js/modules/speech-synthesis.js',
        'web/templates/index.html',
        'web/api/media_library.py'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        logger.error(f"❌ 缺少必要文件: {missing_files}")
        return False
    
    logger.info("✅ 文件结构检查通过")
    return True

def check_api_routes():
    """检查API路由"""
    logger.info("🔍 检查API路由...")
    
    routes_file = Path('src/api/routes.py')
    try:
        with open(routes_file, 'r') as f:
            routes_content = f.read()
        
        required_routes = [
            '/speech/synthesize',
            '/speech/voices',
            '/speech/batch/create',
            '/speech/batch/status',
            '/davinci/timeline/extract-text',
            '/davinci/audio/import',
            '/davinci/subtitles/generate-voiceover',
            '/voice/clone/upload-sample',
            '/voice/clone/create'
        ]
        
        missing_routes = []
        for route in required_routes:
            if route not in routes_content:
                missing_routes.append(route)
        
        if missing_routes:
            logger.error(f"❌ 缺少API路由: {missing_routes}")
            return False
        
        logger.info("✅ API路由检查通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查API路由失败: {e}")
        return False

def check_frontend_integration():
    """检查前端集成"""
    logger.info("🔍 检查前端集成...")
    
    # 检查JavaScript模块
    js_file = Path('web/static/js/modules/speech-synthesis.js')
    try:
        with open(js_file, 'r') as f:
            js_content = f.read()
        
        required_functions = [
            'synthesizeSpeech',
            'extractTimelineText',
            'generateSubtitleVoiceover',
            'importToDavinci'
        ]
        
        missing_functions = []
        for func in required_functions:
            if func not in js_content:
                missing_functions.append(func)
        
        if missing_functions:
            logger.error(f"❌ JavaScript模块缺少函数: {missing_functions}")
            return False
        
        logger.info("✅ JavaScript模块检查通过")
        
    except Exception as e:
        logger.error(f"❌ 检查JavaScript模块失败: {e}")
        return False
    
    # 检查HTML模板
    html_file = Path('web/templates/index.html')
    try:
        with open(html_file, 'r') as f:
            html_content = f.read()
        
        required_elements = [
            'speech-synthesis',
            'voice-selector',
            'speech-text',
            'synthesize-btn'
        ]
        
        missing_elements = []
        for element in required_elements:
            if element not in html_content:
                missing_elements.append(element)
        
        if missing_elements:
            logger.error(f"❌ HTML模板缺少元素: {missing_elements}")
            return False
        
        logger.info("✅ HTML模板检查通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查HTML模板失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始ElevenLabs集成功能检查...")
    logger.info("="*60)
    
    checks = [
        ("配置文件", check_configuration),
        ("依赖项", check_dependencies),
        ("文件结构", check_file_structure),
        ("API路由", check_api_routes),
        ("前端集成", check_frontend_integration)
    ]
    
    passed_checks = 0
    total_checks = len(checks)
    
    for check_name, check_func in checks:
        logger.info(f"\n📋 检查: {check_name}")
        try:
            if check_func():
                passed_checks += 1
            else:
                logger.error(f"❌ {check_name} 检查失败")
        except Exception as e:
            logger.error(f"❌ {check_name} 检查异常: {e}")
    
    logger.info("\n" + "="*60)
    logger.info("📊 检查结果汇总")
    logger.info("="*60)
    logger.info(f"总检查项: {total_checks}")
    logger.info(f"通过检查: {passed_checks}")
    logger.info(f"失败检查: {total_checks - passed_checks}")
    
    if passed_checks == total_checks:
        logger.info("🎉 所有检查通过！ElevenLabs集成准备就绪")
        logger.info("\n📝 下一步操作:")
        logger.info("1. 确保ElevenLabs API密钥有效")
        logger.info("2. 安装ElevenLabs MCP服务器: uvx elevenlabs-mcp")
        logger.info("3. 启动应用程序: python src/main.py")
        logger.info("4. 在浏览器中访问语音合成功能")
        return True
    else:
        logger.error("⚠️  部分检查未通过，请修复相关问题后重试")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

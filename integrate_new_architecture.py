#!/usr/bin/env python3
"""
新架构集成脚本
将统一服务架构集成到现有系统中
"""

import logging
import shutil
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def integrate_new_architecture():
    """集成新架构到现有系统"""
    logger.info("🚀 开始集成新架构...")
    
    try:
        # 1. 更新主路由文件
        update_main_routes()
        
        # 2. 更新HTML模板
        update_html_templates()
        
        # 3. 更新服务管理器
        update_service_manager()
        
        # 4. 创建配置文件备份
        backup_configs()
        
        # 5. 验证集成
        verify_integration()
        
        logger.info("✅ 新架构集成完成！")
        
    except Exception as e:
        logger.error(f"❌ 新架构集成失败: {e}")
        raise


def update_main_routes():
    """更新主路由文件"""
    logger.info("📝 更新主路由文件...")
    
    routes_file = Path("src/api/routes.py")
    if not routes_file.exists():
        logger.error("routes.py文件不存在")
        return
    
    # 读取现有内容
    with open(routes_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加动态路由导入
    import_addition = """
# 导入动态路由系统
from .dynamic_routes import dynamic_route_registry
from .auto_media_integration import auto_add_to_media_library
from ..services.universal_result_parser import universal_result_parser
"""
    
    # 在现有导入后添加新导入
    if "from .dynamic_routes import" not in content:
        # 找到最后一个import语句的位置
        lines = content.split('\n')
        import_end = 0
        for i, line in enumerate(lines):
            if line.strip().startswith('from ') or line.strip().startswith('import '):
                import_end = i
        
        lines.insert(import_end + 1, import_addition)
        content = '\n'.join(lines)
    
    # 添加动态路由注册
    router_addition = """
# 注册动态路由
dynamic_route_registry.register_all_routes()
router.include_router(dynamic_route_registry.get_router(), prefix="/api")
"""
    
    if "dynamic_route_registry.register_all_routes()" not in content:
        # 在router定义后添加
        content = content.replace(
            "router = APIRouter()",
            f"router = APIRouter()\n{router_addition}"
        )
    
    # 写回文件
    with open(routes_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    logger.info("✅ 主路由文件更新完成")


def update_html_templates():
    """更新HTML模板"""
    logger.info("📝 更新HTML模板...")
    
    html_file = Path("web/templates/index.html")
    if not html_file.exists():
        logger.error("index.html文件不存在")
        return
    
    # 读取现有内容
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加新的JavaScript模块
    js_addition = """    <script src="/static/js/modules/universal-service-integration.js"></script>"""
    
    if "universal-service-integration.js" not in content:
        # 在现有模块脚本后添加
        content = content.replace(
            '<script src="/static/js/modules/vidu-integration.js"></script>',
            f'<script src="/static/js/modules/vidu-integration.js"></script>\n{js_addition}'
        )
    
    # 写回文件
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    logger.info("✅ HTML模板更新完成")


def update_service_manager():
    """更新服务管理器"""
    logger.info("📝 更新服务管理器...")
    
    # 更新enhanced_ai_services.py以使用新的解析器
    service_file = Path("src/services/enhanced_ai_services.py")
    if service_file.exists():
        with open(service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 添加新解析器导入
        if "from .universal_result_parser import universal_result_parser" not in content:
            content = content.replace(
                "from .dynamic_service_provider import dynamic_provider_manager",
                """from .dynamic_service_provider import dynamic_provider_manager
from .universal_result_parser import universal_result_parser"""
            )
        
        with open(service_file, 'w', encoding='utf-8') as f:
            f.write(content)
    
    logger.info("✅ 服务管理器更新完成")


def backup_configs():
    """备份配置文件"""
    logger.info("💾 备份配置文件...")
    
    config_dir = Path("config")
    backup_dir = Path("config_backup")
    
    if backup_dir.exists():
        shutil.rmtree(backup_dir)
    
    shutil.copytree(config_dir, backup_dir)
    logger.info("✅ 配置文件备份完成")


def verify_integration():
    """验证集成"""
    logger.info("🔍 验证集成...")
    
    # 检查必要文件是否存在
    required_files = [
        "config/unified_services.json",
        "src/api/dynamic_routes.py",
        "web/static/js/modules/universal-service-integration.js",
        "src/services/universal_result_parser.py",
        "src/api/auto_media_integration.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        logger.error(f"❌ 缺少必要文件: {missing_files}")
        raise FileNotFoundError(f"缺少必要文件: {missing_files}")
    
    logger.info("✅ 集成验证通过")


def create_migration_guide():
    """创建迁移指南"""
    guide_content = """
# DaVinci AI Co-pilot Pro 新架构迁移指南

## 🎯 新架构特性

### 1. 统一服务配置
- 所有服务配置集中在 `config/unified_services.json`
- 支持服务元数据、UI配置、解析规则的统一管理
- 配置驱动的服务注册和发现

### 2. 动态路由系统
- 基于配置自动生成API路由
- 消除硬编码的服务提供商判断
- 支持运行时动态添加新服务

### 3. 通用前端组件
- 自动生成服务提供商选择器
- 基于配置的参数控制面板
- 统一的事件处理和参数管理

### 4. 统一结果解析
- 配置驱动的解析规则
- 支持多种数据提取模式
- 自动错误检测和处理

### 5. 自动媒体库集成
- 新生成的文件自动添加到媒体库
- 智能元数据提取和标签生成
- 支持缩略图自动生成

## 🚀 添加新服务的步骤

### 1. 更新统一配置
在 `config/unified_services.json` 中添加新服务配置：

```json
{
  "services": {
    "new_service": {
      "metadata": {
        "display_name": "新服务",
        "description": "服务描述",
        "icon": "🤖",
        "priority": 5
      },
      "capabilities": {
        "text_generation": {
          "enabled": true,
          "models": ["model-1", "model-2"]
        }
      },
      "mcp_config": {
        "transport": "stdio",
        "command": "uvx",
        "args": ["new-service-mcp"]
      },
      "tools": {
        "generate_text": {
          "capability": "text_generation",
          "parameters": {
            "prompt": {"type": "string", "required": true}
          }
        }
      },
      "ui_config": {
        "provider_selector": {
          "label": "🤖 新服务",
          "description": "服务描述"
        },
        "parameter_panels": {
          "text_generation": {
            "title": "新服务参数",
            "groups": [...]
          }
        }
      }
    }
  }
}
```

### 2. 重启服务
重启DaVinci AI Co-pilot Pro服务，新服务将自动：
- 注册到动态路由系统
- 在前端界面显示
- 支持参数控制
- 集成结果解析
- 自动媒体库集成

## 🔧 配置热重载

支持运行时重新加载配置：
```bash
curl -X POST http://127.0.0.1:8000/api/config/reload
```

## 📊 监控和调试

### 查看已注册路由
```bash
curl http://127.0.0.1:8000/api/routes/registered
```

### 查看服务能力
```bash
curl http://127.0.0.1:8000/api/services/capabilities
```

### 查看服务统计
```bash
curl http://127.0.0.1:8000/api/services/stats
```

## ⚠️ 注意事项

1. **向后兼容**：现有MiniMax和ElevenLabs服务功能完全保留
2. **配置验证**：修改配置前请备份原文件
3. **性能监控**：新架构包含完整的性能监控和错误处理
4. **扩展性**：支持无限制添加新的MCP服务

## 🆘 故障排除

### 服务未显示
1. 检查 `config/unified_services.json` 配置
2. 确认MCP服务器正确安装
3. 查看服务日志排查错误

### 参数面板未生成
1. 检查 `ui_config.parameter_panels` 配置
2. 确认前端JavaScript模块正确加载
3. 检查浏览器控制台错误

### 结果解析失败
1. 检查 `result_parsing` 配置
2. 测试正则表达式模式
3. 查看解析器日志

## 📞 技术支持

如遇问题，请查看：
1. 服务日志：`logs/` 目录
2. 配置备份：`config_backup/` 目录
3. 测试脚本：`test_new_architecture.py`
"""
    
    with open("MIGRATION_GUIDE.md", 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    logger.info("✅ 迁移指南创建完成")


if __name__ == "__main__":
    integrate_new_architecture()
    create_migration_guide()

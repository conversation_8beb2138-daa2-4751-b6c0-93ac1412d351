#!/usr/bin/env python3
"""
测试克隆声音存储和使用功能
"""
import asyncio
import aiohttp
import json
from pathlib import Path

async def test_clone_creation_and_storage():
    """测试克隆创建和存储"""
    print("🎯 测试克隆创建和本地存储")
    print("-" * 50)
    
    # 使用真实音频文件
    audio_file_path = "/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro/output/audio_exports/Untitled Project 1_track2_1753511956.wav"
    
    if not Path(audio_file_path).exists():
        print(f"❌ 音频文件不存在: {audio_file_path}")
        return None
    
    print(f"✅ 音频文件: {Path(audio_file_path).name}")
    
    async with aiohttp.ClientSession() as session:
        try:
            # 准备表单数据
            form_data = aiohttp.FormData()
            form_data.add_field('name', 'StorageTest')
            form_data.add_field('provider', 'minimax')
            form_data.add_field('audio_source', 'upload')
            
            # 添加音频文件
            with open(audio_file_path, 'rb') as f:
                form_data.add_field(
                    'audio_files', 
                    f, 
                    filename='storage_test.wav', 
                    content_type='audio/wav'
                )
                
                print("🔄 发送克隆创建请求...")
                
                async with session.post(
                    "http://localhost:8000/api/speech/voices/clone/create",
                    data=form_data
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        if data.get('success'):
                            data_obj = data.get('data', {})
                            voice_id = data_obj.get('voice_id')
                            
                            print("🎉 克隆创建成功！")
                            print(f"🎤 克隆声音ID: {voice_id}")
                            
                            return voice_id
                        else:
                            print(f"❌ 克隆创建失败: {data.get('error')}")
                            return None
                    else:
                        print(f"❌ 请求失败: {response.status}")
                        return None
                        
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return None

async def test_clone_list():
    """测试克隆声音列表"""
    print("\n🔍 测试克隆声音列表")
    print("-" * 50)
    
    async with aiohttp.ClientSession() as session:
        try:
            response = await session.get("http://localhost:8000/api/speech/voices/clones")
            if response.status == 200:
                data = await response.json()
                
                if data.get('success'):
                    voices = data.get('data', {}).get('voices', [])
                    print(f"✅ 找到 {len(voices)} 个克隆声音:")
                    
                    for voice in voices:
                        print(f"  - ID: {voice.get('id')}")
                        print(f"    名称: {voice.get('name')}")
                        print(f"    提供商: {voice.get('provider')}")
                        print(f"    类别: {voice.get('category')}")
                        print(f"    使用次数: {voice.get('usage_count', 0)}")
                        if voice.get('demo_url'):
                            print(f"    演示音频: {voice.get('demo_url')}")
                        print()
                    
                    return voices
                else:
                    print(f"❌ API返回错误: {data.get('error')}")
                    return []
            else:
                print(f"❌ 请求失败: {response.status}")
                return []
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return []

async def test_clone_voice_usage(voice_id: str):
    """测试使用克隆声音进行语音合成"""
    print(f"\n🎵 测试使用克隆声音: {voice_id}")
    print("-" * 50)
    
    async with aiohttp.ClientSession() as session:
        try:
            # 准备语音合成请求
            synthesis_data = {
                "text": "这是使用克隆声音进行的语音合成测试。",
                "voice_id": voice_id,
                "provider": "minimax"
            }
            
            print("🔄 发送语音合成请求...")
            
            async with session.post(
                "http://localhost:8000/api/speech/synthesize",
                json=synthesis_data
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    if data.get('success'):
                        print("🎉 语音合成成功！")
                        audio_url = data.get('data', {}).get('audio_url')
                        if audio_url:
                            print(f"🎵 音频文件: {audio_url}")
                        return True
                    else:
                        print(f"❌ 语音合成失败: {data.get('error')}")
                        return False
                else:
                    print(f"❌ 请求失败: {response.status}")
                    error_text = await response.text()
                    print(f"📄 错误详情: {error_text}")
                    return False
                    
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False

async def main():
    """主测试函数"""
    print("🚀 测试克隆声音存储和使用功能")
    print("=" * 60)
    
    # 1. 测试克隆创建
    voice_id = await test_clone_creation_and_storage()
    
    if voice_id:
        # 2. 测试克隆列表
        clone_voices = await test_clone_list()
        
        # 3. 测试使用克隆声音
        if clone_voices:
            # 使用第一个克隆声音进行测试
            test_voice_id = clone_voices[0].get('id')
            if test_voice_id:
                synthesis_success = await test_clone_voice_usage(test_voice_id)
            else:
                synthesis_success = False
        else:
            synthesis_success = False
        
        # 总结
        print("\n📋 测试总结:")
        print(f"  - 克隆创建: {'✅ 成功' if voice_id else '❌ 失败'}")
        print(f"  - 本地存储: {'✅ 成功' if clone_voices else '❌ 失败'}")
        print(f"  - 语音合成: {'✅ 成功' if synthesis_success else '❌ 失败'}")
        
        if voice_id and clone_voices:
            print("\n🎉 克隆声音存储和使用功能测试成功！")
            print("💡 现在您可以:")
            print("  1. 创建克隆声音并自动保存到本地")
            print("  2. 在克隆声音列表中查看您的声音")
            print("  3. 直接使用voice_id进行语音合成")
        else:
            print("\n⚠️ 部分功能存在问题，需要进一步调试")
    else:
        print("\n❌ 克隆创建失败，无法继续测试")

if __name__ == "__main__":
    asyncio.run(main())

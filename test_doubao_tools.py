#!/usr/bin/env python3
"""
测试豆包和DeepSeek MCP服务器的实际工具列表
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.services.fastmcp_server import fastmcp_manager

async def test_both_services():
    """测试豆包和DeepSeek MCP服务器的工具"""
    print("🔍 测试豆包和DeepSeek MCP服务器工具...")

    try:
        # 初始化MCP管理器
        initialized = await fastmcp_manager.initialize()
        if not initialized:
            print("❌ MCP管理器初始化失败")
            return

        print("✅ MCP管理器初始化成功")

        # 获取可用服务器
        available_servers = fastmcp_manager.get_available_servers()
        print(f"📊 可用服务器: {available_servers}")

        # 测试豆包服务器
        print("\n" + "="*50)
        print("🫘 测试豆包MCP服务器")
        print("="*50)

        if 'doubao' in available_servers:
            print("✅ 豆包服务器可用")

            # 获取豆包服务器的实际工具列表
            doubao_tools = await fastmcp_manager.get_tools_for_server('doubao')
            print(f"🔧 豆包服务器实际工具: {doubao_tools}")

            # 获取服务器配置
            doubao_config = fastmcp_manager.get_server_config('doubao')
            print(f"⚙️ 豆包服务器配置:")
            print(f"  - 连接状态: {doubao_config.get('is_connected', False)}")
            print(f"  - 配置中的工具: {doubao_config.get('tools', {})}")
            print(f"  - 能力: {doubao_config.get('capabilities', [])}")
            print(f"  - 最后错误: {doubao_config.get('last_error', 'None')}")

            # 测试连接健康状态
            doubao_health = await fastmcp_manager.check_connection_health('doubao')
            print(f"🏥 豆包连接健康状态: {doubao_health}")
        else:
            print("❌ 豆包服务器不可用")

        # 测试DeepSeek服务器
        print("\n" + "="*50)
        print("🧠 测试DeepSeek MCP服务器")
        print("="*50)

        if 'deepseek' in available_servers:
            print("✅ DeepSeek服务器可用")

            # 获取DeepSeek服务器的实际工具列表
            deepseek_tools = await fastmcp_manager.get_tools_for_server('deepseek')
            print(f"🔧 DeepSeek服务器实际工具: {deepseek_tools}")

            # 获取服务器配置
            deepseek_config = fastmcp_manager.get_server_config('deepseek')
            print(f"⚙️ DeepSeek服务器配置:")
            print(f"  - 连接状态: {deepseek_config.get('is_connected', False)}")
            print(f"  - 配置中的工具: {deepseek_config.get('tools', {})}")
            print(f"  - 能力: {deepseek_config.get('capabilities', [])}")
            print(f"  - 最后错误: {deepseek_config.get('last_error', 'None')}")

            # 测试连接健康状态
            deepseek_health = await fastmcp_manager.check_connection_health('deepseek')
            print(f"🏥 DeepSeek连接健康状态: {deepseek_health}")

            # 测试文本生成功能
            if 'chat_completion' in deepseek_tools:
                print("\n🧪 测试DeepSeek chat_completion工具...")
                try:
                    result = await fastmcp_manager.call_tool(
                        'deepseek',
                        'chat_completion',
                        {
                            'message': '你好，请简单介绍一下DeepSeek AI，使用V3模型。',
                            'model': 'deepseek-chat',
                            'max_tokens': 100
                        },
                        timeout=30
                    )
                    print(f"✅ DeepSeek chat_completion测试成功:")
                    print(f"   结果类型: {type(result)}")
                    if hasattr(result, 'content'):
                        for content in result.content:
                            if hasattr(content, 'text'):
                                print(f"   响应内容: {content.text[:200]}...")
                    else:
                        print(f"   响应: {str(result)[:200]}...")
                except Exception as e:
                    print(f"❌ DeepSeek chat_completion测试失败: {e}")
        else:
            print("❌ DeepSeek服务器不可用")

        print("\n" + "="*50)
        print("📊 测试总结")
        print("="*50)
        print(f"豆包服务器: {'✅ 正常' if 'doubao' in available_servers else '❌ 不可用'}")
        print(f"DeepSeek服务器: {'✅ 正常' if 'deepseek' in available_servers else '❌ 不可用'}")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_both_services())

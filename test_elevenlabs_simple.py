#!/usr/bin/env python3
"""
简化的ElevenLabs测试脚本
"""

import requests
import json

def test_voices():
    """测试获取语音列表"""
    print("🎤 测试获取ElevenLabs语音列表...")
    
    try:
        response = requests.get("http://127.0.0.1:8000/api/speech/voices")
        response.raise_for_status()
        
        data = response.json()
        if data.get('success') and data.get('data', {}).get('voices'):
            voices = data['data']['voices']
            print(f"✅ 成功获取 {len(voices)} 个语音选项")

            # 显示语音信息
            for i, voice in enumerate(voices[:3]):
                if isinstance(voice, dict):
                    print(f"  {i+1}. {voice.get('name', 'Unknown')} - {voice.get('id', 'No ID')}")
                else:
                    print(f"  {i+1}. {voice}")
        else:
            print(f"❌ 获取语音列表失败: {data.get('error', 'Unknown error')}")
            voices_success = False
        
        return True
    except Exception as e:
        print(f"❌ 获取语音列表失败: {e}")
        return False

def test_synthesis():
    """测试语音合成"""
    print("\n🎵 测试ElevenLabs语音合成...")
    
    test_data = {
        "text": "Hello, this is a test of ElevenLabs speech synthesis.",
        "voice": "Rachel",
        "provider": "elevenlabs"
    }
    
    try:
        response = requests.post(
            "http://127.0.0.1:8000/api/speech/synthesize",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        response.raise_for_status()
        
        result = response.json()
        print(f"✅ 语音合成成功!")
        print(f"   响应: {result}")
        
        return True
    except Exception as e:
        print(f"❌ 语音合成失败: {e}")
        if hasattr(e, 'response') and e.response:
            try:
                error_detail = e.response.json()
                print(f"   错误详情: {error_detail}")
            except:
                print(f"   响应内容: {e.response.text}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始ElevenLabs简化测试")
    print("=" * 40)
    
    # 测试语音列表
    voices_ok = test_voices()
    
    # 测试语音合成
    synthesis_ok = test_synthesis()
    
    # 总结
    print("\n" + "=" * 40)
    print("📊 测试结果:")
    print(f"   语音列表: {'✅ 成功' if voices_ok else '❌ 失败'}")
    print(f"   语音合成: {'✅ 成功' if synthesis_ok else '❌ 失败'}")
    
    if voices_ok and synthesis_ok:
        print("\n🎉 ElevenLabs集成测试成功!")
    else:
        print("\n⚠️  部分测试失败")

if __name__ == "__main__":
    main()

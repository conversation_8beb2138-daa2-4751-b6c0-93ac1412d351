# 🎬 DaVinci AI Co-pilot PRO

一个强大的AI驱动的DaVinci Resolve集成工具，提供智能视频制作辅助功能。

## 🌟 主要特性

### 🤖 AI核心功能
- **AI文本生成**: 基于DeepSeek的智能文本生成
- **文案分析**: 智能文案分析和优化建议
- **提示词增强**: AI驱动的提示词优化
- **语音合成**: 高质量的AI语音生成 (MiniMax)
- **视频生成**: AI视频内容生成 (火山引擎)
- **图像生成**: AI图像创作 (MiniMax)

### 🎬 DaVinci Resolve深度集成
- **📸 静帧捕获**: 高质量静帧导出，支持AI优化格式
- **🤖 AI内容分析**: 综合分析、场景检测、物体识别、情感分析
- **📝 智能字幕生成**: AI驱动的多语言字幕生成
- **🎵 音频提取**: 智能音频提取和处理
- **🏷️ 智能标记**: 自动添加内容标记、场景切换点、质量问题标记
- **🎬 渲染管理**: 智能渲染设置和状态监控
- **📁 项目管理**: 项目创建、加载、状态监控
- **⚡ 批量处理**: 多项目批量自动化处理

### 🌐 现代化界面
- **Web界面**: 响应式现代化Web用户界面
- **实时通信**: WebSocket实时状态更新
- **多标签页**: 功能模块化，操作直观
- **状态监控**: 实时连接状态和功能状态显示

## 🏗️ 项目架构

```
davinci-ai-copilot/
├── src/                    # 源代码目录
│   ├── core/              # 核心模块
│   │   ├── config.py      # 配置管理
│   │   ├── utils.py       # 工具函数
│   │   └── errors.py      # 错误处理
│   ├── services/          # AI服务模块
│   │   ├── ai_services.py # AI服务管理器
│   │   ├── deepseek.py    # DeepSeek服务
│   │   ├── minimax.py     # Minimax服务
│   │   └── volcano.py     # 火山引擎服务
│   ├── integrations/      # 集成模块
│   │   └── davinci_manager.py # DaVinci集成
│   ├── api/              # API路由
│   │   └── routes.py     # FastAPI路由
│   └── main.py           # 应用入口
├── web/                  # Web界面
│   ├── static/          # 静态资源
│   ├── templates/       # HTML模板
│   └── js/             # JavaScript文件
├── tests/               # 测试文件
├── config/             # 配置文件
│   ├── config.json     # 主配置文件
│   └── config.example.json # 配置示例
├── docs/               # 文档
├── requirements.txt    # Python依赖
└── README.md          # 项目说明
```

## 🚀 快速开始

### 📋 环境要求

- **Python**: 3.8+ (推荐 3.9-3.11)
- **DaVinci Resolve**: 18.x 或 19.x (必须支持Python API)
- **操作系统**: macOS, Windows, Linux
- **浏览器**: Chrome, Firefox, Safari, Edge (现代版本)

### ⚡ 一键部署和启动

#### 推荐方式: 自动部署脚本

```bash
# 1. 部署到DaVinci Resolve Scripts目录
python scripts/deploy_to_davinci.py

# 2. 一键启动 (最新版本)
chmod +x scripts/start_davinci_ai.sh
./scripts/start_davinci_ai.sh

# 或使用原版本
./scripts/quick_start.sh        # macOS/Linux
scripts\quick_start.bat         # Windows
```

#### 方法2: 手动安装

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd "DaVinci AI Co-pilot Pro"
   ```

2. **创建虚拟环境**
   ```bash
   python3 -m venv davinci_ai_env
   source davinci_ai_env/bin/activate  # macOS/Linux
   # 或
   davinci_ai_env\Scripts\activate     # Windows
   ```

3. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

4. **配置API密钥**
   ```bash
   cp config/config.example.json config/config.json
   # 编辑config.json，添加你的AI服务API密钥
   ```

5. **启动DaVinci Resolve**
   ```bash
   # 确保DaVinci Resolve正在运行
   ```

6. **启动AI Co-pilot Pro**
   ```bash
   python src/main.py
   ```

7. **访问Web界面**
   ```
   http://127.0.0.1:8000
   ```

### 🔧 连接测试

运行连接测试确保一切正常：

```bash
python scripts/test_connection.py
```

# 访问Web界面
# http://127.0.0.1:8000
```

## 📋 开发进度

### 🎯 MVP版本 (v0.1.0)
- [x] 项目架构设计
- [x] 配置管理系统
- [ ] DeepSeek服务集成
- [ ] 基础Web界面
- [ ] 文案分析功能

### 🚀 完整版本 (v1.0.0)
- [ ] Minimax服务集成
- [ ] 火山引擎服务集成
- [ ] DaVinci Resolve集成
- [ ] 批量处理功能
- [ ] 完整Web界面

### ⭐ 高级版本 (v2.0.0)
- [ ] 性能优化
- [ ] 工作流系统
- [ ] 模板管理
- [ ] 高级监控

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

- 项目链接: [https://github.com/your-username/davinci-ai-copilot](https://github.com/your-username/davinci-ai-copilot)
- 问题反馈: [Issues](https://github.com/your-username/davinci-ai-copilot/issues)

---

⭐ 如果这个项目对您有帮助，请给我们一个星标！

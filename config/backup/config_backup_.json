{"app": {"name": "DaVinci AI Co-pilot <PERSON><PERSON>", "version": "0.1.0", "debug": true, "host": "127.0.0.1", "port": 8000}, "ai_services": {"deepseek": {"api_key": "***********************************", "api_base": "https://api.deepseek.com/v1", "model": "deepseek-chat", "max_tokens": 4000, "temperature": 0.7, "timeout": 30, "max_retries": 3}, "minimax": {"api_key": "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "group_id": "1894672146249027958", "api_host": "https://api.minimax.chat", "comment": "使用全球版API Host，API密钥需要与Host地址匹配"}, "volcano": {"access_key": "9dd1697b-8636-4964-bfa8-2d5ebad73f06", "secret_key": "9dd1697b-8636-4964-bfa8-2d5ebad73f06", "region": "cn-north-1", "services": {"text": "doubao-text", "speech": "doubao-speech", "video": "doubao-video", "image": "doubao-image"}, "timeout": 60, "max_retries": 3}, "elevenlabs": {"api_key": "***************************************************", "api_base": "https://api.elevenlabs.io/v1", "timeout": 60, "max_retries": 3, "comment": "ElevenLabs API密钥，用于高质量语音合成。请在 https://elevenlabs.io/app/settings/api-keys 获取API密钥"}, "doubao": {"api_key": "your_doubao_api_key_here", "base_url": "https://ark.cn-beijing.volces.com/api/v3", "model": "ep-20241226140932-xxxxx", "timeout": 30, "max_retries": 3, "comment": "豆包AI服务配置，请在 https://console.volcengine.com/ark/ 获取API密钥和模型ID"}, "vidu": {"api_key": "your_vidu_api_key_here", "base_url": "https://api.vidu.cn/v1", "default_model": "vidu-1.0", "max_duration": 10, "default_resolution": "1080p", "timeout": 120, "max_retries": 2, "comment": "Vidu视频生成服务配置，请在 https://platform.vidu.cn/ 获取API密钥"}}, "davinci": {"resolve_path": "/Applications/DaVinci Resolve/DaVinci Resolve.app", "project_manager_timeout": 30, "media_pool_timeout": 30, "timeline_timeout": 30, "auto_save": true, "backup_projects": true}, "features": {"text_analysis": {"enabled": true, "default_provider": "deepseek", "scene_extraction": true, "keyword_analysis": true}, "speech_synthesis": {"enabled": true, "default_provider": "minimax", "voice_options": ["male", "female"], "audio_format": "wav", "sample_rate": 44100}, "video_generation": {"enabled": true, "default_provider": "volcano", "resolution": "1920x1080", "fps": 30, "duration_limit": 60}, "image_generation": {"enabled": true, "default_provider": "minimax", "resolution": "1024x1024", "aspect_ratios": ["1:1", "16:9", "9:16", "4:3"]}, "batch_processing": {"enabled": true, "max_concurrent_tasks": 3, "queue_size": 100, "auto_retry": true}}, "storage": {"temp_dir": "./temp", "output_dir": "./output", "cache_dir": "./cache", "max_file_size": "100MB", "cleanup_interval": 3600}, "logging": {"level": "INFO", "file": "./logs/app.log", "max_size": "10MB", "backup_count": 5, "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"}, "security": {"cors_origins": ["http://localhost:3000", "http://127.0.0.1:8000"], "api_rate_limit": "100/minute", "max_request_size": "50MB", "allowed_file_types": [".mp4", ".mov", ".avi", ".wav", ".mp3", ".txt", ".json"]}, "ui": {"theme": "dark", "language": "zh-CN", "auto_refresh": true, "refresh_interval": 5000, "show_debug_info": false}}
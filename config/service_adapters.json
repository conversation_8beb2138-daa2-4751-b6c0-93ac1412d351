{"adapters": {"elevenlabs": {"type": "mcp", "server_name": "elevenlabs", "capabilities": {"speech_synthesis": {"parameters": {"voice_id": {"type": "string", "description": "ElevenLabs语音ID", "required": true}, "model_id": {"type": "string", "description": "语音模型", "choices": ["eleven_multilingual_v2", "eleven_turbo_v2", "eleven_monolingual_v1"], "default": "eleven_multilingual_v2"}, "stability": {"type": "float", "description": "语音稳定性", "min_value": 0.0, "max_value": 1.0, "default": 0.5}, "similarity_boost": {"type": "float", "description": "相似度增强", "min_value": 0.0, "max_value": 1.0, "default": 0.5}, "style": {"type": "float", "description": "语音风格", "min_value": 0.0, "max_value": 1.0, "default": 0.0}, "output_format": {"type": "string", "description": "输出格式", "choices": ["mp3_44100_128", "mp3_44100_192", "pcm_16000", "pcm_22050", "pcm_24000", "pcm_44100"], "default": "mp3_44100_128"}}}, "voice_cloning": {"parameters": {"name": {"type": "string", "description": "克隆语音名称", "required": true, "min_length": 1, "max_length": 100}, "clone_type": {"type": "string", "description": "克隆类型", "choices": ["instant", "professional"], "default": "instant"}, "files": {"type": "array", "description": "音频文件列表", "required": true, "min_items": 1, "max_items": 25}}}}, "tool_mappings": {"speech_synthesis": "text_to_speech", "voice_cloning": "voice_clone"}, "content_parameter": "text", "parameter_mappings": {"speech_synthesis": {"voice_id": "voice_id", "model": "model_id", "format": "output_format", "stability": "stability", "similarity_boost": "similarity_boost", "style": "style", "use_speaker_boost": "use_speaker_boost"}, "voice_cloning": {"name": "name", "audio_files": "files", "description": "description", "clone_type": {"target": "clone_type", "transform": {"type": "map", "mapping": {"instant": "instant", "professional": "professional"}}}}}, "response_mappings": {"speech_synthesis": {"audio_url": {"source": "file_path", "transform": {"type": "format", "template": "/output/media_library/{}"}}, "duration": "duration", "file_size": "file_size", "format": "format"}, "voice_cloning": {"voice_id": "voice_id", "status": "status", "message": "message"}}}, "minimax": {"type": "mcp", "server_name": "minimax", "capabilities": {"speech_synthesis": {"parameters": {"voice_id": {"type": "string", "description": "MiniMax语音ID", "required": true, "pattern": "^[a-zA-Z][a-zA-Z0-9_]{2,254}[a-zA-Z0-9]$"}, "model": {"type": "string", "description": "语音模型", "choices": ["speech-01", "speech-02", "speech-02-hd"], "default": "speech-02-hd"}, "speed": {"type": "float", "description": "语速", "min_value": 0.5, "max_value": 2.0, "default": 1.0}, "vol": {"type": "float", "description": "音量", "min_value": 0.1, "max_value": 3.0, "default": 1.0}, "pitch": {"type": "int", "description": "音调", "min_value": -12, "max_value": 12, "default": 0}, "audio_sample_rate": {"type": "int", "description": "采样率", "choices": [16000, 22050, 24000, 32000, 44100, 48000], "default": 32000}, "format": {"type": "string", "description": "音频格式", "choices": ["mp3", "wav", "pcm", "flac"], "default": "mp3"}}}, "voice_cloning": {"parameters": {"voice_id": {"type": "string", "description": "语音ID", "required": true, "pattern": "^[a-zA-Z][a-zA-Z0-9_]{2,254}[a-zA-Z0-9]$", "min_length": 3, "max_length": 256}, "file": {"type": "string", "description": "音频文件路径", "required": true}, "text": {"type": "string", "description": "音频对应文本", "required": true, "min_length": 1, "max_length": 1000}}}, "image_generation": {"parameters": {"model": {"type": "string", "description": "图像生成模型", "choices": ["abab6.5-chat", "abab6.5s-chat"], "default": "abab6.5s-chat"}, "size": {"type": "string", "description": "图像尺寸", "choices": ["256x256", "512x512", "1024x1024", "1024x768", "768x1024"], "default": "1024x1024"}, "quality": {"type": "string", "description": "图像质量", "choices": ["standard", "hd"], "default": "standard"}}}}, "tool_mappings": {"speech_synthesis": "text_to_audio", "voice_cloning": "voice_clone", "image_generation": "text_to_image"}, "content_parameter": "text", "parameter_mappings": {"speech_synthesis": {"voice_id": "voice_id", "model": "model", "emotion": "emotion", "speed": "speed", "volume": "volume", "pitch": "pitch", "sample_rate": "sample_rate", "format": "format", "ssml": "ssml"}, "voice_cloning": {"voice_id": "voice_id", "audio_file": "file", "description": "text"}, "image_generation": {"model": "model", "aspect_ratio": "aspect_ratio", "guidance_scale": "guidance_scale"}}, "response_mappings": {"speech_synthesis": {"audio_url": "url", "duration": "duration", "format": "format"}, "voice_cloning": {"voice_id": "voice_id", "status": "status", "demo_url": "demo_url"}, "image_generation": {"image_url": "url", "width": "width", "height": "height"}}}, "deepseek": {"type": "mcp", "server_name": "deepseek", "capabilities": ["text_generation", "chat", "text_analysis", "translation"], "tool_mappings": {"text_generation": "chat_completion", "chat": "multi_turn_chat", "text_analysis": "chat_completion", "translation": "chat_completion"}, "content_parameter": "prompt", "parameter_mappings": {"text_generation": {"model": "model", "max_tokens": "max_tokens", "temperature": "temperature", "top_p": "top_p", "frequency_penalty": "frequency_penalty", "presence_penalty": "presence_penalty"}, "chat": {"model": "model", "messages": "messages", "max_tokens": "max_tokens", "temperature": "temperature"}, "text_analysis": {"model": "model", "task": "task", "max_tokens": "max_tokens"}, "translation": {"model": "model", "source_language": "source_language", "target_language": "target_language"}}, "response_mappings": {"text_generation": {"text": "text", "content": "text", "generated_text": "text"}, "chat": {"text": "text", "message": "text", "response": "text"}, "text_analysis": {"text": "text", "analysis": "text", "enhanced_prompt": "text", "content": "text", "result": "text"}, "translation": {"text": "text", "translated_text": "text", "content": "text"}}}, "doubao": {"type": "mcp", "server_name": "do<PERSON>o", "capabilities": ["image_generation", "video_generation"], "tool_mappings": {"image_generation": "text_to_image", "video_generation": "text_to_video"}, "content_parameter": "prompt", "parameter_mappings": {"image_generation": {"model": "model", "size": "size", "quality": "quality", "style": "style"}, "video_generation": {"model": "model", "duration": "duration", "fps": "fps", "resolution": "resolution"}}, "response_mappings": {"image_generation": {"image_url": "url", "width": "width", "height": "height", "format": "format"}, "video_generation": {"video_url": "url", "duration": "duration", "fps": "fps", "resolution": "resolution"}}}, "vidu": {"type": "mcp", "server_name": "vidu", "capabilities": ["video_generation"], "tool_mappings": {"video_generation": "text_to_video"}, "content_parameter": "prompt", "parameter_mappings": {"video_generation": {"model": "model", "duration": "duration", "aspect_ratio": "aspect_ratio", "fps": "fps"}}, "response_mappings": {"video_generation": {"video_url": "url", "duration": "duration", "width": "width", "height": "height"}}}}, "capability_definitions": {"speech_synthesis": {"display_name": "语音合成", "description": "将文本转换为语音", "category": "audio", "parameters": {"voice_id": {"type": "string", "description": "语音ID", "required": true}, "model": {"type": "string", "description": "语音模型", "default": "default"}, "format": {"type": "string", "description": "音频格式", "choices": ["mp3", "wav", "ogg"], "default": "mp3"}, "speed": {"type": "float", "description": "语速", "min_value": 0.5, "max_value": 2.0, "default": 1.0}, "volume": {"type": "float", "description": "音量", "min_value": 0.0, "max_value": 1.0, "default": 1.0}}}, "voice_cloning": {"display_name": "语音克隆", "description": "基于样本音频创建克隆语音", "category": "audio", "parameters": {"name": {"type": "string", "description": "克隆语音名称", "required": true}, "audio_files": {"type": "array", "description": "音频样本文件", "required": true}, "description": {"type": "string", "description": "语音描述"}, "clone_type": {"type": "string", "description": "克隆类型", "choices": ["instant", "professional"], "default": "instant"}}}, "text_generation": {"display_name": "文本生成", "description": "基于提示词生成文本", "category": "text", "parameters": {"model": {"type": "string", "description": "文本模型", "default": "default"}, "max_tokens": {"type": "integer", "description": "最大令牌数", "min_value": 1, "max_value": 4000, "default": 1000}, "temperature": {"type": "float", "description": "创造性温度", "min_value": 0.0, "max_value": 2.0, "default": 0.7}}}, "image_generation": {"display_name": "图像生成", "description": "基于提示词生成图像", "category": "visual", "parameters": {"model": {"type": "string", "description": "图像模型", "default": "default"}, "size": {"type": "string", "description": "图像尺寸", "choices": ["512x512", "1024x1024", "1024x768", "768x1024"], "default": "1024x1024"}, "quality": {"type": "string", "description": "图像质量", "choices": ["standard", "hd"], "default": "standard"}}}, "video_generation": {"display_name": "视频生成", "description": "基于提示词生成视频", "category": "visual", "parameters": {"model": {"type": "string", "description": "视频模型", "default": "default"}, "duration": {"type": "integer", "description": "视频时长（秒）", "min_value": 1, "max_value": 30, "default": 5}, "fps": {"type": "integer", "description": "帧率", "choices": [24, 30, 60], "default": 30}}}}}
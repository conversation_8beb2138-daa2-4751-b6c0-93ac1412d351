{"version": "2.0", "mcpServers": {"minimax": {"provider": "minimax", "transport": "stdio", "command": "uvx", "args": ["minimax-mcp"], "env": {"MINIMAX_API_KEY": "${MINIMAX_API_KEY}", "MINIMAX_GROUP_ID": "${MINIMAX_GROUP_ID}", "MINIMAX_API_HOST": "${MINIMAX_API_HOST}", "MINIMAX_MCP_BASE_PATH": "${OUTPUT_DIR}", "MINIMAX_API_RESOURCE_MODE": "url"}, "capabilities": ["speech_synthesis", "image_generation", "video_generation"], "tools": {"text_to_audio": "speech_synthesis", "text_to_image": "image_generation", "generate_video": "video_generation", "list_voices": "utility", "voice_clone": "speech_synthesis", "music_generation": "audio_generation", "voice_design": "speech_synthesis"}, "enabled": true, "timeout": 60, "retry_count": 3, "health_check_interval": 300, "metadata": {"display_name": "MiniMax AI", "description": "MiniMax多模态AI服务", "version": "1.0.0", "tags": ["ai", "multimodal", "chinese"]}}, "elevenlabs": {"provider": "elevenlabs", "transport": "stdio", "command": "uvx", "args": ["elevenlabs-mcp"], "env": {"ELEVENLABS_API_KEY": "${ELEVENLABS_API_KEY}", "ELEVENLABS_MCP_BASE_PATH": "${OUTPUT_DIR}"}, "capabilities": ["speech_synthesis", "voice_cloning", "speech_to_text", "voice_conversion"], "tools": {"text_to_speech": "speech_synthesis", "voice_clone": "speech_synthesis", "speech_to_text": "text_analysis", "voice_conversion": "speech_synthesis", "list_voices": "utility", "search_voices": "utility", "search_voice_library": "utility"}, "enabled": true, "timeout": 60, "retry_count": 3, "health_check_interval": 300, "metadata": {"display_name": "ElevenLabs", "description": "ElevenLabs语音合成和克隆服务", "version": "1.0.0", "tags": ["voice", "tts", "cloning"]}}, "doubao": {"provider": "do<PERSON>o", "transport": "stdio", "command": "uvx", "args": ["doubao-mcp-server"], "env": {"DOUBAO_API_KEY": "${DOUBAO_API_KEY}", "DOUBAO_BASE_URL": "${DOUBAO_BASE_URL}", "DOUBAO_MODEL": "${DOUBAO_MODEL}"}, "capabilities": ["text_generation", "chat", "text_analysis"], "tools": {"chat": "text_generation", "complete": "text_generation", "analyze": "text_analysis"}, "enabled": true, "timeout": 30, "retry_count": 3, "health_check_interval": 300, "metadata": {"display_name": "豆包", "description": "字节跳动豆包AI对话服务", "version": "1.0.0", "homepage": "https://github.com/wwwzhouhui/doubao_mcp_server", "tags": ["text", "chat", "chinese"]}}, "vidu": {"provider": "vidu", "transport": "stdio", "command": "uvx", "args": ["vidu-mcp"], "env": {"VIDU_API_KEY": "${VIDU_API_KEY}", "VIDU_BASE_URL": "${VIDU_BASE_URL}", "VIDU_OUTPUT_DIR": "${OUTPUT_DIR}"}, "capabilities": ["video_generation"], "tools": {"generate_video": "video_generation", "text_to_video": "video_generation", "image_to_video": "video_generation", "get_video_status": "utility"}, "enabled": true, "timeout": 120, "retry_count": 2, "health_check_interval": 600, "metadata": {"display_name": "Vidu", "description": "Vidu高质量视频生成服务", "version": "1.0.0", "homepage": "https://platform.vidu.cn/docs/mcp-stdio", "tags": ["video", "generation", "creative"]}}}, "global_settings": {"default_timeout": 30, "max_concurrent_connections": 5, "output_directory": "./output", "log_level": "INFO", "auto_discovery": true, "health_check_enabled": true, "circuit_breaker": {"enabled": true, "failure_threshold": 5, "recovery_timeout": 300}, "load_balancer": {"strategy": "smart", "weights": {}}, "environment_variables": {"MINIMAX_API_KEY": "ai_services.minimax.api_key", "MINIMAX_GROUP_ID": "ai_services.minimax.group_id", "MINIMAX_API_HOST": "ai_services.minimax.api_host", "ELEVENLABS_API_KEY": "ai_services.elevenlabs.api_key", "DOUBAO_API_KEY": "ai_services.doubao.api_key", "DOUBAO_BASE_URL": "ai_services.doubao.base_url", "DOUBAO_MODEL": "ai_services.doubao.model", "VIDU_API_KEY": "ai_services.vidu.api_key", "VIDU_BASE_URL": "ai_services.vidu.base_url", "OUTPUT_DIR": "storage.output_dir"}}, "service_discovery": {"enabled": true, "cache_ttl": 300, "auto_refresh": true, "capability_probing": true}, "compatibility_matrix": {"speech_synthesis": {"input_formats": ["text", "ssml"], "output_formats": ["mp3", "wav", "ogg"], "chaining": {"can_follow": ["text_generation", "text_analysis"], "can_precede": ["media_library"]}}, "video_generation": {"input_formats": ["text", "image"], "output_formats": ["mp4", "webm"], "chaining": {"can_follow": ["image_generation", "text_generation"], "can_precede": ["media_library"]}}}}
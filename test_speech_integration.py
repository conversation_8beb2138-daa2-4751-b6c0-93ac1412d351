#!/usr/bin/env python3
"""
测试语音合成集成功能
"""

import asyncio
import aiohttp
import json
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_speech_integration():
    """测试语音合成集成功能"""
    base_url = "http://127.0.0.1:8000"
    
    async with aiohttp.ClientSession() as session:
        # 1. 测试获取语音列表
        logger.info("🎤 测试获取语音列表...")
        try:
            async with session.get(f"{base_url}/api/speech/voices") as response:
                logger.info(f"响应状态码: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"成功获取语音列表")
                    
                    if data.get('success'):
                        voices = data.get('data', {}).get('voices', [])
                        logger.info(f"✅ 获取到 {len(voices)} 个语音")
                        
                        # 按提供商分组显示
                        elevenlabs_voices = [v for v in voices if v.get('provider') == 'elevenlabs']
                        minimax_voices = [v for v in voices if v.get('provider') == 'minimax']
                        
                        logger.info(f"   ElevenLabs语音: {len(elevenlabs_voices)} 个")
                        for voice in elevenlabs_voices[:3]:  # 显示前3个
                            logger.info(f"     - {voice.get('name')} ({voice.get('id')})")
                        
                        logger.info(f"   Minimax语音: {len(minimax_voices)} 个")
                        for voice in minimax_voices[:3]:  # 显示前3个
                            logger.info(f"     - {voice.get('name')} ({voice.get('id')})")
                    else:
                        logger.error(f"❌ 获取语音列表失败: {data.get('error')}")
                else:
                    error_text = await response.text()
                    logger.error(f"❌ API请求失败，状态码: {response.status}")
                    logger.error(f"   错误详情: {error_text}")
        except Exception as e:
            logger.error(f"❌ 获取语音列表时发生异常: {e}")
        
        # 2. 测试Minimax语音合成
        logger.info("\n🎤 测试Minimax语音合成...")
        try:
            synthesis_data = {
                "text": "你好，这是一个Minimax语音合成测试。",
                "voice_id": "male-qn-qingse",
                "model_id": "speech-02-hd",
                "output_format": "mp3",
                "provider": "minimax"
            }
            
            async with session.post(
                f"{base_url}/api/speech/synthesize",
                json=synthesis_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                logger.info(f"响应状态码: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    
                    if data.get('success'):
                        logger.info("✅ Minimax语音合成成功")
                        if 'audio_url' in data.get('data', {}):
                            logger.info(f"   音频URL: {data['data']['audio_url']}")
                        if 'audio_path' in data.get('data', {}):
                            logger.info(f"   音频路径: {data['data']['audio_path']}")
                    else:
                        logger.error(f"❌ Minimax语音合成失败: {data.get('error')}")
                else:
                    error_text = await response.text()
                    logger.error(f"❌ API请求失败，状态码: {response.status}")
                    logger.error(f"   错误详情: {error_text}")
        except Exception as e:
            logger.error(f"❌ Minimax语音合成时发生异常: {e}")
        
        # 3. 测试ElevenLabs语音合成
        logger.info("\n🎤 测试ElevenLabs语音合成...")
        try:
            synthesis_data = {
                "text": "Hello, this is an ElevenLabs speech synthesis test.",
                "voice_id": "JBFqnCBsd6RMkjVDRZzb",
                "model_id": "eleven_multilingual_v2",
                "output_format": "mp3_44100_128",
                "provider": "elevenlabs"
            }
            
            async with session.post(
                f"{base_url}/api/speech/synthesize",
                json=synthesis_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                logger.info(f"响应状态码: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    
                    if data.get('success'):
                        logger.info("✅ ElevenLabs语音合成成功")
                        if 'audio_url' in data.get('data', {}):
                            logger.info(f"   音频URL: {data['data']['audio_url']}")
                        if 'audio_path' in data.get('data', {}):
                            logger.info(f"   音频路径: {data['data']['audio_path']}")
                    else:
                        logger.error(f"❌ ElevenLabs语音合成失败: {data.get('error')}")
                else:
                    error_text = await response.text()
                    logger.error(f"❌ API请求失败，状态码: {response.status}")
                    logger.error(f"   错误详情: {error_text}")
        except Exception as e:
            logger.error(f"❌ ElevenLabs语音合成时发生异常: {e}")

if __name__ == "__main__":
    asyncio.run(test_speech_integration())

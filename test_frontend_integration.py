#!/usr/bin/env python3
"""
前端集成测试脚本
"""

import requests
import re
from pathlib import Path

def test_static_files():
    """测试静态文件加载"""
    print('📁 静态文件测试:')
    
    static_files = [
        '/static/js/modules/universal-service-integration.js',
        '/static/js/modules/speech-synthesis.js',
        '/static/css/style.css',
        '/static/js/app.js'
    ]
    
    for file_path in static_files:
        try:
            response = requests.get(f'http://127.0.0.1:8000{file_path}', timeout=5)
            status = "✅" if response.status_code == 200 else "❌"
            print(f'  {status} {file_path}: {response.status_code}')
        except Exception as e:
            print(f'  ❌ {file_path}: {str(e)}')

def test_html_template():
    """测试HTML模板"""
    print('\n🌐 HTML模板测试:')
    
    try:
        response = requests.get('http://127.0.0.1:8000/', timeout=5)
        if response.status_code == 200:
            html_content = response.text
            
            # 检查关键元素
            checks = [
                ('universal-service-integration.js', 'universal-service-integration.js' in html_content),
                ('语音合成选择器', 'speech-provider' in html_content),
                ('图像生成选择器', 'image-provider' in html_content),
                ('视频生成选择器', 'video-provider' in html_content),
                ('文本生成选择器', 'generation-provider' in html_content),
                ('旧模块已移除', 'doubao-integration.js' not in html_content and 'vidu-integration.js' not in html_content)
            ]
            
            for check_name, result in checks:
                status = "✅" if result else "❌"
                print(f'  {status} {check_name}')
                
            print(f'  ✅ HTML模板加载: {response.status_code}')
        else:
            print(f'  ❌ HTML模板加载: {response.status_code}')
    except Exception as e:
        print(f'  ❌ HTML模板测试: {str(e)}')

def test_config_accessibility():
    """测试配置文件可访问性"""
    print('\n⚙️ 配置文件测试:')
    
    # 检查配置文件是否存在
    config_file = Path('config/unified_services.json')
    if config_file.exists():
        print('  ✅ unified_services.json 文件存在')
        
        # 检查配置内容
        try:
            import json
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            required_keys = ['version', 'services', 'global_settings']
            for key in required_keys:
                status = "✅" if key in config else "❌"
                print(f'  {status} 配置包含 {key}')
            
            services_count = len(config.get('services', {}))
            print(f'  ✅ 配置了 {services_count} 个服务')
            
        except Exception as e:
            print(f'  ❌ 配置文件解析错误: {str(e)}')
    else:
        print('  ❌ unified_services.json 文件不存在')

def main():
    print('🧪 DaVinci AI Co-pilot Pro 前端集成测试')
    print('=' * 50)
    
    test_static_files()
    test_html_template()
    test_config_accessibility()
    
    print('\n' + '=' * 50)
    print('✅ 前端集成测试完成')

if __name__ == '__main__':
    main()

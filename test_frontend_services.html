<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试前端服务显示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 8px;
            background-color: #2a2a2a;
        }
        .section h3 {
            margin-top: 0;
            color: #4CAF50;
        }
        select {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            background-color: #333;
            color: #fff;
            border: 1px solid #555;
            border-radius: 4px;
        }
        .info {
            background-color: #333;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 前端服务显示测试</h1>
        
        <div class="section">
            <h3>📝 文本生成服务</h3>
            <select id="generation-provider">
                <option value="">选择提供商...</option>
            </select>
            <div class="info" id="generation-info">等待加载...</div>
        </div>
        
        <div class="section">
            <h3>📊 文本分析服务</h3>
            <select id="analysis-provider">
                <option value="">选择提供商...</option>
            </select>
            <div class="info" id="analysis-info">等待加载...</div>
        </div>
        
        <div class="section">
            <h3>🌐 翻译服务</h3>
            <select id="translation-provider">
                <option value="">选择提供商...</option>
            </select>
            <div class="info" id="translation-info">等待加载...</div>
        </div>
        
        <div class="section">
            <h3>🎤 语音克隆服务</h3>
            <select id="clone-provider">
                <option value="">选择提供商...</option>
            </select>
            <div class="info" id="clone-info">等待加载...</div>
        </div>
        
        <div class="section">
            <h3>🔧 控制面板</h3>
            <button onclick="loadServices()">重新加载服务</button>
            <button onclick="clearCache()">清除缓存</button>
            <button onclick="showRawData()">显示原始数据</button>
        </div>
        
        <div class="section">
            <h3>📋 原始数据</h3>
            <div class="info" id="raw-data">点击"显示原始数据"按钮查看...</div>
        </div>
    </div>

    <script>
        let servicesData = null;
        
        async function loadServices() {
            try {
                console.log('🔄 开始加载服务数据...');
                
                // 获取服务能力数据
                const response = await fetch('/api/services/capabilities');
                const data = await response.json();
                
                if (data.success) {
                    servicesData = data.data;
                    console.log('✅ 服务数据加载成功:', servicesData);
                    
                    // 更新各个选择器
                    updateSelector('generation-provider', servicesData.text_generation || [], 'generation-info');
                    updateSelector('analysis-provider', servicesData.text_analysis || [], 'analysis-info');
                    updateSelector('translation-provider', servicesData.translation || [], 'translation-info');
                    updateSelector('clone-provider', servicesData.voice_cloning || [], 'clone-info');
                } else {
                    console.error('❌ 服务数据加载失败:', data.error);
                }
            } catch (error) {
                console.error('❌ 加载服务时发生错误:', error);
            }
        }
        
        function updateSelector(selectorId, services, infoId) {
            const selector = document.getElementById(selectorId);
            const info = document.getElementById(infoId);
            
            // 清空现有选项（保留默认选项）
            selector.innerHTML = '<option value="">选择提供商...</option>';
            
            // 添加服务选项
            services.forEach(service => {
                const option = document.createElement('option');
                option.value = service;
                option.textContent = service;
                selector.appendChild(option);
            });
            
            // 更新信息显示
            info.textContent = `可用服务: ${services.length}个 - [${services.join(', ')}]`;
            
            // 检查重复项
            const uniqueServices = [...new Set(services)];
            if (uniqueServices.length !== services.length) {
                info.textContent += ` ⚠️ 发现重复项！原始: ${services.length}, 去重后: ${uniqueServices.length}`;
                info.style.color = '#ff6b6b';
            } else {
                info.style.color = '#4CAF50';
            }
        }
        
        function clearCache() {
            // 清除浏览器缓存
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => {
                        caches.delete(name);
                    });
                });
            }
            
            // 重新加载页面
            location.reload(true);
        }
        
        function showRawData() {
            const rawDataDiv = document.getElementById('raw-data');
            if (servicesData) {
                rawDataDiv.innerHTML = '<pre>' + JSON.stringify(servicesData, null, 2) + '</pre>';
            } else {
                rawDataDiv.textContent = '没有数据，请先加载服务';
            }
        }
        
        // 页面加载时自动加载服务
        document.addEventListener('DOMContentLoaded', loadServices);
    </script>
</body>
</html>

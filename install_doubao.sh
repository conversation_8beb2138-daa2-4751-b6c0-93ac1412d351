#!/bin/bash

echo "🚀 安装豆包MCP服务器..."

# 检查uv是否安装
if ! command -v uv &> /dev/null; then
    echo "❌ uv未安装，请先安装uv"
    echo "安装命令: curl -LsSf https://astral.sh/uv/install.sh | sh"
    exit 1
fi

# 安装豆包MCP服务器
echo "📦 安装doubao-mcp-server..."
uv tool install git+https://github.com/wwwzhouhui/doubao_mcp_server.git

# 检查安装是否成功
if uv tool list | grep -q "doubao-mcp-server"; then
    echo "✅ 豆包MCP服务器安装成功"
else
    echo "❌ 豆包MCP服务器安装失败"
    exit 1
fi

echo "🎉 豆包MCP服务器安装完成！"
echo ""
echo "📝 接下来请配置API密钥："
echo "1. 访问 https://console.volcengine.com/ark/"
echo "2. 创建应用并获取API密钥"
echo "3. 在config/config.json中添加豆包配置"
